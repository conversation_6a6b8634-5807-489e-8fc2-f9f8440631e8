### idc机房的mongodb使用
+ 对应阿里云的5个库,找李晓伟开启日志同步，会自动同步到线上供业务使用，使用方法如下：
```
   // 获取template
   MongoTemplate template = BaseMongoTemplate.getInstance(BaseMongoEnum.PROD_NEWS_DB);

   // 枚举有如下类型:
   PROD_RISK_DB("prod_risk_db"),
   PROD_NEWS_DB("prod_news_db"),
   PROD_IPRIGHT_DB("prod_ipright_db"),
   PROD_FINANCIAL_DB("prod_financial_db"),
   PROD_ENTERPRISE_DB("prod_enterprise_db");
    
    // 配置
    mongodb://prod_risk_db_write:<EMAIL>:20000/prod_risk_db
    mongodb://prod_news_db_writer:<EMAIL>:20000/prod_news_db
    mongodb://prod_ipright_db_writer:%<EMAIL>:20000/prod_ipright_db
    mongodb://prod_financial_db_write:<EMAIL>:20000/prod_financial_db
    mongodb://prod_enterprise_db_writer:<EMAIL>:20000/prod_enterprise_db
```

+ 调用mongodb的方法
  ```
     MongodbUtil.getSingleInstance().upsert(BaseMongoEnum.PROD_RISK_DB,list,MLimitConsumer.class);
  ```
  
+ 存放爬虫数据的base_clean_data,使用方法如下：
```
   // 调用公共方法存就行，不需要自己写方法存
   // 配置
    mongodb://base_mongo_user:mongo_qcc_user123!@mongos.ld-hadoop.com:20000/base_clean_data
```
### 阿里云的mongodb使用
+ QccData的使用方法:
```
private static QccDataMongoTemplate qccDataMongoTemplate;
    static {
        qccDataMongoTemplate = ApplicationCoreContextManager.getInstance(QccDataMongoTemplate.class);
    }
// 配置
mongodb://data_user:d967#<EMAIL>:3717,dds-bp18582e838f06542.mongodb.rds.aliyuncs.com:3717/QccData
```

+ EciData的使用方法:
```
private static EciDataMongoTemplate eciDataMongoTemplate;
    static {
        eciDataMongoTemplate = ApplicationCoreContextManager.getInstance(EciDataMongoTemplate.class);
    }
//  配置
mongodb://eci_user:d967#<EMAIL>:3717,dds-bp1116a6e1d313842.mongodb.rds.aliyuncs.com:3717/ECIData
```

+ 新的mongo集群prod库和search的只读账号
  mongodb-dts-03.ld-hadoop.com:30000
  账号：idc_readonly
  密码：x0Ji6NXZ6tJDMF2aZPQL
  认证库选择：admin