### 版本升级记录
  
##### base.core.`1.0.45` 和 ld-multiple-db：`1.0.32`
+ ld-multiple-db：`1.0.32 `
  + 读取数据库配置： 由原先的从配置文件读取，改成从nacos读取，并使用`guava cache`对配置进行缓存。
  +  由于使用了nacos,`application.properties`需要改为`base-application.properties`。否则读取文件内容时会报空指针。
+ base.core.`1.0.45`
  + OssManager重命名为：OssUtil，自己不要再创建OSSClient。调用静态方法时，传入nacosId即可。
  + nacosId的命名规则: `AccessKeyId.bucketName`,注意中间用.(英文句号)连接。
  + nacosId需要在系统中添加，使用前需要先联系赵勇添加OSS的相关配置
  
##### base.core.`1.0.46`
+ CommonService添加爬虫数据不符合规则数据的公共方法
```
/**
     * 推送不符合规则的数据
     * @param topicName kafka队列名称
     * @param jsonVal 完整的json数据
     * @param spiderId 爬虫id
     * @return
     */
    boolean pushInvalidData(String topicName,String jsonVal,String spiderId);
```  
##### base.core.`1.0.47` 
+ 审计平台推送的`kafka`数据保存以及查询。用途：数据清洗以人工数据为准的逻辑判断
```
/**
 * 审计平台审核操作业务
 * <AUTHOR>
 * @date 2020年7月22日 16点46分
 */
public interface DapAuditService {

    /**
     * 审计记录插入表中
     * @param id 主键id
     * @param list dap推送的kafk数据
     * @return
     */
    boolean insertBatch(String id,List<DapAuditRecordsDto> list);

    /**
     * 查询是否存在审核记录,返回update时间最大的记录
     * @param id 数据的主键id
     * @param tableNmae 表名（用于区分维度）
     * @return
     */
    DapAuditRecords getListByJonitIndex(String id, String tableNmae);
}
```

##### base.core.`1.0.48` 
+ bean属性的copy，在人工采编数据部分字段更新的功能非常有用
`CopyBeanUtil.merge(M target, M destination);`

```
/**
     * 把destination中的属性copy到target中为null 或者为 "" 属性中
     * @param target
     * @param destination
     * @param <M>
     * @throws Exception
     */
    public static <M> void merge(M target, M destination) throws Exception{
    ……
    }

使用示例：
  @Data
    @AllArgsConstructor
    @NoArgsConstructor
    private static class FileTest{
        private String test1;
        private String test12;
    }

    public static void main(String[] args) throws  Exception{
        FileTest test1 = new FileTest("test1","");
        FileTest test2 = new FileTest("test2","test3");

        System.out.println(test1);
        merge(test1,test2);
        System.out.println(test1);
    }
```

##### base.core.`1.0.51` 
+ 添加邮箱有效性判断 CommonUtil.isValidEmailAddress(contact.getEmail()
+ 获取有效的电话号码  commonService.getValidTelList(m, phoneCode, areaCodeList)

+ 更改查询是否存在审核记录的逻辑，把所有的审核记录合并后返回，防止人工修改的数据丢失
```
/**
 * 审计平台审核操作业务
 * <AUTHOR>
 * @date 2020年7月22日 16点46分
 */
public interface DapAuditService {

    /**
     * 查询是否存在审核记录,返回update时间最大的记录
     * @param id 数据的主键id
     * @param tableNmae 表名（用于区分维度）
     * @return
     */
    DapAuditRecords getListByJonitIndex(String id, String tableNmae);
}
```
 
-----------------------------------------------
#### 框架核心版本从1.0阶段进入1.1阶段，此次对核心功能动态切换数据源，dao层依赖注入方式做了重大变更。变更原因：发布到yarn上项目启动太慢。

##### base.core.`1.1.0` 
对业务代码的影响：
1. 项目中的`ApplicationContextManager`需要删除，统一使用core-service中的`ApplicationCoreContextManager`，变更原因：`ApplicationContextManager`本应是核心服务的一部分，之前希望每个模块中各自依赖注入service，service已经被process取代。同时该部分内容做了比较大的变更，所以放到core中统一管理。
2. core-service的dao层代码包名由`com.ld.clean`改为`com.ld.clean.core`，变更原因：之前多次出现业务代码中的dao层代码和核心业务逻辑中重复的问题，同时为了区分core和普通业务dao层的依赖注入。
+ 此次为了性能做了比较大的调整，每个模块希望各自负责人自己调整以上两点。
+ 修改前后性能对比：修改前：加载到容器大概需要8分钟左右；修改后：加载到容器内部只需要15秒左右。

##### base.core.`1.1.2` 
+ 风险维度增加根据主键+姓名获取人员keyNo方法,PersonService.getPersonInfoByName

##### base.core.`1.1.3` 
+ 添加BaseMongoTemplate,使用方法如下：
```
   // 获取template
   MongoTemplate template = BaseMongoTemplate.getInstance(BaseMongoEnum.PROD_NEWS_DB);

   // 枚举有如下类型:
   PROD_RISK_DB("prod_risk_db"),
   PROD_NEWS_DB("prod_news_db"),
   PROD_IPRIGHT_DB("prod_ipright_db"),
   PROD_FINANCIAL_DB("prod_financial_db"),
   PROD_ENTERPRISE_DB("prod_enterprise_db");
```
##### base.core.`1.1.7` 
+ 添加`MongodbUtil`,主要用于保存和查询爬虫数据，可用于数据核验，爬虫数据重复推送等，用法如下：
```
1. 所有维度在接收爬虫数据时，应做mongodb的保存操作
   — 默认mongodb的collectionName使用爬虫的kafka-topicName，不需要传输，封装的方法会自动拿配置文件中的"spider.metrics.topic"属性值
   — 补充全量数据找何强推送，后期dmp提供该功能
   — 添加完之后找赵勇添加collection分片

   保存爬虫数据的方法（可以参考限制出境spiderMap）：
   MongodbUtil.saveSpiderObj(MongodbSpiderEntity.builder().id("爬虫主键id").value("爬虫推送过来的jsonString").build());

2. 查询爬虫数据的方法:
   MongodbUtil.querySpiderList(List<String>,"collectionName");
```

##### base.core.`1.1.13` 
+ 添加`SpellUtil`,汉字转拼音，用法如下：
```
SpellUtil.getUpEnName("李宇春")
```

##### base.core.`1.1.15` 
+ 为了解决countAllWindow可能会产生数据延时以及timeAllWindow高峰时单批次过大的问题，添加`CountWithTimeoutTrigger`和`GlobalWindowTrigger`
```
keyBy的值设置方法：
1.有字段可以均匀分布，直接用该字段即可
2.如果没有均匀分布的字段，可以对主键id取hash值，然后和任务并行度取余，id.hashCode()%并行度数
CountWithTimeoutTrigger的用法：
DataStream<List<CleanSourceEntity<EndExecuteCaseSpider>>> dataStream = cleanSourceEntitySingleOutputStreamOperator.keyBy(x->x.getCleanSourceEnum().name())
                .timeWindow(Time.seconds(10L))
                .trigger(new CountWithTimeoutTrigger<>(100, TimeCharacteristic.ProcessingTime))
                .process(new CleanBatchWindowProcess());
```

##### base.core.`1.1.18` 
重启策略改成无限重启
##### base.core.`1.1.19`
KafkaHelper添加如下方法
```
    public static <T> FlinkKafkaConsumer010<T> buildKafkaSource(String topic,String groupId,Properties properties, DeserializationSchema<T> valueDeserializer) {

    }
```
##### base.core.`1.1.24`
MongodbUtil添加常用操作方法：
```
// 根据主键id查询
 MEndCase mEndCase = (MEndCase) MongodbUtil.getSingleInstance().selectById(BaseMongoEnum.PROD_RISK_DB,"001c06f124213407c4a86b349ca25a47", MEndCase.class);
// 批量更新
 public void upsert(BaseMongoEnum baseMongoEnum, List<T> list,Class<T> clazz)
……
```
##### base.core.`1.1.24`
```
personService.getPersonIdByNameAndOrgNo("朱玉松","4114251977****723x")
添加redis缓存以及x兼容X的情况
```
##### base.core.`base.core.1.1.39`
```
MongodbUtil 更改upsert方法
```

##### base.core.`base.core.1.1.61`  人工字段审核
```
/**
 * 审计平台审核操作业务
 * <AUTHOR>
 * @date 第一版：2020年7月22日 16点46分, 第二版：2020年12月22日 14点44分
 */
public interface DapAuditService {

    /**
     * 1.插入审核记录表
     * 2.插入爬虫重新刷新队列表，功能：人工修改后的数据，通知爬虫不断刷新
     * @param cleanBaseTableId   清洗基础表主键id
     * @param list dap推送的kafka数据
     * @param spiderTopicName  爬虫的topicName（新增）
     * @param spiderIdArray  爬虫的主键id,可以多值（新增）
     * @return
     */
    boolean insertBatch(String cleanBaseTableId, List<DapAuditRecordsDto> list,String spiderTopicName, String... spiderIdArray);

    /**
     * 1.查询是否存在基础表审核记录，如果存在，返回update时间最大的记录
     * 2.查询是否存在基础表审核记录，如果存在，根据artificial_edit_log判断满足“爬虫最新值<>人工值 and 爬虫最新值<> 爬虫历史值”，则插入审核表handle_edit_audit_data
     * @param cleanBaseTableId   清洗基础表主键id
     * @param cleanBaseTableName 清洗基础表名（用于区分维度）
     * @param cleanBaseTableJsonString 清洗基础表数据的json字符串（用于判断是否往审核表中插入数据）（新增）
     * @return
     */
    DapAuditRecords getListByJonitIndex(String cleanBaseTableId, String cleanBaseTableName,String cleanBaseTableJsonString);
}
```
