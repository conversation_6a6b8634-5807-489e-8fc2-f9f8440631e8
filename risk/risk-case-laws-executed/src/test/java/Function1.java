import com.base.clean.utils.BaseRegexUtil;

import java.util.HashSet;
import java.util.Set;

/**
 * <AUTHOR>
 * @Date 2022/11/29 20:16
 **/
public class Function1 {
    public static void main(String[] args) {
        System.out.println(BaseRegexUtil.handlerIdCard("350321197212121913/350322197711121025"));
    }

    /**
     * 1、给定一个未排序的整数数组 nums ，找出数字连续的最长序列（不要求序列元素在原数组中连续）的长度。
     *
     * @param nums
     * @return
     */
    public static int Function1(int[] nums) {
        if (nums == null || nums.length == 0) {
            return 0;
        }
        Set<Integer> set = new HashSet<>();
        int result = 0;
        for (int num : nums) {
            set.add(num);
        }
        for (int num : nums) {
            int len = 1;
            if (!set.contains(num - 1)) {
                for (int i = 1; set.contains(num + i); i++) {
                    len++;
                }
            }
            result = Math.max(result, len);
        }
        return result;
    }
}
