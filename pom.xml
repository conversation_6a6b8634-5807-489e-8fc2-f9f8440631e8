<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.ld.clean</groupId>
    <artifactId>ld-base-data</artifactId>
    <version>1.0.0</version>
    <packaging>pom</packaging>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <java.version>1.8</java.version>
        <spring-cloud.version>Finchley.RELEASE</spring-cloud.version>
        <spring-boot.version>2.0.1.RELEASE</spring-boot.version>
        <mybatis.spring.boot.starter.version>1.3.2</mybatis.spring.boot.starter.version>
        <today.belief.version>1.0.0</today.belief.version>
        <alibaba.druid.version>1.1.18</alibaba.druid.version>
        <alibaba.fastjson.version>1.2.83</alibaba.fastjson.version>
        <spring-kafka.version>2.1.4.RELEASE</spring-kafka.version>
        <amqp.version>1.5.9.RELEASE</amqp.version>
        <xxl-job-core.version>0.0.1</xxl-job-core.version>
        <org.projectlombok.version>1.16.20</org.projectlombok.version>
        <guava.version>27.1-jre</guava.version>
        <sqljdbc4.version>4.0</sqljdbc4.version>
        <maven.plugin.version>2.4.1</maven.plugin.version>
        <maven.compiler.source>1.8</maven.compiler.source>
        <maven.compiler.target>1.8</maven.compiler.target>
        <maven.plugin.version>2.4.1</maven.plugin.version>
        <scala.binary.version>2.11</scala.binary.version>
        <flink.version>1.6.3</flink.version>
        <flink11.version>1.11.2</flink11.version>
        <!-- flink1.14.4的版本 -->
        <flink14.version>1.14.4</flink14.version>
        <flink14.kafka.version>base.1.14.4</flink14.kafka.version>
        <!-- 公共服务的版本 -->
        <ld.core.version>base.core.1.9.50</ld.core.version>
        <ld.dbcore.version>base.1.4.7</ld.dbcore.version>
        <mysql8.connector.version>8.0.11</mysql8.connector.version>
        <mysql5.connector.version>5.1.38</mysql5.connector.version>
        <!-- flink1.18.1的版本 -->
        <flink18.version>1.18.1</flink18.version>
        <flink18.kafka.version>qcc.3.1.6</flink18.kafka.version>
        <flink18.elasticsearch7.version>3.0.1-1.17</flink18.elasticsearch7.version>
        <!-- dao层的版本管理 -->
        <common.dao.version>common.dao.1.0.7</common.dao.version>
        <engineering.dao.version>engineering.dao.1.0.0</engineering.dao.version>
        <enterprise.dao.version>enterprise.dao.1.0.7</enterprise.dao.version>
        <financial.dao.version>financial.dao.1.0.1</financial.dao.version>
        <globalsys.dao.version>globalsys.dao.1.0.1</globalsys.dao.version>
        <ipright.dao.version>ipright.dao.1.0.3</ipright.dao.version>
        <news.dao.version>news.dao.1.0.0</news.dao.version>
        <oversea.dao.version>oversea.dao.1.0.46</oversea.dao.version>
        <person.dao.version>person.dao.1.0.0</person.dao.version>
        <risk.dao.version>risk.dao.1.0.8</risk.dao.version>
    </properties>

    <!-- 开发时可以添加其他module -->
    <modules>
        <module>common</module>
        <module>rover</module>
        <module>enterprise</module>
        <module>risk</module>
        <module>financial</module>
        <module>globalsys</module>
        <module>engineering</module>
        <module>ipright</module>
        <module>news</module>
        <module>oversea</module>
        <module>person</module>
        <module>professional</module>
        <module>common/common-pdf-ocr</module>
        <module>common/common-bom</module>
    </modules>

    <dependencyManagement>
        <dependencies>
            <!-- 数据清洗基础服务:mybatis、mongodb等的封装类 -->
            <dependency>
                <groupId>com.ld.clean</groupId>
                <artifactId>qcc-base-bom</artifactId>
                <version>${ld.dbcore.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <!-- 数据清洗公共业务逻辑工具类：关联公司的keyno,dap流程，flink封装类等 -->
            <dependency>
                <groupId>com.ld.clean</groupId>
                <artifactId>ld-core-bom</artifactId>
                <version>${ld.core.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <dependency>
                <groupId>com.ld.clean</groupId>
                <artifactId>common-base-util</artifactId>
                <version>1.0.0</version>
            </dependency>

            <!-- 第三方sdk -->
            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-core</artifactId>
                <version>5.7.14</version>
            </dependency>
            <!-- http -->
            <dependency>
                <groupId>com.konghq</groupId>
                <artifactId>unirest-java</artifactId>
                <version>3.11.10</version>
            </dependency>
            <dependency>
                <groupId>org.apache.httpcomponents</groupId>
                <artifactId>httpclient</artifactId>
                <version>4.5.13</version>
            </dependency>
            <!-- redis -->
            <dependency>
                <groupId>redis.clients</groupId>
                <artifactId>jedis</artifactId>
                <version>3.3.0</version>
            </dependency>
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-lang3</artifactId>
                <version>3.6</version>
            </dependency>
            <dependency>
                <groupId>commons-lang</groupId>
                <artifactId>commons-lang</artifactId>
                <version>2.6</version>
                <scope>compile</scope>
            </dependency>
            <!-- 解析html -->
            <dependency>
                <groupId>org.jsoup</groupId>
                <artifactId>jsoup</artifactId>
                <version>1.10.2</version>
            </dependency>
            <!-- sqlserver驱动 -->
            <dependency>
                <groupId>com.microsoft.sqlserver</groupId>
                <artifactId>sqljdbc4</artifactId>
                <version>4.0</version>
            </dependency>
            <!-- 中文繁简体转化 -->
            <dependency>
                <groupId>com.github.houbb</groupId>
                <artifactId>opencc4j</artifactId>
                <version>1.8.1</version>
            </dependency>
            <!-- aliyun mns -->
            <dependency>
                <groupId>com.aliyun.mns</groupId>
                <artifactId>aliyun-sdk-mns</artifactId>
                <version>1.1.8</version>
            </dependency>
            <!-- 规则引擎 easy-rules -->
            <dependency>
                <groupId>org.jeasy</groupId>
                <artifactId>easy-rules-core</artifactId>
                <version>4.1.0</version>
            </dependency>
            <dependency>
                <groupId>org.jeasy</groupId>
                <artifactId>easy-rules-support</artifactId>
                <version>4.1.0</version>
            </dependency>
            <dependency>
                <groupId>org.jeasy</groupId>
                <artifactId>easy-rules-mvel</artifactId>
                <version>4.1.0</version>
            </dependency>
            <dependency>
                <groupId>org.slf4j</groupId>
                <artifactId>slf4j-api</artifactId>
                <version>1.7.15</version>
            </dependency>
            <dependency>
                <groupId>com.fasterxml.jackson.core</groupId>
                <artifactId>jackson-databind</artifactId>
                <version>2.10.2</version>
            </dependency>
            <!-- 自动检测文本文件的字符编码 -->
            <dependency>
                <groupId>com.googlecode.juniversalchardet</groupId>
                <artifactId>juniversalchardet</artifactId>
                <version>1.0.3</version>
            </dependency>
            <!-- Microsoft Office文档的读写 -->
            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi</artifactId>
                <version>3.10-FINAL</version>
            </dependency>
            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi-ooxml</artifactId>
                <version>3.10-FINAL</version>
            </dependency>
            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi-scratchpad</artifactId>
                <version>3.10-FINAL</version>
            </dependency>
            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi-ooxml-schemas</artifactId>
                <version>3.10-FINAL</version>
            </dependency>
            <dependency>
                <groupId>commons-beanutils</groupId>
                <artifactId>commons-beanutils</artifactId>
                <version>1.8.0</version>
            </dependency>
            <!-- flink kafka -->
            <dependency>
                <groupId>org.apache.flink</groupId>
                <artifactId>flink-connector-kafka</artifactId>
                <version>${flink18.kafka.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <distributionManagement>
        <repository>
            <id>maven-releases</id>
            <name>Nexus Release Repository</name>
            <url>http://nexus.ld-hadoop.com/repository/maven-releases/</url>
        </repository>
        <snapshotRepository>
            <id>maven-snapshots</id>
            <name>Nexus Snapshot Repository</name>
            <url>http://nexus.ld-hadoop.com/repository/maven-snapshots/</url>
        </snapshotRepository>
    </distributionManagement>

    <build>
        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-compiler-plugin</artifactId>
                    <version>3.5.1</version>
                    <configuration>
                        <source>1.8</source>
                        <target>1.8</target>
                        <encoding>UTF-8</encoding>
                    </configuration>
                </plugin>
            </plugins>
        </pluginManagement>
    </build>
</project>
