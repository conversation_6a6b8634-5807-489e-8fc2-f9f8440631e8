# 基础数据清洗
### 项目首次运行
+ 需要配置maven的[setting.xml](base_doc/settings.xml)

test
### common-dao-***的维护
##### 更改dao层的版本管理的版本
[ld-base-data/pom.xml](pom.xml)
##### 打包脚本
- common-dao

`mvn -pl common/common-dao -am clean deploy -P prod -DskipTest
`
- common-dao-engineering

`mvn -pl engineering/common-dao-engineering -am clean deploy -P prod -DskipTest
`
- common-dao-enterprise

`mvn -pl enterprise/common-dao-enterprise -am clean deploy -P prod -DskipTest
`

- common-dao-financial

`mvn -pl financial/common-dao-financial -am clean deploy -P prod -DskipTest
`

- common-dao-globalsys

`mvn -pl globalsys/common-dao-globalsys -am clean deploy -P prod -DskipTest
`

- common-dao-ipright

`mvn -pl ipright/common-dao-ipright -am clean deploy -P prod -DskipTest
`
- common-dao-news

`mvn -pl news/common-dao-news -am clean deploy -P prod -DskipTest
`
- common-dao-oversea

`mvn -pl oversea/common-dao-oversea -am clean deploy -P prod -DskipTest
`

- common-dao-person

`mvn -pl person/common-dao-person -am clean deploy -P prod -DskipTest
`

- common-dao-risk

`mvn -pl risk/common-dao-risk -am clean deploy -P prod -DskipTest
`

- 所有的一起执行

`
mvn -pl globalsys/common-dao-globalsys,enterprise/common-dao-enterprise,risk/common-dao-risk,person/common-dao-person,engineering/common-dao-engineering,financial/common-dao-financial,ipright/common-dao-ipright,news/common-dao-news,oversea/common-dao-oversea -am clean deploy -P prod -DskipTest
`
##### 版本命名规范
`版本样例：<version>engineering.dao.1.0.1</version>`
```
在POM中，版本号通常遵循语义化版本规范（Semantic Versioning），
采用三个数字组成的形式：主版本号.次版本号.修订号。
在遵循语义化版本规范的同时，我们扩展了：父模块名，功能缩写单词
```
-  父模块名：common-dao-engineering取engineering
-  功能缩写：dao层的版本，取dao
-  主版本号（Major version）：当进行不兼容的API修改时增加。
-  次版本号（Minor version）：当添加了向后兼容的功能或特性时增加。
-  修订号（Patch version）：当进行向后兼容的Bug修复或其它细微改动时增加

### 编辑单个项目的命令
##### 首先编译依赖的common项目
`cd common`  

`mvn -pl common-dao-* -am clean install -P prod -Dmaven.test.skip=true`   

 以风险为例：`common-dao-*` 改为 `common-dao-risk`
##### 编译业务的单个模块
`cd ../../risk`  

以终本案件为例：
`mvn -pl risk-stop-execute-case -am clean package -P prod -Dmaven.test.skip=true`

### 基础配置服务
+ [数据库配置](base_doc/db_config.md) 
+ [monogodb配置](base_doc/mongo_config.md) 
  
### wiki文档汇总

  + [文档汇总](https://thoughts.teambition.com/workspaces/5e82e5a4bd9a43001aeaea38/docs/5f2bccdee6eed50001308630)

### core-service版本记录
  + [core_version.md](base_doc/core_version.md) 
  
### core-service的git地址 
  +  [http://gitlab.greatld.com:14444/root/ldcleancore.git](http://gitlab.greatld.com:14444/root/ldcleancore.git)

### IDC-Aliyun DTS服务管理
+ http://doc.greatld.com/pages/viewpage.action?pageId=5710928

#### 常用工具类
```
常用工具类,具体用法查看官网文档
```
|模块     |介绍  |
|:-----------:|:---|
| hutool-core | 包括Bean操作、日期、各种Util等，原则上其他hutool的包基本使用不上    |
| guava| 包含集合、缓存、字符串、数学运算、反射、I/O等|
| common-lang3| 包括StringUtils、WordUtils、SystemUtils、ArrayUtils、ObjectUtils等|
| unirest | http的get和post请求 |
| FastJson | json的各种操作 |
| MnsManager | mns的推送 |
| MongodbUtil | mongodb的常用增删改查 |
| OssUtil | oss的上传和下载 | 
| XMathUtil | 常用的数运算 | 
| MD5Util | md5的工具类 建议使用encode(String... content)|
| DateUtil | 常用的日期处理方法 |
| CleanMonitor | 推送钉钉消息的工具类，不要自己调用钉钉推送的接口 |
| RetryUtil | 重试方法，基于guava-retrying封装，当然也可以直接使用guava-retrying |
| SpellUtil | 汉字转拼音 | 
| CommonUtil | 简繁体转换、半全角转换、判断类（公司keyno/是否是人名/统一社会信用代码/企业注册号/是否包含中文英文/）、移除中文和前缀等|
| Log | 日志打印，会自动采集到elk |
| DynamicDatasource | JdbcTemplate的封装类，可以切换不同数据源 |


