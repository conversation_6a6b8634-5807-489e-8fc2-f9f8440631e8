package com.ld.clean.beans;

import com.alibaba.fastjson.JSONArray;
import lombok.Data;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import java.util.Date;

/**
 * <AUTHOR>
 * @Date: 2019/2/18 19:10
 * @Description: mongo中的公司信息
 */
@Data
public class MongoCompany {
    private String id;
    /**
     * 法人姓名
     */
    private String corporation = "";
    private String name;
    private String province;
    private String city;
    private String industryCode = "";
    private String subIndustryCode = "";
    private String middleIndustryCode = "";
    private String smallIndustryCode = "";
    private String imageUrl = "";
    private String webSite = "";
    private Date startTime;

    @Data
    public static class ContactInfo {
        @Field("WebSite")
        private JSONArray webSite;
        @Field("PhoneNumber")
        private String phoneNumber;
        @Field("Email")
        private String email;
    }


    @Data
    @Document(collection = "Qcc_Company")
    public static class Company {
        @Field("_id")
        private String id;
        @Field("Name")
        private String name;
        @Field("Province")
        private String province;
        @Field("StartDate")
        private Long startTime;
        @Field("Oper")
        private Corporation corporation = new Corporation();
        @Field("ImageUrl")
        private String imageUrl;
        @Field("ContactInfo")
        private ContactInfo contactInfo = new ContactInfo();

        @Data
        public class Corporation {
            @Field("Name")
            private String name = "";
            @Field("KeyNo")
            private String cer = "";
        }

        @Field("Area")
        private Area area = new Area();

        @Data
        public class Area {
            @Field("Province")
            private String province = "";
            @Field("City")
            private String city = "";
            @Field("County")
            private String county;
        }

        @Field("IndustryV3")
        private Industry industry = new Industry();

        @Data
        public static class Industry {
            @Field("IndustryCode")
            private String industryCode = "";
            @Field("SubIndustryCode")
            private String subIndustryCode = "";
            @Field("MiddleCategoryCode")
            private String middleIndustryCode = "";
            @Field("SmallCategoryCode")
            private String smallIndustryCode = "";
        }
    }

    @Data
    @Document(collection = "Qcc_HKCompany")
    public class HCompany {
        @Field("_id")
        private String id;
        @Field("OperName")
        private String corporation = "";
        @Field("CompanyName")
        private String name;
        @Field("StartDate")
        private String startTime;
        @Field("ImageUrl")
        private String imageUrl;
        @Field("ContactInfo")
        private ContactInfo contactInfo = new ContactInfo();
    }

    @Data
    @Document(collection = "Qcc_TWCompany")
    public class TCompany {
        @Field("_id")
        private String id;
        @Field("OperName")
        private String corporation = "";
        @Field("CompanyName")
        private String name;
        @Field("CheckStudioDate")
        private Long startTime;
        @Field("ImageUrl")
        private String imageUrl;
        @Field("ContactInfo")
        private ContactInfo contactInfo = new ContactInfo();
    }
}
