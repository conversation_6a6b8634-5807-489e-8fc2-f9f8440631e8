package com.ld.clean.constants;

/**
 * 常量类
 *
 * <AUTHOR>
 */
public class MyConstants {

    /**
     * 线程数
     */
    public static final String CORE_POOL_SIZE = "core.pool.size";
    public static final int CORE_POOL_SIZE_DEFAULT = 20;

    /**
     * 并行度
     */
    public static final String SPIDER_PROCESS_PARALLELISM = "spider.process.parallelism";
    public static final String CLEAN_PROCESS_PARALLELISM = "clean.process.parallelism";

    /**
     * time out 时间
     */
    public static final int TIME_OUT = 5000;

    public static final String UPDATE = "UPDATE";
    public static final String INSERT = "INSERT";
    public static final String DELETE = "DELETE";


    /**
     * 人员头像图片地址
     * "https://image.qcc.com/person/" + "p07ae254dd38c0335aefb98fede2ab94" + ".jpg"
     */
    public static final String PERSON_LOGO = "https://image.qcc.com/person/";
    /**
     * 产品LOGO图片地址
     */
    public static final String QCC_IMAGE = "https://image.qcc.com/";
    public static final String BUCKET_NAME = "qcc-image";
    public static final String LOGO_SUFFIX_JPG = ".jpg";
    public static final String PRODUCT_KEY = "product/";
    public static final String COMPANY_KEY = "logo/";

    /**
     * 港股上退市公告转存地址
     */
    public static String BUCKET_NAME_CLEAN = "qcc-data";
    public static String CLEAN_FOLDER = "CtFinancialNotice/";

    /**
     * 上市公告pdf地址
     */
    public static final String IPO_REPORT_DATA_PDF_PATH = "https://qccdata.qichacha.com/ReportData/PDF/";
    public static final String IPO_HK_SK_REPORT_DATA_PDF_PATH = "https://qccdata.qichacha.com/ReportData/PDF/ExNewsHK/";
    public static final String REPORT_SUFFIX_PDF = ".pdf";
    public static final String IPO_HK_SK_REPORT_DATA_TXT_PATH = "https://qccdata.qichacha.com/CtFinancialNotice/";
    public static final String REPORT_SUFFIX_TXT = ".txt";

    /**
     * 未转化为内链的新闻的钉钉消息通知token
     */
//    https://oapi.dingtalk.com/robot/send?access_token=19f037867cd9e2780525bd4bce7cc51982d9066e5f6e036139dd1827f38a98f2
    public static final String DING_TALK_UNCONNECT_NEWS_ACCESS_TOKEN = "19f037867cd9e2780525bd4bce7cc51982d9066e5f6e036139dd1827f38a98f2";

    /**
     * 产品名重复的钉钉消息通知token
     */
    public static final String DING_TALK_DUOLICATE_PRODUCT_ACCESS_TOKEN = "78648dc02fcd7964956601dfa11e7fc216f026a029855d4cfb61050e0f2f40ce";

    /**
     * IPO推出事件	topic
     */
    public static final String DAP_KAFKA_PRODUCT_EVENT_IPO_OUT_TOPIC = "base_enterprise_product_envent_ipo_out";
    public static final String DAP_KAFKA_PRODUCT_EVENT_IPO_OUT_GROUPID = "group_base_product_event_ipo_history_baseinfo_20230706";
    public static final String DAP_KAFKA_PRODUCT_EVENT_EXIT_OUT_GROUPID = "group_base_product_event_ipo_eixt_baseinfo_20230706";
    public static final String DAP_KAFKA_PRODUCT_EVENT_PLACE_FINANCE_GROUPID = "group_base_product_event_place_finance_20230711";
    public static final String DAP_KAFKA_PRODUCT_EVENT_HOLDER_LIMIT_GROUPID = "group_base_product_event_limit_holder_20230711";
    public static final String DAP_KAFKA_PRODUCT_EVENT_IPO_OUT_BUSINESS_TOPIC = "dap_ep_product_exit_event_bussiness";
    public static final String DAP_KAFKA_PRODUCT_EVENT_IPO_OUT_BUSINESS_GROUPID = "group_dap_ep_exit_evevt_sync_20230727";

    /**
     * 产品别名topic
     */
    public static final String DAP_KAFKA_PRODUCT_BASEINFO_NEXT_TOPIC = "base_ct_product_baseinfo_down_sream_data";
    public static final String DAP_KAFKA_PRODUCT_BASEINFO_NEXT_GROUPID = "group_base_product_baseinfo_alias_240910";
    /**
     * 并购事件	topic
     */
    public static final String DAP_KAFKA_PRODUCT_MA_EVENT_CVS_TOPIC = "dap_spider_financial_invest_cvsource_ma";
    public static final String DAP_KAFKA_PRODUCT_MA_EVENT_CVS_GROUPID = "group_dap_spider_comp_ma_event_cvs_20231018";

    /**
     * cvs线索表rs  topic
     */

    public static final String DAP_KAFKA_PRODUCT_MA_EVENT_CVS_RS_TOPIC = "dap_spider_financial_finance_rsfx_event";
    public static final String DAP_KAFKA_PRODUCT_MA_EVENT_CVS_RS_GROUPID = "group_dap_spider_rsfx_comp_ma_event_cvs_240425";

    /**
     * cvs线索表qk  topic
     */

    public static final String DAP_KAFKA_PRODUCT_MA_EVENT_CVS_QK_TOPIC = "dap_spider_financial_invest_qk_bgsj";
    public static final String DAP_KAFKA_PRODUCT_MA_EVENT_CVS_QK_GROUPID = "group_dap_spider_qk_comp_ma_event_cvs_241224";

    /**
     * 品牌产品成员企业基本信息表  topic
     * comp_affiliated_product
     */
    //dap group
    public static final String DAP_KAFKA_COMP_AFFILIATED_PRODUCT_GROUPID = "group_dap_dap_comp_affiliated_product_250206";
    //品牌产品具有cvc标识通知
    //public static final String DAP_KAFKA_PRODUCT_BASEINFO_TOPIC = "dap_spider_enterprise_product_baseinfo";
    //public static final String DAP_KAFKA_PRODUCT_BASEINFO_GROUPID = "group_dap_spider_product_baseinfo_20250206";

    /**
     * 品牌产品竞品	topic
     */
    public static final String DAP_KAFKA_PRODUCT_BRAND_JP_TOPIC = "dap_spider_financial_maigoo_brand";
    public static final String DAP_KAFKA_PRODUCT_BRAND_JP_GROUPID = "group_dap_spider_product_brand_maigoo_20230719";
    public static final String DAP_KAFKA_PRODUCT_BRAND_JP_TZJ_SPIDER_TOPIC = "dap_spider_financial_invest_company_tzj";
    public static final String DAP_KAFKA_PRODUCT_BRAND_JP_TZJ_SPIDER_GROUPID = "group_dap_spider_ep_brand_info_jp_20230803";
    public static final String DAP_KAFKA_PRODUCT_BRAND_JP_GROUPID1 = "group_base_ep_comp_brand_info_20230804";
    public static final String DAP_KAFKA_PRODUCT_BRAND_XN_JP_TOPIC = "dap_spider_financial_invest_cvs_track_info";
    public static final String DAP_KAFKA_PRODUCT_BRAND_XN_JP_GROUPID = "group_dap_spider_comp_track_info_xn_20231018";
    public static final String DAP_KAFKA_PRODUCT_BRAND_INFO_JP_TOPIC = "dap_spider_enterprise_maigoo_brand_info";
    public static final String DAP_KAFKA_PRODUCT_BRAND_INFO_GROUPID1 = "group_dap_spider_comp_maigoo_brand_info_20231018";

    /**
     * 融资这边通用基础topic 用于分发到各个业务队列
     */
    public static final String DAP_KAFKA_PRODUCT_COMMON_TABLES_TOPIC = "base_ep_product_common_tables";
    public static final String DAP_KAFKA_PRODUCT_COMMON_TABLES_GROUPID = "group_base_ep_product_brand_phase_info_2020809";
    public static final String DAP_KAFKA_PRODUCT_COMMON_TABLES_GROUPID1= "group_base_ep_product_common_tables_20230811";
    public static final String DAP_KAFKA_PRODUCT_ENTERPRISE_COMPKEYNO_CHANGE_TOPIC = "base_company_alias_sync";
    public static final String DAP_KAFKA_PRODUCT_ENTERPRISE_COMPKEYNO_CHANGE_GROUPID = "group_base_ep_product_baseinfo_keyno_notice_20230818";
    public static final String DAP_KAFKA_PRODUCT_MENBER_ADMIN_BUSSNEISS_GROUPID = "group_base_ep_product_member_admin_20230822";
    public static final String DAP_KAFKA_PRODUCT_INVEST_MAPPING_SYNC_BUSSNEISS_GROUPID = "group_base_invest_mapping_sync_231206";

    /**
     * 投资赛道	topic  废弃
     */
    public static final String DAP_KAFKA_PRODUCT_TRACK_INFO_DATA_TOPIC = "base_ep_ct_track_info";
    public static final String DAP_KAFKA_PRODUCT_TRACK_INFO_DATA_GROUPID = "group_base_comp_track_info_match_20231028";


    /**
     * 投资赛道	topic 新版本
     */
    public static final String DAP_KAFKA_PRODUCT_TRACK_INFO_DATA_NEW_TOPIC = "dap_spider_financial_invest_qmp_xmk";
    public static final String DAP_KAFKA_PRODUCT_TRACK_INFO_DATA_NEW_GROUPID = "group_dap_spider_comp_ct_track_info_240815";
    public static final String DAP_KAFKA_PRODUCT_TRACK_INFO_DATA_NEW_TOPIC1 = "dap_spider_financial_invest_qk_bgsj";
    public static final String DAP_KAFKA_PRODUCT_TRACK_INFO_DATA_NEW_GROUPID1 = "group_dap_spider_comp_macth_info_241021";
    public static final String DAP_KAFKA_PRODUCT_TRACK_INFO_DATA_NEW_QK_TAG_TOPIC = "dap_spider_financial_invest_qk_tag_list";
    public static final String DAP_KAFKA_PRODUCT_TRACK_INFO_DATA_NEW_QK_TAG_GROUP_ID = "group_dap_spider_comp_track_match_info_241128";


    /**
     * 融资事件竞品	topic
     */
    public static final String DAP_KAFKA_PRODUCT_EVENT_JP_TOPIC = "base_product_invest_event_jp";
    public static final String DAP_KAFKA_PRODUCT_EVENT_JP_GROUPID = "group_base_comp_invest_event_jp_20230718";

    public static final String DAP_KAFKA_PRODUCT_EVENT_JP_BRAND_INFO_TYC_TOPIC = "dap_spider_financial_invest_brand_info_tyc";
    public static final String DAP_KAFKA_PRODUCT_EVENT_JP_BRAND_INFO_TYC_GROUPID = "group_dap_spider_invest_brand_info_jp_20230801";
    public static final String DAP_KAFKA_PRODUCT_EVENT_JP_TYC_TOPIC = "dap_spider_enterprise_invest_event_tyc";
    public static final String DAP_KAFKA_PRODUCT_EVENT_JP_TYC_GROUPID = "group_dap_spider_product_event_tyc_20230718";
    public static final String DAP_KAFKA_PRODUCT_EVENT_JP_TZJ_TOPIC = "dap_spider_financial_invest_event_tzj";
    public static final String DAP_KAFKA_PRODUCT_EVENT_JP_TZJ_GROUPID = "group_dap_spider_procuct_event_tzj_20230718";
    public static final String DAP_KAFKA_PRODUCT_EVENT_JP_QMP_TOPIC = "dap_spider_financial_invest_event_qmp";
    public static final String DAP_KAFKA_PRODUCT_EVENT_JP_QMP_GROUPID= "group_dap_spider_product_event_qmp_20230718";
    public static final String DAP_KAFKA_PRODUCT_EVENT_JP_QMP_APP_TOPIC = "dap_spider_financial_invest_event_qmp_app";
    public static final String DAP_KAFKA_PRODUCT_EVENT_JP_QMP_APP_GROUPID= "group_dap_spider_product_event_qmp_app_240705";
    public static final String DAP_KAFKA_PRODUCT_EVENT_JP_CVS_TOPIC = "dap_spider_financial_invest_cvsource";
    public static final String DAP_KAFKA_PRODUCT_EVENT_JP_CVS_GROUPID= "group_dap_spider_product_event_cvs_20230718";
    public static final String DAP_KAFKA_PRODUCT_EVENT_JP_XN_TOPIC = "dap_spider_financial_invest_cvs_finance_info";
    public static final String DAP_KAFKA_PRODUCT_EVENT_JP_XN_GROUPID= "group_dap_spider_comp_invest_info_xn_20231017";


    public static final String DAP_KAFKA_PRODUCT_EVENT_JP_YJT_TOPIC = "dap_spider_financial_invest_yjt_pevc";
    public static final String DAP_KAFKA_PRODUCT_EVENT_JP_YJT_GROUPID= "group_dap_spider_comp_invest_info_yjt_20240428";

    public static final String DAP_DAP_KAFKA_ENTERPRISE_DB_TOPIC = "dap_dap_manage_enterprise_db";
    public static final String DAP_DAP_PRODUCT_EVENT_JP_GROUPID= "group_dap_dap_comp_product_event_jp_20230725";
    public static final String DAP_DAP_COMP_MA_CSV_GROUPID= "group_dap_dap_comp_ma_event_cvs_231211";
    public static final String DAP_DAP_PRODUCT_IPO_EXIT_BASEINFO_GROUPID= "group_dap_dap_ipo_exit_baseinfo_20230814";
    public static final String DAP_DAP_PRODUCT_BRAND_INFO_JP_GROUPID= "group_dap_dap_comp_brand_info_jp_20230816";
    public static final String DAP_DAP_PRODUCT_HOLDER_VALUE_GROUPID= "group_dap_dap_ipo_hold_value_history_20231007";
    public static final String FIRM_PREFIX = "https://www.qcc.com/firm/";
    public static final String PRODUCT_PREFIX = "https://www.qcc.com/product/";
    public static final String INVESTOR_PREFIX = "https://www.qcc.com/investor/";
    public static final String HTML = ".html";


}
