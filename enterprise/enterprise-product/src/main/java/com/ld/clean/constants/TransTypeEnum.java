package com.ld.clean.constants;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 并购交易类型
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum TransTypeEnum {
    OTHER("其他", -1),
    AGREE_TRANS("协议转让", 1),
    INCREASE("增资", 2),
    ISSUE_STOCK("发行股份", 3);

    private Integer code;

    private String desc;



    TransTypeEnum(String desc, Integer code) {
        this.code = code;
        this.desc = desc;
    }

    public static TransTypeEnum getByCode(int code) {
        for (TransTypeEnum roundLevelEnum : TransTypeEnum.values()) {
            if (code == roundLevelEnum.getCode()) {
                return roundLevelEnum;
            }
        }
        return OTHER;
    }

}

