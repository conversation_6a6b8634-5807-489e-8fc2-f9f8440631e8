package com.ld.clean.util;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.ld.clean.job.compreportdataagiresult.model.IpoHistoryModel;
import com.ld.clean.job.compreportdataagiresult.model.ShareHolderModel;
import com.ld.clean.job.compreportdataagiresult.model.ValidResultModel;
import com.ld.clean.model.EpCtCurrency;
import com.ld.clean.utils.CommonUtil;
import com.ld.clean.utils.CtCurrencyUtils;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
public class ReportDataUtils {
    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyyMMdd");

    public static boolean isTextOver128KB(String text) {
        if (text == null) {
            return false;
        }
        // 获取文本的字节长度（默认使用平台默认编码）
        byte[] bytes = text.getBytes();
        return bytes.length > 128 * 1024;
    }


    public static ValidResultModel checkReportShareHolderAgiResult(String text) {
        ValidResultModel resultModel = new ValidResultModel();
        HashMap<String, List<String>> msg = new HashMap<>();
       /* if(StringUtils.isBlank(text) ||"[]".equalsIgnoreCase(text)|| "\n[]\n".equalsIgnoreCase(text)){
            msg.add("模型结果result为空；");
        }else*/
        if (StringUtils.isNotBlank(text)) {
            List<ShareHolderModel> resultAnnotationList = new ArrayList<>();
            try {
                JSONArray contentArray = JSONArray.parseArray(text.replace("```json", "").replace("```", ""));
                if (contentArray != null && contentArray.size() > 0) {
                    contentArray.forEach(vv -> {
                        ShareHolderModel value = new ShareHolderModel();


                        JSONObject data = (JSONObject) vv;
                        value.setUuid(UUID.randomUUID().toString());
                        value.setChangeDate(data.getString("变更日期"));
                        value.setFinanceDateHy(data.getString("会议日期"));
                        value.setFinanceDateXy(data.getString("协议日期"));
                        value.setFinanceDateDz(data.getString("到账日期"));
                        value.setFinanceDateZm(data.getString("证明日期"));
                        value.setFinanceDateGs(data.getString("工商日期"));
                        value.setChangeBatch(data.getString("变更批次"));
                        value.setHolderNameOriginal(data.getString("股东名称"));
                        value.setHolderType(data.getString("股东类型"));
                        value.setSubCapital(data.getString("认缴出资"));
                        value.setPaidCapital(data.getString("实缴出资"));
                        value.setHoldSum(data.getString("持股数"));
                        value.setHoldRatioTotal(data.getString("持股比例"));
                        value.setIfAll(data.getString("股东是否完整"));
                        value.setContributionWay(data.getString("出资方式"));
                        value.setContributionDate(data.getString("出资时间"));


                        resultAnnotationList.add(value);


                    });
                }


                //所属批次为空
                List<String> holderNames0 = resultAnnotationList.stream().filter(vv -> StringUtils.isBlank(vv.getChangeBatch())).map(ShareHolderModel::getUuid).distinct().collect(Collectors.toList());
                if (!holderNames0.isEmpty()) {
                    msg.put("股东当前批次字段为空；", holderNames0);
                }
                //核心字段缺失

                List<String> holderNames = resultAnnotationList.stream().filter(vv -> StringUtils.isBlank(vv.getHoldSum()) && StringUtils.isBlank(vv.getPaidCapital()) && StringUtils.isBlank(vv.getSubCapital())).map(ShareHolderModel::getUuid).distinct().collect(Collectors.toList());
                if (!holderNames.isEmpty()) {
                    msg.put("股东核心字段缺失；", holderNames);
                }
                //日期字段校验
                //1.变更日期为空
                List<String> holderNames1 = resultAnnotationList.stream().filter(vv -> StringUtils.isBlank(vv.getChangeDate())).map(ShareHolderModel::getUuid).distinct().collect(Collectors.toList());
                //2.日期格式不规范
                List<String> holderNames2 = resultAnnotationList.stream().filter(vv -> !isValidYYYYMMDD(vv.getChangeDate()) || !isValidYYYYMMDD(vv.getFinanceDateDz()) || !isValidYYYYMMDD(vv.getFinanceDateGs()) || !isValidYYYYMMDD(vv.getFinanceDateHy()) || !isValidYYYYMMDD(vv.getFinanceDateXy()) || !isValidYYYYMMDD(vv.getFinanceDateZm())).map(ShareHolderModel::getUuid).distinct().collect(Collectors.toList());
                //3.变更日期不等于任意一个日期
                List<String> holderNames3 = resultAnnotationList.stream().filter(vv -> StringUtils.isNotBlank(vv.getChangeDate()) && !vv.getChangeDate().equalsIgnoreCase(vv.getFinanceDateDz()) && !vv.getChangeDate().equalsIgnoreCase(vv.getFinanceDateGs()) && !vv.getChangeDate().equalsIgnoreCase(vv.getFinanceDateHy()) && !vv.getChangeDate().equalsIgnoreCase(vv.getFinanceDateXy()) && !vv.getChangeDate().equalsIgnoreCase(vv.getFinanceDateZm())).map(ShareHolderModel::getUuid).distinct().collect(Collectors.toList());
                if (!holderNames1.isEmpty()) {
                    msg.put("股东变更日期为空；", holderNames1);
                }
                if (!holderNames2.isEmpty()) {
                    msg.put("股东日期格式不规范；", holderNames2);
                }
                if (!holderNames3.isEmpty()) {
                    msg.put("股东变更日期不等于任意日期；", holderNames3);
                }
                //金额

                List<String> holderNames4 = resultAnnotationList.stream().filter(vv -> {
                    if (StringUtils.isNotBlank(vv.getPaidCapital())) {
                        EpCtCurrency ctCurrency = CtCurrencyUtils.getEpCtCurrencyByCapital(vv.getPaidCapital());
                        if (StringUtils.isBlank(ctCurrency.getAmount()) || StringUtils.isBlank(ctCurrency.getCurrency())) {
                            return true;
                        }
                    }
                    if (StringUtils.isNotBlank(vv.getSubCapital())) {
                        EpCtCurrency ctCurrency1 = CtCurrencyUtils.getEpCtCurrencyByCapital(vv.getSubCapital());
                        if (StringUtils.isBlank(ctCurrency1.getAmount()) || StringUtils.isBlank(ctCurrency1.getCurrency())) {
                            return true;
                        }
                    }
                    return false;

                }).map(ShareHolderModel::getUuid).distinct().collect(Collectors.toList());
                if (!holderNames4.isEmpty()) {
                    msg.put("股东金额格式不规范；", holderNames4);
                }
                List<String> batchList = new ArrayList<>();
                //统一变更日期内，所有股东持股比例！=100%，该变更日期内转人工
                resultAnnotationList.stream().filter(vv -> StringUtils.isNotBlank(vv.getChangeBatch())).collect(Collectors.groupingBy(ShareHolderModel::getChangeBatch)).forEach((k, v) -> {
                    double sum = v.stream().filter(vb -> StringUtils.isNotBlank(vb.getHoldRatioTotal())).mapToDouble(vb -> {
                        return Double.valueOf(vb.getHoldRatioTotal().replace("%", "").replace("％", ""));

                    }).sum();
                    if (sum < 99.98 || sum > 100.02) {
                        // batchList.add(k);
                        batchList.addAll(v.stream().map(ShareHolderModel::getUuid).collect(Collectors.toList()));
                    }
                });

                if (!batchList.isEmpty()) {
                    msg.put("股东当前批次持股比例总和不足100%；", batchList);
                    // msg.add(batchList.stream().collect(Collectors.joining(",")) + "持股比例不是100%；");

                }

                //股东是否完整

                List<String> holderNames6 = resultAnnotationList.stream().filter(vv -> "否".equalsIgnoreCase(vv.getIfAll())).map(ShareHolderModel::getUuid).distinct().collect(Collectors.toList());
                if (!holderNames6.isEmpty()) {
                    //msg.add(holderNames6.stream().collect(Collectors.joining(",")) + "股东是否完整为 否；");
                    msg.put("股东是否完整为 否；", holderNames6);

                }

                for (ShareHolderModel v : resultAnnotationList) {
                    List<String> aduitMsg = new ArrayList<>();
                    msg.forEach((k, vList) -> {
                        if (vList.stream().anyMatch(iu -> iu.equalsIgnoreCase(v.getUuid()))) {
                            aduitMsg.add(k);
                        }
                    });
                    v.setAduitContent(String.join("\r\n", aduitMsg));
                }
            } catch (Exception e) {
                resultModel.setRemark("解析/校驗異常：" + e.getMessage());
            }

            resultModel.setShareHolderModelList(resultAnnotationList);


        }


        // }

        return resultModel;

    }


    public static ValidResultModel checkReportIpoHistoryAgiResult(String text) {
        ValidResultModel resultModel = new ValidResultModel();
        HashMap<String, List<String>> msg = new HashMap<>();
       /* if(StringUtils.isBlank(text) ||"[]".equalsIgnoreCase(text)|| "\n[]\n".equalsIgnoreCase(text)){
            msg.add("模型结果result为空；");
        }else*/
        if (StringUtils.isNotBlank(text)) {
            List<IpoHistoryModel> resultAnnotationList = new ArrayList<>();
            try {
                JSONArray contentArray = JSONArray.parseArray(text.replace("```json", "").replace("```", ""));
                if (contentArray != null && contentArray.size() > 0) {
                    contentArray.forEach(vv -> {
                        IpoHistoryModel value = new IpoHistoryModel();


                        JSONObject data = (JSONObject) vv;
                        value.setUuid(UUID.randomUUID().toString());
                        value.setHolderNameOriginal(data.getString("股东名称"));
                        value.setHolderType(data.getString("股东类型"));
                        value.setEventDate(data.getString("事件日期"));
                        value.setFinanceDateHy(data.getString("会议日期"));
                        value.setFinanceDateXy(data.getString("协议日期"));
                        value.setFinanceDateDz(data.getString("到账日期"));
                        value.setFinanceDateZm(data.getString("证明日期"));
                        value.setFinanceDateGs(data.getString("工商日期"));
                        value.setInvestmentType(data.getString("入股形式"));
                        value.setParticialType(data.getString("参与形式"));
                        value.setHolderNum(data.getString("交易股数") == null ? "" : data.getString("交易股数"));
                        value.setHoldRadio(data.getString("交易比例") == null ? "" : data.getString("交易比例"));
                        value.setAmount(data.getString("交易金额") == null ? "" : data.getString("交易金额"));
                        value.setPercentPrice(data.getString("每股价格"));
                        value.setIfAll(data.getString("是否独立"));
                        value.setRound("");


                        resultAnnotationList.add(value);


                    });
                }

                //核心字段缺失


                List<String> holderNames = resultAnnotationList.stream().filter(vv -> StringUtils.isBlank(vv.getEventDate()) && StringUtils.isBlank(vv.getHolderNameOriginal()) && StringUtils.isBlank(vv.getHolderType()) && StringUtils.isBlank(vv.getParticialType()) && StringUtils.isBlank(vv.getInvestmentType()) && StringUtils.isBlank(vv.getHolderNum() + vv.getHoldRadio() + vv.getAmount())).map(IpoHistoryModel::getUuid).distinct().collect(Collectors.toList());
                if (!holderNames.isEmpty()) {
                    msg.put("事件核心字段缺失；", holderNames);
                }
                //日期字段校验
                //1.变更日期为空
                List<String> holderNames1 = resultAnnotationList.stream().filter(vv -> StringUtils.isBlank(vv.getEventDate())).map(IpoHistoryModel::getUuid).distinct().collect(Collectors.toList());
                //2.日期格式不规范
                List<String> holderNames2 = resultAnnotationList.stream().filter(vv -> !isValidYYYYMMDD(vv.getEventDate()) || !isValidYYYYMMDD(vv.getFinanceDateDz()) || !isValidYYYYMMDD(vv.getFinanceDateGs()) || !isValidYYYYMMDD(vv.getFinanceDateHy()) || !isValidYYYYMMDD(vv.getFinanceDateXy()) || !isValidYYYYMMDD(vv.getFinanceDateZm())).map(IpoHistoryModel::getUuid).distinct().collect(Collectors.toList());
                //3.变更日期不等于任意一个日期
                List<String> holderNames3 = resultAnnotationList.stream().filter(vv -> StringUtils.isNotBlank(vv.getEventDate()) && !vv.getEventDate().equalsIgnoreCase(vv.getFinanceDateDz()) && !vv.getEventDate().equalsIgnoreCase(vv.getFinanceDateGs()) && !vv.getEventDate().equalsIgnoreCase(vv.getFinanceDateHy()) && !vv.getEventDate().equalsIgnoreCase(vv.getFinanceDateXy()) && !vv.getEventDate().equalsIgnoreCase(vv.getFinanceDateZm())).map(IpoHistoryModel::getUuid).distinct().collect(Collectors.toList());
                //4.事件日期满足优先级排序
                List<String> holderNames4 = resultAnnotationList.stream().filter(vv -> {
                    if (StringUtils.isNotBlank(vv.getFinanceDateGs()) && isValidYYYYMMDD(vv.getFinanceDateGs()) && vv.getFinanceDateGs().length() == 8 && StringUtils.isNotBlank(vv.getFinanceDateDz()) && isValidYYYYMMDD(vv.getFinanceDateDz()) && vv.getFinanceDateDz().length() == 8 && StringUtils.isNotBlank(vv.getFinanceDateXy()) && isValidYYYYMMDD(vv.getFinanceDateXy()) && vv.getFinanceDateXy().length() == 8 && StringUtils.isNotBlank(vv.getFinanceDateHy()) && isValidYYYYMMDD(vv.getFinanceDateHy()) && vv.getFinanceDateHy().length() == 8) {
                        LocalDate businessDate = LocalDate.parse(vv.getFinanceDateGs(), DATE_FORMATTER);
                        LocalDate arrivalDate = LocalDate.parse(vv.getFinanceDateDz(), DATE_FORMATTER);
                        LocalDate agreementDate = LocalDate.parse(vv.getFinanceDateXy(), DATE_FORMATTER);
                        LocalDate meetingDate = LocalDate.parse(vv.getFinanceDateHy(), DATE_FORMATTER);
                        return !(businessDate.isAfter(arrivalDate)
                                && arrivalDate.isAfter(agreementDate)
                                && agreementDate.isAfter(meetingDate));
                    }
                    return false;
                }).map(IpoHistoryModel::getUuid).distinct().collect(Collectors.toList());

                if (!holderNames1.isEmpty()) {
                    msg.put("事件变更日期为空；", holderNames1);
                }
                if (!holderNames2.isEmpty()) {
                    msg.put("事件日期格式不规范；", holderNames2);
                }
                if (!holderNames3.isEmpty()) {
                    msg.put("事件日期不等于任意日期；", holderNames3);
                }
                if (!holderNames4.isEmpty()) {
                    msg.put("事件日期不满足优先级；", holderNames3);
                }
                //金额

                List<String> holderNames5 = resultAnnotationList.stream().filter(vv -> {
                    if (StringUtils.isNotBlank(vv.getAmount())) {
                        EpCtCurrency ctCurrency = CtCurrencyUtils.getEpCtCurrencyByCapital(vv.getAmount());
                        if (StringUtils.isBlank(ctCurrency.getAmount()) || StringUtils.isBlank(ctCurrency.getCurrency())) {
                            return true;
                        }
                    }
              /*  if(StringUtils.isNotBlank(vv.getSubCapital())) {
                    EpCtCurrency ctCurrency1 = CtCurrencyUtils.getEpCtCurrencyByCapital(vv.getSubCapital());
                    if (StringUtils.isBlank(ctCurrency1.getAmount()) || StringUtils.isBlank(ctCurrency1.getCurrency())) {
                        return true;
                    }
                }*/
                    return false;

                }).map(IpoHistoryModel::getUuid).distinct().collect(Collectors.toList());
                if (!holderNames5.isEmpty()) {
                    msg.put("事件金额格式不规范；", holderNames4);
                }
                List<String> batchList = new ArrayList<>();
                List<String> batchList1 = new ArrayList<>();
                List<String> batchList2 = new ArrayList<>();
                List<String> batchList3 = new ArrayList<>();
                //统一变更日期内，所有股东持股比例！=100%，该变更日期内转人工
                resultAnnotationList.stream().filter(vv -> StringUtils.isNotBlank(vv.getEventDate())).collect(Collectors.groupingBy(IpoHistoryModel::getEventDate)).forEach((k, v) -> {
                    double sum = v.stream().filter(vb -> StringUtils.isNotBlank(vb.getHoldRadio())).mapToDouble(vb -> {
                        return Double.valueOf(vb.getHoldRadio().replace("%", "").replace("％", ""));

                    }).sum();
                    if (sum < 99.98 || sum > 100.02) {
                        // batchList.add(k);
                        batchList.addAll(v.stream().map(IpoHistoryModel::getUuid).collect(Collectors.toList()));
                    }
                    double sumAmount1 = v.stream().filter(vb -> StringUtils.isNotBlank(vb.getHoldRadio())).mapToDouble(vb -> {
                        return Double.valueOf(vb.getHoldRadio().replace("%", "").replace("％", ""));

                    }).sum();

                    double sumAmount2 = v.stream().filter(vb -> StringUtils.isNotBlank(vb.getHoldRadio())).mapToDouble(vb -> {
                        return Double.valueOf(vb.getHoldRadio().replace("%", "").replace("％", ""));

                    }).sum();

                    if (!BigDecimal.valueOf(sumAmount1).setScale(10, RoundingMode.HALF_UP).equals(BigDecimal.valueOf(sumAmount2).setScale(10, RoundingMode.HALF_UP))) {
                        batchList1.addAll(v.stream().map(IpoHistoryModel::getUuid).collect(Collectors.toList()));

                    }


                    double sumRadio1 = v.stream().filter(vb -> StringUtils.isNotBlank(vb.getHoldRadio())).mapToDouble(vb -> {
                        return Double.valueOf(vb.getHoldRadio().replace("%", "").replace("％", ""));

                    }).sum();

                    double sumRadio2 = v.stream().filter(vb -> StringUtils.isNotBlank(vb.getHoldRadio())).mapToDouble(vb -> {
                        return Double.valueOf(vb.getHoldRadio().replace("%", "").replace("％", ""));

                    }).sum();

                    if (!BigDecimal.valueOf(sumRadio1).setScale(10, RoundingMode.HALF_UP).equals(BigDecimal.valueOf(sumRadio2).setScale(10, RoundingMode.HALF_UP))) {
                        batchList2.addAll(v.stream().map(IpoHistoryModel::getUuid).collect(Collectors.toList()));

                    }

                    List<IpoHistoryModel> t = v.stream().filter(vb -> StringUtils.isNotBlank(vb.getInvestmentType()) && vb.getInvestmentType().contains("股权转让") && StringUtils.isNotBlank(vb.getParticialType()) && vb.getParticialType().contains("投资方")).collect(Collectors.toList());
                    List<IpoHistoryModel> c = v.stream().filter(vb -> StringUtils.isNotBlank(vb.getInvestmentType()) && vb.getInvestmentType().contains("股权转让") && StringUtils.isNotBlank(vb.getParticialType()) && vb.getParticialType().contains("出让方")).collect(Collectors.toList());
                    if (t.isEmpty() || c.isEmpty()) {
                        batchList3.addAll(v.stream().map(IpoHistoryModel::getUuid).collect(Collectors.toList()));

                    }
                });

                if (!batchList1.isEmpty()) {
                    msg.put("事件本轮交易金额不相等；", batchList1);
                    // msg.add(batchList.stream().collect(Collectors.joining(",")) + "持股比例不是100%；");

                }
                if (!batchList2.isEmpty()) {
                    msg.put("事件本轮交易比例不相等；", batchList2);
                    // msg.add(batchList.stream().collect(Collectors.joining(",")) + "持股比例不是100%；");

                }
                if (!batchList.isEmpty()) {
                    msg.put("事件本轮持股比例总和不足100%；", batchList);
                    // msg.add(batchList.stream().collect(Collectors.joining(",")) + "持股比例不是100%；");

                }
                if (!batchList3.isEmpty()) {
                    msg.put("事件本轮缺失投资方或出让方；", batchList3);
                    // msg.add(batchList.stream().collect(Collectors.joining(",")) + "持股比例不是100%；");

                }


                //股东是否完整

                List<String> holderNames6 = resultAnnotationList.stream().filter(vv -> "否".equalsIgnoreCase(vv.getIfAll())).map(IpoHistoryModel::getUuid).distinct().collect(Collectors.toList());
                if (!holderNames6.isEmpty()) {
                    //msg.add(holderNames6.stream().collect(Collectors.joining(",")) + "股东是否完整为 否；");
                    msg.put("股东是否完整为 否；", holderNames6);

                }

                for (IpoHistoryModel v : resultAnnotationList) {
                    List<String> aduitMsg = new ArrayList<>();
                    msg.forEach((k, vList) -> {
                        if (vList.stream().anyMatch(iu -> iu.equalsIgnoreCase(v.getUuid()))) {
                            aduitMsg.add(k);
                        }
                    });
                    v.setAduitContent(String.join("\r\n", aduitMsg));
                }
            } catch (Exception e) {
                resultModel.setRemark("解析/校驗異常：" + e.getMessage());
            }

            resultModel.setIpoHistoryModelList(resultAnnotationList);


        }


        // }

        return resultModel;

    }


    public static Boolean isValidYYYYMMDD(String dateStr) {
        try {
            if (StringUtils.isNotBlank(dateStr)) {
                SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
                sdf.setLenient(false); // 严格模式，不允许自动转换
                try {
                    if (dateStr.length() == 8) {
                        sdf.parse(dateStr);
                        return true;
                    } else if (dateStr.length() == 6) {
                        sdf.parse(dateStr + "01");
                        return true;
                    } else if (dateStr.length() == 4) {
                        sdf.parse(dateStr + "0101");
                        return true;
                    }
                } catch (Exception e) {
                    return false;
                }

                String format = dateStr.replace("年", "#").replace("月", "#").replace("日", "#").replace(".", "#").replace("-", "#").replace("/", "#");
                if (CommonUtil.iscontainsChinese(format)) {
                    return false;
                }
                String[] arr = format.split("#");
                StringBuilder sb = new StringBuilder();
                sb.append(arr[0]);
                Calendar cal = Calendar.getInstance();  // 获取当前时间的Calendar实例
                int year = cal.get(Calendar.YEAR);
                if (Integer.valueOf(arr[0]) > year) {
                    return false;
                }

                if (arr.length == 3) {
                    if (arr[1].length() < 2) {
                        sb.append("0").append(arr[1]);
                    } else {
                        sb.append(arr[1]);
                    }
                    if (arr[2].length() < 2) {
                        sb.append("0").append(arr[2]);
                    } else {
                        sb.append(arr[2]);
                    }

                } else if (arr.length == 2) {
                    if (arr[1].length() < 2) {
                        sb.append("0").append(arr[1]);
                    } else {
                        sb.append(arr[1]);
                    }
                    sb.append("01");
                } else if (arr.length == 1) {
                    sb.append("0101");
                }

                if (sb.toString().length() != 8) {
                    return false;
                }


                try {
                    sdf.parse(sb.toString());
                    sdf.parse(dateStr);
                    return true;
                } catch (ParseException e) {
                    return false;
                }

            }

            return true;
        } catch (Exception e) {
            return false;
        }

    }


}
