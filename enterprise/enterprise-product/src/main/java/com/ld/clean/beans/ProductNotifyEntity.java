package com.ld.clean.beans;

import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class ProductNotifyEntity {
    /**
     * 类型 0：产品基本信息，1：产品所有维度，2：产品融资，3：产品人员，4：产品竞品，5：产品新闻, 6:榜单
     */
    private int type;

    /**
     * 产品id
     */
    private String productId;

    /**
     * 融资id集合 逗号分割
     */
    private String financeIdList;

    /**
     * 新闻id集合 逗号分割
     */
    private String newsIdList;

    /**
     * 成员id集合 逗号分割
     */
    private String memberIdList;

    /**
     * 竞品id集合 逗号分割
     */
    private String competitorIdList;

    public ProductNotifyEntity() {
    }

    public ProductNotifyEntity(int type, String productId) {
        this.type = type;
        this.productId = productId;
    }

    public ProductNotifyEntity(int type, String productId, String financeIdList) {
        this.type = type;
        this.productId = productId;
        this.financeIdList = financeIdList;
    }
}
