package com.ld.clean.test.mainproduct;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.ld.clean.constants.MyConstants;
import com.ld.clean.context.ApplicationCoreContextManager;
import com.ld.clean.enums.DimensionEnum;
import com.ld.clean.job.product.monitorcompanychange.entity.NameKeyNoTagsEntity;
import com.ld.clean.job.productv2.baseinfo.entity.RelationCompEntity;
import com.ld.clean.job.productv2.mergercompevents.entity.MergerDetailEntity;
import com.ld.clean.model.ASharesAndNewThirdBoardFinance;
import com.ld.clean.model.QccCompanyOutCt;
import com.ld.clean.model.baseenterprisedb.CompFinanceRoundMapping;
import com.ld.clean.model.basefinancialdb.QccMktReportIdMapping;
import com.ld.clean.model.manageenterprisedb.*;
import com.ld.clean.model.managefinancialdb.CompSkRelatedparty;
import com.ld.clean.model.managefinancialdb.SecSkAshareipo;
import com.ld.clean.model.manageglobalsysdb.SysSecCode;
import com.ld.clean.model.tidbsearchsyncfinancial.FinHkskGlobalOverviewSync;
import com.ld.clean.mongo.BaseMongoEnum;
import com.ld.clean.mongo.BaseMongoTemplate;
import com.ld.clean.mongo.QccDataMongoTemplate;
import com.ld.clean.parent.AsyncCleanParentFunction;

import com.ld.clean.pojo.common.escount.CompCountDetailMo;
import com.ld.clean.pojo.common.escount.CountDimValueMo;
import com.ld.clean.pojo.mo.QccMktReportDetail;
import com.ld.clean.pojo.mo.QccMktReportDetailV2;
import com.ld.clean.pojo.mo.count.QccCount;
import com.ld.clean.repository.basefinancialdb.QccMktReportIdMappingRepository;
import com.ld.clean.repository.manageenterprisedb.CompSkequityHoldingRepository;
import com.ld.clean.repository.manageenterprisedb.CompVcpeForeignInvestmentRepository;
import com.ld.clean.repository.manageenterprisedb.InvestCompanyHolderDiffRepository;
import com.ld.clean.repository.manageenterprisedb.ProductBaseinfoRepository;
import com.ld.clean.repository.managefinancialdb.CompSkRelatedpartyRepository;
import com.ld.clean.repository.managefinancialdb.SecSkAshareipoRepository;
import com.ld.clean.repository.manageglobalsysdb.SysSecCodeRepository;
import com.ld.clean.repository.tidbsearchsyncfinancial.FinHkskGlobalOverviewSyncRepository;
import com.ld.clean.test.mainbrand.dao.finance.ScMeASharesAndNewThirdBoardFinance20250428;
import com.ld.clean.util.FinacingAmountUtil;
import com.ld.clean.utils.CtCompanyUtils;
import com.ld.clean.utils.DateUtil;
import com.ld.clean.utils.MD5Util;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import tk.mybatis.mapper.entity.Condition;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
public class TmpCleanBaseinfoSyncFunction extends AsyncCleanParentFunction<DapProductMergerEvents, DapProductMergerEvents> {

    /*private static final EpProductFinancingSyncRepository epProductFinancingSyncRepository = ApplicationCoreContextManager.getInstance(EpProductFinancingSyncRepository.class);
    private static final EpProductBaseinfoSyncRepository epProductBaseinfoSyncRepository = ApplicationCoreContextManager.getInstance(EpProductBaseinfoSyncRepository.class);
    private static final ProductBaseinfoRepository productBaseinfoRepository = ApplicationCoreContextManager.getInstance(ProductBaseinfoRepository.class);
    private static final CompBrandPhaseInfoRepository compBrandPhaseInfoRepository = ApplicationCoreContextManager.getInstance(CompBrandPhaseInfoRepository.class);
    private static final EpProductFinancingV2SyncRepository epProductFinancingV2SyncRepository = ApplicationCoreContextManager.getInstance(EpProductFinancingV2SyncRepository.class);
    private static final CompVcpeLabelBaseinfoRepository compVcpeLabelBaseinfoRepository = ApplicationCoreContextManager.getInstance(CompVcpeLabelBaseinfoRepository.class);
    private static final CompMaEventCvsRepository compMaEventCvsRepository = ApplicationCoreContextManager.getInstance(CompMaEventCvsRepository.class);
    private static final CompProductMergerEventsQkRepository compProductMergerEventsQkRepository = ApplicationCoreContextManager.getInstance(CompProductMergerEventsQkRepository.class);
    private static final CompProductAdminRelationshipRepository compProductAdminRelationshipRepository = ApplicationCoreContextManager.getInstance(CompProductAdminRelationshipRepository.class);
    private static final CompAffiliatedInstitutionRepository compAffiliatedInstitutionRepository =ApplicationCoreContextManager.getInstance(CompAffiliatedInstitutionRepository.class);*/
    private static final SecSkAshareipoRepository secSkAshareipoRepository = ApplicationCoreContextManager.getInstance(SecSkAshareipoRepository.class);
    private static final ProductBaseinfoRepository productBaseinfoRepository = ApplicationCoreContextManager.getInstance(ProductBaseinfoRepository.class);
    public static MongoTemplate mongoTemplate;

    private static final QccDataMongoTemplate qccDataMongoTemplate = ApplicationCoreContextManager.getInstance(QccDataMongoTemplate.class);
    private static final CompVcpeForeignInvestmentRepository compVcpeForeignInvestmentRepository = ApplicationCoreContextManager.getInstance(CompVcpeForeignInvestmentRepository.class);
    private static final CompSkRelatedpartyRepository compSkRelatedpartyRepository =  ApplicationCoreContextManager.getInstance(CompSkRelatedpartyRepository.class);
    private static final CompSkequityHoldingRepository compSkequityHoldingRepository =  ApplicationCoreContextManager.getInstance(CompSkequityHoldingRepository.class);
    private static SysSecCodeRepository sysSecCodeRepository = ApplicationCoreContextManager.getInstance(SysSecCodeRepository.class);
    private static final FinHkskGlobalOverviewSyncRepository finHkskGlobalOverviewSyncRepository = ApplicationCoreContextManager.getInstance(FinHkskGlobalOverviewSyncRepository.class);
    private static final QccMktReportIdMappingRepository qccMktReportIdMappingRepository = ApplicationCoreContextManager.getInstance(QccMktReportIdMappingRepository.class);


    private static final List<String> VALID_ROUNDS = Arrays.asList("IPO", "退市", "挂牌", "摘牌");

    private static final List<String> EX_NAME = Arrays.asList("北京证券交易所", "上海证券交易所", "深圳证券交易所");
    private static final List<String> HK_MO_TW = Arrays.asList("HK", "MO", "TW");
    private static final List<String> fields = Arrays.asList("_id", "WebSite","StartDate");
    private static final List<Integer> types = Arrays.asList(24, 44, 45, 47, 48, 80, 129, 152, 264);
    public TmpCleanBaseinfoSyncFunction(int corePoolSize) {
        super(corePoolSize);
    }


    /*@Override
    protected CompMaEventCvs invoke(CompMaEventCvs cvs) throws Exception {
        if ( ObjectUtil.isEmpty(cvs.getSpiderSource())) {
            cvs.setSpiderSource(1);
           *//* List<MergerDetail> mergerDetails = JSON.parseArray(cvs.getMergerdetails(), MergerDetail.class);
            if (CollectionUtil.isNotEmpty(mergerDetails)) {
                MergerDetail detail = mergerDetails.get(0);

                if (("进行中".equals(detail.getStatus()) || "已失败".equals(detail.getStatus())) && cvs.getRecordStatus() == 0) {
                    cvs.setDataStatus(2);
                }
                String context = detail.getContext();
                String title = detail.getTitle();
                if (StringUtils.isNotBlank(context) && StringUtils.isNotBlank(title)) {
                    if ((context.contains("股份回购") || title.contains("股份回购")) && cvs.getRecordStatus() == 0) {
                        cvs.setDataStatus(2);
                    }
                    if ((context.contains("增持") || title.contains("增持")) && cvs.getRecordStatus() == 0 && "非控制权收购".equals(cvs.getRoundname())) {
                        cvs.setDataStatus(2);
                    }
                }
            }*//*
            compMaEventCvsRepository.updateByPrimaryKey(cvs);
            return null;

        }
        return null;
    }*/

    @Override
    protected DapProductMergerEvents invoke(DapProductMergerEvents mergerEvents) throws Exception {


        if (StringUtils.isNotBlank(mergerEvents.getMergerDetails())) {
            MergerDetailEntity detail = JSON.parseArray(mergerEvents.getMergerDetails(), MergerDetailEntity.class).get(0);
            String link = detail.getLink();
            //String backLink = detail.getBackupsLink();
            if (StringUtils.isNotBlank(link) && link.startsWith("https://www.qcc.com/web/tools-finance/search-announce-detail")) {
                String[] parts = link.split("id=");

                if (parts.length > 1) {
                    String idAndParameters = parts[1];
                    String id = idAndParameters.substring(0, 32);
                    //查询
                    Query query = new Query(Criteria.where("_id").is(id));
                    List<QccMktReportDetailV2> qccMktReportDetails = BaseMongoTemplate.getInstance(BaseMongoEnum.SEARCH_SYNC_FINANCIAL).find(query, QccMktReportDetailV2.class);
                    if (CollectionUtil.isNotEmpty(qccMktReportDetails)) {
                        //保持不动
                    }else {
                        //变更
                        Query query1 = new Query(Criteria.where("_id").is(id));
                        List<QccMktReportDetail> qccMktReportV1s = BaseMongoTemplate.getInstance(BaseMongoEnum.SEARCH_SYNC_FINANCIAL).find(query1, QccMktReportDetail.class);
                        if(CollectionUtil.isNotEmpty(qccMktReportV1s)){
                            QccMktReportDetail detail1 = qccMktReportV1s.get(0);
                            String ossId = detail1.getOssId();
                            String reportTitle = this.replaceTxt(detail1.getReportTitle());
                            Long reportDate = detail1.getReportDate();
                            String syncId =  MD5Util.encode(reportTitle,
                                    reportDate.toString(), ossId);
                            //查询v2如果存在进行替换
                            Query queryv2 = new Query(Criteria.where("_id").is(syncId));
                            List<QccMktReportDetailV2> qccMktReportDetailsv2 = BaseMongoTemplate.getInstance(BaseMongoEnum.SEARCH_SYNC_FINANCIAL).find(queryv2, QccMktReportDetailV2.class);
                            if(CollectionUtil.isNotEmpty(qccMktReportDetailsv2)){
                                //将link后id置换为v2id
                                detail.setLink(parts[0]+"id="+syncId);
                                System.out.println(mergerEvents.getId()+"    "+JSONObject.toJSONString(Collections.singletonList(detail)));
                            }
                        }




                        /*Condition mappingCondition = new Condition(QccMktReportIdMapping.class);
                        mappingCondition.and().andEqualTo("businessV1Id", id)
                                .andEqualTo("dataStatus", 1);
                        List<QccMktReportIdMapping> mappingList = qccMktReportIdMappingRepository.selectByCondition(mappingCondition);
                        if(CollectionUtil.isNotEmpty(mappingList)){
                            QccMktReportIdMapping mapping = mappingList.get(0);
                            String v2Id = mapping.getBusinessV2Id();
                            detail.setLink(v2Id);
                            System.out.println(mergerEvents.getId()+"    "+JSONObject.toJSONString(Collections.singletonList(detail)));

                        }*/

                    }
                }

            }
        }

        return mergerEvents;


        /*String holderName = share.getHolderName();
        if(StringUtils.isNotBlank(holderName)){
            String keyNo = CtCompanyUtils.getKeyNoByName(holderName, "", "", null, true, "", DimensionEnum.PD_PRODUCT);
            if(StringUtils.isNotBlank(keyNo)){
                share.setHolderOrgType(1);
            }
        }
        return share;*/

        /*//查询获取  两批
        //1.品牌存在keyno
        String keyno = share.getCompanyKeyno();
        if(StringUtils.isBlank(keyno)){
            System.out.println(share.getId() +"未匹配");
            return null;
        }

        Condition cc = new Condition(FinHkskGlobalOverviewSync.class);
        cc.createCriteria().andEqualTo("compKeyno", keyno).andEqualTo("dataStatus", 1);
        List<FinHkskGlobalOverviewSync> sync = finHkskGlobalOverviewSyncRepository.selectByCondition(cc);
        if(CollectionUtil.isNotEmpty(sync)){
            String name = sync.get(0).getCompName();
            RelationCompEntity entity = new RelationCompEntity();
            entity.setType("1");
            entity.setName(name);
            entity.setKeyno(keyno);

            System.out.println();
        }
        return null;*/








        /*if (null != ship) {
            String id = ship.getSpiderId();
            SecSkAshareipo secSkAshareipo = (SecSkAshareipo)secSkAshareipoRepository.selectByPrimaryKey(id);
            if(ObjectUtil.isNotEmpty(secSkAshareipo.getIssueMarketvalue())){
                ship.setMarketValueRmb(secSkAshareipo.getIssueMarketvalue());

                String valuation = secSkAshareipo.getIssueMarketvalue().toEngineeringString()+"元人民币";
                String marketValue = FinacingAmountUtil.handleDimensionAmount(valuation,true);

                ship.setMarketValue(marketValue);
            }
        }


        return ship;*/

        //逻辑处理过滤 置为0
        //1 无项目 根据keyno查询   diff 补充
/*        String compKeyno = diff.getCompanyKeyno();
        Boolean haveNotProduct = false;
        Condition condition = new Condition(ProductBaseinfo.class);
        condition.createCriteria().andEqualTo("companyId", compKeyno).andEqualTo("dataStatus",1);
        List<ProductBaseinfo> infos = productBaseinfoRepository.selectByCondition(condition);
        if(CollectionUtil.isEmpty(infos)){
            haveNotProduct = true;
        }
        //2 无官网
        Boolean havaWeb = false;
        QccCompanyOutCt qccCompany = CtCompanyUtils.getCompDetailsByKeyNo(Arrays.asList(compKeyno), fields).stream().findFirst().orElse(null);

        String startDate = DateUtil.getStringFormatTimeOfTimestamp(qccCompany.getStartDate() * 1000, DateUtil.YMD);

        if(null == qccCompany){
            diff.setDataStatus(0);
            return diff;
        }else {
            if(StringUtils.isBlank(qccCompany.getWebSite())){
                havaWeb = true;
            }
        }


        // 企业发展  知识产权
        Boolean haveCompInfo = false;
        CompCountDetailMo qccCount = qccDataMongoTemplate.getMongoTemplate().findById(compKeyno, CompCountDetailMo.class);
        Set<Integer> setVa = new HashSet<>();
        //'NewsCount','SAICCount','SoftCRCount','WorkCRCount'

        setVa.add(qccCount.getNewsCount());
        setVa.add(qccCount.getSAICCount());
        setVa.add(qccCount.getSoftCRCount());
        setVa.add(qccCount.getWorkCRCount());

        List<CountDimValueMo<Integer>> oldCommonList = qccCount.getCommonList();
        List<Object> targetValues = oldCommonList.stream()
                .filter(item -> types.contains(item.getDimId()))
                .map(CountDimValueMo::getDimCount)
                .collect(Collectors.toList());

        if(setVa.size() == 1 && setVa.iterator().next() == 0 && targetValues.size() == 0){
            haveCompInfo = true;
        }else {
            return null;
        }
        //股东相关
        //参考 MdCleanFeildAsync2
        //股东入股日期
        Condition condition1 = new Condition(CompVcpeForeignInvestment.class);
        condition1.createCriteria().andEqualTo("investedCompKeyno",compKeyno).andEqualTo("dataStatus",1);
        List<CompVcpeForeignInvestment> list = compVcpeForeignInvestmentRepository.selectByCondition(condition1);
        boolean sameDate = true;
        for(CompVcpeForeignInvestment investment : list ){
            if(StringUtils.isNotBlank(investment.getInDate()) && !startDate.equals(investment.getInDate())){
                sameDate = false;
                break;
            }
        }

        List<String> compKeynoList = list.stream()
                .map(CompVcpeForeignInvestment::getCompKeyno)
                .collect(Collectors.toList());

        //股东是否有上市企业
        boolean isIPo = true;
        if(CollectionUtil.isNotEmpty(compKeynoList)){
            Condition secCodeCondition = new Condition(SysSecCode.class);
            secCodeCondition.createCriteria().andIn("compKeyno", compKeynoList).andEqualTo("dataStatus", 1);
            List<SysSecCode> sysSecCodeList = sysSecCodeRepository.selectByCondition(secCodeCondition);
            if(CollectionUtil.isNotEmpty(sysSecCodeList)){
                isIPo = false;
            }
        }


        //及上市企业100%控股公司（或者参控股）
        //comp_sk_relatedparty A股  related_comp_keyno
        *//*boolean control = false ;
        boolean haveAIpo = false;
        Condition cc1 =new Condition(CompSkRelatedparty.class);
        cc1.createCriteria().andEqualTo("relatedCompKeyno",compKeyno).andEqualTo("dataStatus",1).andIsNotNull("symbol");
        List<CompSkRelatedparty> compSkRelatedpartiesParent =compSkRelatedpartyRepository.selectByCondition(cc1);
        if(CollectionUtil.isNotEmpty(compSkRelatedpartiesParent)){
            haveAIpo = true;
        }

        //comp_skequity_holding 港股参控股   keyno_investee查询
        boolean haveHKIpo = false;
        Condition condition2 =new Condition(CompSkequityHolding.class);
        condition2.createCriteria().andEqualTo("keynoInvestee",compKeyno).andEqualTo("dataStatus",1).andIsNotNull("symbol");
        List<CompSkequityHolding> compSkequityHoldingList =compSkequityHoldingRepository.selectByCondition(condition2);
        if(CollectionUtil.isNotEmpty(compSkequityHoldingList)){
            haveHKIpo = true;
        }

        if(haveAIpo || haveHKIpo){
            control = true;
        }*//*
        //1.被投没有项目    2.没有官网  3.没有企业发展信息 都为空或者0  4.不存在股东入股日期与公司成立日期不同的  5.股东无上市企业 6.无上市控股
        if(haveNotProduct && havaWeb && haveCompInfo && sameDate && isIPo *//*&& control*//*){
            diff.setDataStatus(0);
            System.out.println("--------------   :   "+diff.getId());
            return diff;
        }
        return null;*/
        //补充symbol代码
       /* Condition condition = new Condition(EpProductFinancingV2Sync.class);
        condition.createCriteria().andEqualTo("productId", product.getId()).andEqualTo("dataStatus", 1);
        List<EpProductFinancingV2Sync> financingList = epProductFinancingV2SyncRepository.selectByCondition(condition);

        //股票代码  获取最新上市退市挂牌摘牌的 股票代码
        if(CollectionUtils.isEmpty(financingList)){
            return null;
        }
        Optional<EpProductFinancingV2Sync> maxRecord = financingList.stream()
                .filter(entity -> VALID_ROUNDS.contains(entity.getRound()))
                .max(Comparator.comparing(EpProductFinancingV2Sync::getFinanceDate));
        if(maxRecord.isPresent()){
            String maxCode = maxRecord.get().getStockCode();
            if(StringUtils.isNotBlank(maxCode) && maxCode.contains(".")){
                int lastIndex = maxCode.lastIndexOf('.');
                product.setSymbol(maxCode.substring(0, lastIndex));
                return product;
            }
        }


        return null;*/


        /*if(StringUtils.isBlank(productBaseinfo.getStockExchangeShow())){
            return null;
        }
        productBaseinfo.setStockExchangeShowWeb(ExchangeForWebEnums.getByCode(productBaseinfo.getStockExchangeShow()).getName());

        return productBaseinfo;*/


        /*if(StringUtils.isBlank(v2.getProductId())){
            return null;
        }
        EpProductBaseinfoSync sync = (EpProductBaseinfoSync)epProductBaseinfoSyncRepository.selectByPrimaryKey(v2.getProductId());
        if(null != sync){
            v2.setBoundaryType(sync.getBoundaryType());
        }*/


//根据基础表nation_code 和 province_code 判断
        /*if("CN".equals(product.getNationCode()) ){
            sync.setBoundaryType(1);
            if(HK_MO_TW.contains(product.getProvinceCode())){
                sync.setBoundaryType(2);
            }
        }else {
            sync.setBoundaryType(2);
        }*/

       /* EpProductFinancingV2Sync latestFinance = null;
        Condition condition = new Condition(EpProductFinancingV2Sync.class);
        condition.createCriteria().andEqualTo("productId", productBaseinfo.getId()).andEqualTo("dataStatus", 1);
        List<EpProductFinancingV2Sync> financingList = epProductFinancingV2SyncRepository.selectByCondition(condition);
        if(CollectionUtils.isNotEmpty(financingList)){
            List<EpProductFinancingV2Sync> sortList = financingList.stream()
                    .sorted(new Comparator<EpProductFinancingV2Sync>() {
                        @Override
                        public int compare(EpProductFinancingV2Sync o1, EpProductFinancingV2Sync o2) {
                            return Long.valueOf(o2.getFinanceDate()).compareTo(Long.valueOf(o1.getFinanceDate()));
                        }
                    }.thenComparing(EpProductFinancingV2Sync::getRoundSortStage)
                            .thenComparing(EpProductFinancingV2Sync::getRoundSort, Comparator.reverseOrder())).collect(Collectors.toList());
            latestFinance = sortList.get(0);
        }
        if(null != latestFinance){
            productBaseinfo.setStageCode(latestFinance.getRoundSortStage());
            String exchange = latestFinance.getStockExchange();
            if( StringUtils.isNotBlank(exchange)){
                String section = "";
                if(EX_NAME.contains(exchange) && ObjectUtil.isNotEmpty(latestFinance.getListSection())){
                    section = ListSectionEnum.getByCode(latestFinance.getListSection()).getDesc();
                }
                productBaseinfo.setStockExchangeShow(latestFinance.getStockExchange()+section);
            }
        }else {
            productBaseinfo.setStageCode(null);
        }*/

        /*EpProductBaseinfoSync sync = (EpProductBaseinfoSync)epProductBaseinfoSyncRepository.selectByPrimaryKey(productBaseinfo.getId());

        sync.setStageCode(ObjectUtil.isEmpty(productBaseinfo.getCompStageGs())
                ? productBaseinfo.getCompStageCt()
                : productBaseinfo.getCompStageGs());*/
        //return v2;
    }

    private NameKeyNoTagsEntity getNameKeyNoEntity(String name, String keyNo, Boolean flag) {
        NameKeyNoTagsEntity nameKeyTagsEntity = new NameKeyNoTagsEntity();
        if (flag) {
            nameKeyTagsEntity.setName(name);
            nameKeyTagsEntity.setKeyno(keyNo);
            nameKeyTagsEntity.setNameUrl(MyConstants.FIRM_PREFIX + keyNo + MyConstants.HTML);
        } else {
            nameKeyTagsEntity.setName(name);
            nameKeyTagsEntity.setKeyno(keyNo);
            nameKeyTagsEntity.setNameUrl(MyConstants.PRODUCT_PREFIX + keyNo + MyConstants.HTML);
        }

        return nameKeyTagsEntity;
    }

    public static String replaceTxt(String content) {
        String newContent = content;
        if (org.apache.commons.lang.StringUtils.isEmpty(newContent)) {
            return "";
        }
        newContent = newContent
                .replace("&amp;", "&").replace("amp;", "")
                .replace("&nbsp;", " ").replace("nbsp;", "")
                .replace("\n", "").replace("\r", "")
                .replace("\t", "")
                .replace("\\u0000", "")
                .replace("\\u0026", "&").replace("\\u003e", ">")
                .replace("\\u003c", "<").replace("/td", "")
                .replace("BsonNull", "")
                .replace("&#8226;", "•").replace("&#8226", "•")
                .replace("&middot;", "·").replace("&#xB7;", "·")
                .replace("&#xB7", "·").replace("&#17557;", "䒕")
                .replace("&NBSP;", " ").replace("NBSP;", "")
                .replace("NULL", "").replace("null", "")
                .replace("#8226;", "•").replace("#xB7", "·")
                .replace("#xB7;", "·").replace("#17557;", "䒕").replace("&ouml;", "ö")
                .replace("\"", "").replace("\\", "")
                .replace("\\'", "").replace("&quot;", "\"")
                .replace("&mdash;", "—").replace("\\u001a", "")
                .replace("&uuml;", "ü").replace("&ldquo;", "“")
                .replace("&rdquo;", "”").replace("&emsp;", " ").trim();

        newContent = removeLeadingAndTrailingQuestionMarks(newContent);

        return newContent;
    }
    public static String removeLeadingAndTrailingQuestionMarks(String s) {
        if (s == null || s.isEmpty()) {
            return s; // 如果字符串为空，直接返回
        }

        // 从开头去除连续的 ? 和 ？ 字符
        int start = 0;
        while (start < s.length() && (s.charAt(start) == '?' || s.charAt(start) == '？')) {
            start++;
        }

        // 从结尾去除连续的 ? 和 ？ 字符
        int end = s.length() - 1;
        while (end >= start && (s.charAt(end) == '?' || s.charAt(end) == '？')) {
            end--;
        }

        // 返回去除开头和结尾的 ? 和 ？ 后的子字符串
        return s.substring(start, end + 1);
    }
}