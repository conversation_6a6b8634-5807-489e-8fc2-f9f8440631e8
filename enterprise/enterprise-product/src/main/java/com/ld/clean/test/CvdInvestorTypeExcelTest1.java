package com.ld.clean.test;

import com.ld.clean.context.ApplicationCoreContextManager;
import com.ld.clean.model.tidbsearchsyncenterprise.EpProductFinancingV2Sync;
import com.ld.clean.repository.manageenterprisedb.CompInvestInstitutionBaseinfoV2Repository;
import com.ld.clean.repository.tidbsearchsyncenterprise.EpProductFinancingV2SyncRepository;
import com.ld.clean.test.entity.FinancialExcel;
import com.ld.clean.utils.DateUtil;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import tk.mybatis.mapper.entity.Condition;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

public class CvdInvestorTypeExcelTest1 {
    private static final CompInvestInstitutionBaseinfoV2Repository compInvestInstitutionBaseinfoV2Repository = ApplicationCoreContextManager.getInstance(CompInvestInstitutionBaseinfoV2Repository.class);
    private static final EpProductFinancingV2SyncRepository epProductFinancingV2SyncRepository = ApplicationCoreContextManager.getInstance(EpProductFinancingV2SyncRepository.class);

    /* public static void main(String[] args) {
         System.out.println("xxx");
     }*/
    public static void main(String[] args) throws Exception {
        System.out.println("step1");
        try {
            XSSFWorkbook book = new XSSFWorkbook("C:\\Users\\<USER>\\Desktop\\融资事件待补充与线上数据对比_90天内投资方完全相同的数据(考虑关联机构或项目)_to数产.xlsx");

            XSSFSheet sheet = book.getSheet("Sheet1");

            int lastRowNum = sheet.getLastRowNum();


            List<FinancialExcel> financialExcels = new ArrayList<>();

            for (int i = 1; i <= 2531; i++) {
                XSSFRow row = sheet.getRow(i);

                if (row != null) {

                    FinancialExcel excel = new FinancialExcel();
                    for (int j = 0; j <= 17; j++) {
                        Cell cell = row.getCell(j);

                        if (cell != null) {
                            cell.setCellType(Cell.CELL_TYPE_STRING);
                            String value = cell.getStringCellValue();
                            switch (j) {
                                case 0:
                                    excel.setId(value);
                                    break;
                                case 1:
                                    excel.setPid(value);
                                    break;
                                case 4:
                                    excel.setFinanceDateCurrent(value);
                                case 12:
                                    excel.setFinanceDateExcel(value);
                                    break;
                                case 14:
                                    excel.setParticipantDetails(value);
                                    break;
                                default:
                                    break;

                            }

                        }

                    }

                    financialExcels.add(excel);
                }

            }

            List<String> pids = financialExcels.stream().filter(vv -> StringUtils.isNotBlank(vv.getPid())).map(FinancialExcel::getPid).collect(Collectors.toList());
            Condition condition = new Condition(EpProductFinancingV2Sync.class);
            condition.createCriteria().andEqualTo("dataStatus", 1).andIn("productId", pids);
            List<EpProductFinancingV2Sync> epProductFinancingV2Syncs = epProductFinancingV2SyncRepository.selectByCondition(condition);



            financialExcels.stream().forEach(it -> {
                EpProductFinancingV2Sync vv = epProductFinancingV2Syncs.stream().filter(hh -> hh.getId().equals(it.getId())).findFirst().orElse(null);
                if (vv != null) {

                        List<EpProductFinancingV2Sync> opList = epProductFinancingV2Syncs.stream().filter(vvv -> vvv.getProductId().equals(vv.getProductId())).collect(Collectors.toList());
                        Long date1 = DateUtil.getTimestampOfStringDate(it.getFinanceDateCurrent(), "yyyy-MM-dd");
                        Long date2 = DateUtil.getTimestampOfStringDate(it.getFinanceDateExcel(), "yyyy-MM-dd");
                        List<EpProductFinancingV2Sync> midList = new ArrayList<>();
                        if (date1 >= date2) {
                            midList.addAll(opList.stream().filter(kk -> !kk.getId().equals(it.getId()) && kk.getFinanceDate() <= date1 / 1000 && kk.getFinanceDate() >= date2 / 1000).collect(Collectors.toList()));
                        } else {
                            midList.addAll(opList.stream().filter(kk -> !kk.getId().equals(it.getId()) && kk.getFinanceDate() <= date2 / 1000 && kk.getFinanceDate() >= date1 / 1000).collect(Collectors.toList()));

                        }

                        if (midList.isEmpty()) {

                            if (vv.getSourceType() == 14) {
                                System.out.println("清洗补充企业投资方,修正融资日期和sourceType =21");

                            }else{
                                System.out.println("清洗补充企业投资方");
                            }



                        } else {
                            System.out.println("采编核验补充，因为下一轮已存在事件");
                        }


                } else {
                    System.out.println("id 不存在");
                }
            });


        } catch (Exception e) {
            e.printStackTrace();
        }
    }


}
