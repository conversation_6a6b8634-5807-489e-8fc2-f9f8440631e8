package com.ld.clean.util;

import com.ld.clean.beans.Currency;
import com.ld.clean.model.EpCtCurrency;
import com.ld.clean.utils.CtCurrencyUtils;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 */
public class FinacingValuationUtil {

    public static final Set<String> FINACING_VALUATION_ENUMS = new HashSet<>(Arrays.asList("-", "=", "未披露"));

    public static final Set<String> AMOUNT_UNIT_ENUMS = new HashSet<>(Arrays.asList("人民币","美元", "欧元", "港元" ,"英镑" ,"港币"));

    public static final List<String> WORD_TO_REMOVE = Arrays.asList("约", "不超过", "超", "数");

    public static String getFinacingValuation(String valuation) {
        if (StringUtils.isBlank(valuation)) {
            return "未披露";
        }
        if (FINACING_VALUATION_ENUMS.contains(valuation)) {
            return "未披露";
        }
        return valuation;
    }

    public static String handleValuation(String amount , String financeDate , BigDecimal amountRmb){
        //估值处理
        //先获取单位
        String unit = Currency.getUnitFromAmountSimple(amount);
        if(AMOUNT_UNIT_ENUMS.contains(unit)){
            //受否包含数字
            if(amount.matches(".*\\d.*")){
                //剔除约、不超过、超、数
                for (String word : WORD_TO_REMOVE) {
                    if (amount.contains(word)) {
                        amount = amount.replace(word, "").trim();
                        break;
                    }
                }
                //剔除小数
                String num = amount.replaceAll("(.*?\\d)(\\.\\d+)?", "$1");
                //人民币加元
                if(num.contains("人民币") && !num.contains("元")){
                    num = num.replace("人民币","元人民币");
                }
                return num;
            }else {
                //不包含数字
                if(amount.contains("不超过")){
                    amount = amount.replace("不超过","约");
                }
                if(amount.contains("超")){
                    amount = amount.replace("超","约");
                }
                if(amount.contains("人民币") && !amount.contains("元")){
                    amount = amount.replace("人民币","元人民币");
                }
                return amount;
            }
        }else {
            //人民币转为美元
            return  CtCurrencyUtils.transCNYToUSD(amountRmb,financeDate,0);
        }
    }
}
