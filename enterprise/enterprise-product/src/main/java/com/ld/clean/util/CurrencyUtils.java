package com.ld.clean.util;


import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.ld.clean.context.ApplicationCoreContextManager;
import com.ld.clean.model.managefinancialdb.IrmRmbCentralParity;
import com.ld.clean.repository.managefinancialdb.IrmRmbCentralParityRepository;
import joptsimple.internal.Strings;
import org.apache.commons.lang3.StringUtils;
import tk.mybatis.mapper.entity.Condition;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
public class CurrencyUtils {

    private static Cache<String, List<IrmRmbCentralParity>> CURRENCY_CACHE = CacheBuilder.newBuilder()
            .expireAfterWrite(24, TimeUnit.HOURS).maximumSize(1000000)
            .build();

    private static IrmRmbCentralParityRepository irmRmbCentralParityRepository = ApplicationCoreContextManager.getInstance(IrmRmbCentralParityRepository.class);


    /**
     *
     * @param value
     * @param currency
     * @param endDate  20201111 8位数字日期类型
     * @return 汇率倍数
     */

    public static BigDecimal convertWithUnitAndCurrency(BigDecimal value,String currency, String endDate,int round) {


        BigDecimal times =BigDecimal.ONE;

        if("CNY".equals(currency)){
             return value;
        }

        List<IrmRmbCentralParity>  objectList =CURRENCY_CACHE.getIfPresent("currencyList");

        if(objectList==null){
            Condition condition =new Condition(IrmRmbCentralParity.class);
            condition.createCriteria().andEqualTo("dataStatus",1);
            List<IrmRmbCentralParity> list =irmRmbCentralParityRepository.selectByCondition(condition);
            CURRENCY_CACHE.put("currencyList",list);
            objectList = list;
        }
        String currency1 = Strings.EMPTY;
        BigDecimal rate1 =null;


        List<IrmRmbCentralParity> parities =objectList.stream().filter(it->StringUtils.isNotBlank(it.getTradeDate())&&it.getExchangeRate()!=null &&it.getCurrency().contains(currency)).collect(Collectors.toList());
        if(!parities.isEmpty()){
            if(StringUtils.isBlank(endDate)){
                currency1 = parities.get(0).getCurrency();
                rate1 =parities.get(0).getExchangeRate();
            }else{
                IrmRmbCentralParity op =  parities.stream().filter(it->it.getTradeDate().equals(endDate) && it.getCurrency().contains(currency)).max(Comparator.comparing(IrmRmbCentralParity::getTradeDate)).orElse(null);
                if(op !=null){
                    currency1 = op.getCurrency();
                    rate1 =op.getExchangeRate();
                }else{
                    IrmRmbCentralParity op1 =  parities.stream().filter(it-> it.getCurrency().contains(currency)).min(Comparator.comparing(it->{
                        return Math.abs(Integer.valueOf(endDate)-Integer.valueOf(it.getTradeDate())); })).orElse(null);
                    if(op1 !=null){
                        currency1 = op1.getCurrency();
                        rate1 =op1.getExchangeRate();
                    }

                }
            }
        }

        if(StringUtils.isNotBlank(currency1) &&rate1 !=null) {
            if ("JPY".equals(currency) && currency1.contains("100JPY")) {
                times =times.multiply(rate1).divide(new BigDecimal(100L), 6, RoundingMode.HALF_UP);
            } else {
                if (currency1.startsWith("CNY/")) {
                    times =times.divide(rate1, 6, RoundingMode.HALF_UP);
                } else {
                    times =times.multiply(rate1).setScale(6, RoundingMode.HALF_UP);
                }
            }
        }

        return times.multiply(value).setScale(round,RoundingMode.HALF_UP);


    }
}
