package com.ld.clean.test.mainproduct;

import cn.hutool.core.util.ObjectUtil;
import com.ld.clean.constants.CompanyStageEnum;
import com.ld.clean.context.ApplicationCoreContextManager;
import com.ld.clean.dao.model.manageenterprisedb.CompProductMergerEventsQk;
import com.ld.clean.dao.repository.manageenterprisedb.CompProductMergerEventsQkRepository;
import com.ld.clean.model.ASharesAndNewThirdBoardFinance;
import com.ld.clean.model.CompBrandFinancing;
import com.ld.clean.model.EpCtCurrency;
import com.ld.clean.model.baseenterprisedb.CompFinanceRoundMapping;
import com.ld.clean.model.baseenterprisedb.CompProductAdminRelationship;
import com.ld.clean.model.manageenterprisedb.*;
import com.ld.clean.model.searchsyncenterprise.EpProductBaseinfoSync;
import com.ld.clean.model.tidbsearchsyncenterprise.EpProductFinancingV2Sync;
import com.ld.clean.mongo.BaseMongoEnum;
import com.ld.clean.pojo.mo.ct.MongoProductV2;
import com.ld.clean.repository.ASharesAndNewThirdBoardFinanceRepository;
import com.ld.clean.repository.CompBrandFinancingRepository;
import com.ld.clean.repository.baseenterprisedb.CompProductAdminRelationshipRepository;
import com.ld.clean.repository.manageenterprisedb.*;
import com.ld.clean.repository.searchsyncenterprise.EpProductBaseinfoSyncRepository;
import com.ld.clean.repository.tidbsearchsyncenterprise.EpProductFinancingV2SyncRepository;
import com.ld.clean.test.mainbrand.dao.ScSeCompBrandFinancing20240513;
import com.ld.clean.test.mainbrand.dao.ScSeCompBrandFinancing20240513Repository;
import com.ld.clean.test.mainbrand.dao.finance.LpfSeEpProductFinancingV2Sync241011;
import com.ld.clean.test.mainbrand.dao.finance.LpfSeEpProductFinancingV2Sync241011Repository;
import com.ld.clean.test.mainbrand.dao.finance.ScMeASharesAndNewThirdBoardFinance20250428;
import com.ld.clean.test.mainbrand.dao.finance.ScMeASharesAndNewThirdBoardFinance20250428Repository;
import com.ld.clean.utils.CtCurrencyUtils;
import com.ld.clean.utils.MongodbUtil;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.api.common.functions.RichFlatMapFunction;
import org.apache.flink.util.Collector;
import tk.mybatis.mapper.entity.Condition;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 */
public class TmpBaseInfoFlatMap extends RichFlatMapFunction<String, DapProductMergerEvents > {

    private static final ProductBaseinfoRepository productBaseinfoRepository = ApplicationCoreContextManager.getInstance(ProductBaseinfoRepository.class);
    //private static CompBrandFinancingRepository compBrandFinancingRepository = ApplicationCoreContextManager.getInstance(CompBrandFinancingRepository.class);
    //private static final LpfSeEpProductFinancingV2Sync241011Repository epProductFinancingV2SyncRepository = ApplicationCoreContextManager.getInstance(LpfSeEpProductFinancingV2Sync241011Repository.class);
    //private static ScSeCompBrandFinancing20240513Repository compBrandFinancingRepository = ApplicationCoreContextManager.getInstance(ScSeCompBrandFinancing20240513Repository.class);
    //private static CompProductFinancingRepository compProductFinancingRepository = ApplicationCoreContextManager.getInstance(CompProductFinancingRepository.class);
    private static final ASharesAndNewThirdBoardFinanceRepository aSharesAndNewThirdBoardFinanceRepository = ApplicationCoreContextManager.getInstance(ASharesAndNewThirdBoardFinanceRepository.class);
    private static final CompProductMergerEventsRepository compProductMergeEventsRepository = ApplicationCoreContextManager.getInstance(CompProductMergerEventsRepository.class);
    private static final CompProductMemberAdminRepository compProductMemberAdminRepository = ApplicationCoreContextManager.getInstance(CompProductMemberAdminRepository.class);
    private static final EpProductFinancingV2SyncRepository epProductFinancingV2SyncRepository = ApplicationCoreContextManager.getInstance(EpProductFinancingV2SyncRepository.class);
    private static final CompProductMergerEventsQkRepository compProductMergerEventsQkRepository =ApplicationCoreContextManager.getInstance(CompProductMergerEventsQkRepository.class);
    private static final CompProductAdminRelationshipRepository compProductAdminRelationshipRepository = ApplicationCoreContextManager.getInstance(CompProductAdminRelationshipRepository.class);
    private static final InvestCompanyHolderDiffRepository investCompanyHolderDiffRepository = ApplicationCoreContextManager.getInstance(InvestCompanyHolderDiffRepository.class);
    private static final ScMeASharesAndNewThirdBoardFinance20250428Repository aSharesAndNewThirdBoardFinance20250428Repository = ApplicationCoreContextManager.getInstance(ScMeASharesAndNewThirdBoardFinance20250428Repository.class);
    private static final DapIpoHistoryRepository dapIpoHistoryRepository = ApplicationCoreContextManager.getInstance(DapIpoHistoryRepository.class);
    private static final DapProductMergerEventsRepository dapProductMergerEventsRepository = ApplicationCoreContextManager.getInstance(DapProductMergerEventsRepository.class);



    @Override
    public void flatMap(String s, Collector<DapProductMergerEvents> collector) throws Exception {
        if (StringUtils.isNotBlank(s)) {
            DapProductMergerEvents mergerEvents = (DapProductMergerEvents)dapProductMergerEventsRepository.selectByPrimaryKey(s);


            collector.collect(mergerEvents);
            /*Condition condition = new Condition(InvestCompanyHolderDiff.class);
            condition.createCriteria().andEqualTo("companyKeyno", s).andEqualTo("recordStatus",0).andEqualTo("sourceType",2)
                    .andEqualTo("dataStatus", 1);
            List<InvestCompanyHolderDiff> list = investCompanyHolderDiffRepository.selectByCondition(condition);
            if(list.size() == 1){
                collector.collect(list.get(0));
            }*/




            //CompProductFinancing pf = (CompProductFinancing) compProductFinancingRepository.selectByPrimaryKey(s);
           //ASharesAndNewThirdBoardFinance pf =  (ASharesAndNewThirdBoardFinance)aSharesAndNewThirdBoardFinanceRepository.selectByPrimaryKey(s);
            //ScSeCompBrandFinancing20240513 pf = (ScSeCompBrandFinancing20240513) compBrandFinancingRepository.selectByPrimaryKey(s);
            /*CompProductMergerEvents pf = (CompProductMergerEvents)compProductMergeEventsRepository.selectByPrimaryKey(s);
            collector.collect(pf);*/


            //LpfSeEpProductFinancingV2Sync241011 pf = (LpfSeEpProductFinancingV2Sync241011)epProductFinancingV2SyncRepository.selectByPrimaryKey(s);
            /*CompProductMemberAdmin pf = (CompProductMemberAdmin)compProductMemberAdminRepository.selectByPrimaryKey(s);
            if(null != pf){
                collector.collect(pf);
            }*/
            /*ProductBaseinfo productBaseinfo = (ProductBaseinfo)productBaseinfoRepository.selectByPrimaryKey(s);
            collector.collect(productBaseinfo);*/
            /*Condition condition = new Condition(CompProductAdminRelationship.class);
            condition.createCriteria().andEqualTo("productId", s)
                    .andEqualTo("dataStatus", 1);
            List<CompProductAdminRelationship> relationshipList = compProductAdminRelationshipRepository.selectByCondition(condition);
            collector.collect(relationshipList);*/

            /*ASharesAndNewThirdBoardFinance mergerEvents = (ASharesAndNewThirdBoardFinance)aSharesAndNewThirdBoardFinanceRepository.selectByPrimaryKey(s);
            collector.collect(mergerEvents);*/
            /*for (CompProductAdminRelationship ship :relationshipList){
                collector.collect(ship);
            }*/

            /*EpProductFinancingV2Sync sync = (EpProductFinancingV2Sync)epProductFinancingV2SyncRepository.selectByPrimaryKey(s);
            collector.collect(sync);*/
            /*Condition condition = new Condition(CompProductMemberAdmin.class);
            condition.createCriteria().andEqualTo("productId",s).andEqualTo("dataStatus",1);
            List<CompProductMemberAdmin> adminList = compProductMemberAdminRepository.selectByCondition(condition);
            for (CompProductMemberAdmin admin :adminList){
                collector.collect(admin);
            }*/

            /*if (null != pf) {
                collector.collect(pf);
            } else {
                ProductBaseinfo baseinfo = new ProductBaseinfo();
                baseinfo.setId(s);
                baseinfo.setDataStatus(0);
                collector.collect(baseinfo);
            }*/
        }
    }
}