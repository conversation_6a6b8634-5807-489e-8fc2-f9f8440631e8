package com.ld.clean.util;

import cn.hutool.core.util.ObjectUtil;
import com.ld.clean.dto.company.QccCompanyOut;
import com.ld.clean.dto.englishtranslate.AreaOut;
import com.ld.clean.model.manageenterprisedb.ProductBaseinfo;
import com.ld.clean.model.manageglobalsysdb.SysNation;
import com.ld.clean.pojo.bo.RegionInfo;
import com.ld.clean.util.ct.CtCacheDataUtil;
import org.apache.commons.lang3.StringUtils;

/**
 * @description：品牌产品location处理
 * @author： liupf
 * @create： 2024/9/25 11:21
 */
public class LocationUtil {

    public static void getLocationData(ProductBaseinfo product, QccCompanyOut company) {
        //历史数据处理
        if (StringUtils.isNotBlank(product.getLocationOversea())) {
            String locationOversea = product.getLocationOversea();
            if (locationOversea.contains("澳门")) {
                product.setLocation("澳门特别行政区");
                product.setLocationOversea("");
            }
            if (locationOversea.contains("香港")) {
                product.setLocation("香港特别行政区");
                product.setLocationOversea("");
            }
            if (locationOversea.contains("台湾")) {
                product.setLocation("台湾省");
                product.setLocationOversea("");
            }

        }
        if (StringUtils.isNotBlank(product.getLocationOversea())) {
            product.setCity("");
            product.setCityCode("");
            product.setCounty("");
            product.setCountyCode("");
            product.setProvince("");
            product.setProvinceCode("");
            //查询码表数据 补充数据
            SysNation sysNation = CtCacheDataUtil.getSysNationByAddress(product.getLocationOversea());
            RegionInfo info = getNewGangAoTai(product.getLocationOversea());
            if (StringUtils.isNotBlank(info.getProvince())) {
                if (null != sysNation) {
                    product.setLocationOversea(sysNation.getNatSname());
                }
                product.setLocationAbbr(info.getProvince());
                product.setNation("中国");
                product.setNationCode("CN");
                product.setProvince(info.getProvince());
                product.setProvinceCode(info.getProvinceCode());
            } else {
                if (null != sysNation) {
                    product.setLocationOversea(sysNation.getNatSname());
                    product.setNation(sysNation.getNatSname());
                    product.setNationCode(sysNation.getNatCode());
                }
                product.setLocationAbbr(product.getLocationOversea());
            }

        }

        if (StringUtils.isNotBlank(product.getLocation()) || ObjectUtil.isNotEmpty(company)) {
            //传入公司id 获取工商数据
            String locationTrans = getAreaOut(product, company);
            String location = product.getLocation();
            if (StringUtils.isNotBlank(locationTrans)) {
                //工商产生的数据处理
                if (locationTrans.contains("-")) {
                    String[] details = locationTrans.split("-");
                    int areaLevel = details.length;
                    AreaOut area = new AreaOut();
                    area.setProvince(details[0]);
                    area.setCity(details[1]);
                    if (areaLevel == 3) {
                        area.setCounty(details[2]);
                    }
                    RegionInfo regionInfo = CtCacheDataUtil.getRegionForProduct(areaLevel, area);
                    getAllLocation(regionInfo, product);
                } else {
                    //无 "-" 即只存在省份
                    RegionInfo regionInfo = CtCacheDataUtil.getRegionByLocation(locationTrans);
                    getAllLocation(regionInfo, product);
                }
            } else {
                if (StringUtils.isNotBlank(product.getLocation())) {
                    //dap采集数据处理 且无companyKeyNo 或 工商查无此 数据，根据采编location进行查询 查询工商无区域信息 或 总部有值
                    //逻辑更新  港澳台数据存入location  location 存入中国香港 中国澳门 台湾省
                    RegionInfo regionInfo = CtCacheDataUtil.getRegionByLocation(location);
                    getAllLocation(regionInfo, product);
                }
            }
        }


        //location和oversea 都为空   判断keyno逻辑
        if (StringUtils.isBlank(product.getLocationOversea()) && StringUtils.isBlank(product.getLocation())) {
            product.setProvince("");
            product.setProvinceCode("");
            product.setNation("");
            product.setNationCode("");
            product.setCity("");
            product.setCityCode("");
            product.setCounty("");
            product.setCountyCode("");
            product.setLocationAbbr("");

            if (StringUtils.isNotBlank(product.getCompanyId())) {
                //有keyno 但是工商无数据
                if (product.getCompanyId().startsWith("h") ||
                        product.getCompanyId().startsWith("z120") ||
                        product.getCompanyId().startsWith("t")) {
                    product.setNation("中国");
                    product.setNationCode("CN");
                    if (product.getCompanyId().startsWith("t")) {
                        product.setLocation("台湾省");
                        RegionInfo regionInfo = CtCacheDataUtil.getRegionByLocation("台湾省");
                        getAllLocation(regionInfo, product);
                    }
                    if (product.getCompanyId().startsWith("h")) {
                        product.setLocation("香港特别行政区");
                        RegionInfo regionInfo = CtCacheDataUtil.getRegionByLocation("香港特别行政区");
                        getAllLocation(regionInfo, product);
                    }
                    if(product.getCompanyId().startsWith("z120")){
                        product.setLocation("澳门特别行政区");
                        RegionInfo regionInfo = CtCacheDataUtil.getRegionByLocation("澳门特别行政区");
                        getAllLocation(regionInfo, product);
                    }
                } else {
                    product.setNation("");
                    product.setNationCode("");
                }
            } else {
                //240905 去掉中文判断 都为空给空置
                product.setNation("");
                product.setNationCode("");
            }
        }
    }

    public static RegionInfo getNewGangAoTai(String location) {
        RegionInfo info = new RegionInfo();
        if (location.contains("香港")) {
            info.setProvince("香港");
            info.setProvinceCode("HK");
        } else if (location.contains("澳门")) {
            info.setProvince("澳门");
            info.setProvinceCode("MO");
        } else if (location.contains("台湾")) {
            info.setProvince("中国台湾");
            info.setProvinceCode("TW");
        } else if (location.contains("台北")) {
            info.setProvince("中国台北");
            info.setProvinceCode("TW");
        }
        return info;
    }

    public static String getAreaOut(ProductBaseinfo product, QccCompanyOut company) {
        if (StringUtils.isNotBlank(product.getCompanyId())) {
            /*List<String> fields = Arrays.asList("_id", "Area");
            QccCompanyOut qccCompany = CompanyDetailsUtil.getCompanyList(Arrays.asList(product.getCompanyId()), true, fields).stream().findFirst().orElse(null);*/
            if (ObjectUtil.isEmpty(company)) {
                return null;
            }
            AreaOut area = company.getArea();
            if (ObjectUtil.isEmpty(area)) {
                return null;
            }
            if (StringUtils.isBlank(area.getProvince())) {
                return null;
            }
            if (StringUtils.isBlank(area.getCity())) {
                return area.getProvince();
            }
            StringBuffer sb = new StringBuffer();
            sb.append(area.getProvince()).append("-").append(area.getCity());
            if (StringUtils.isBlank(area.getCounty())) {
                return sb.toString();
            }
            sb.append("-").append(area.getCounty());
            return sb.toString();
        }
        return null;
    }


    public static void getAllLocation(RegionInfo regionInfo, ProductBaseinfo product) {
        if (null != regionInfo) {
            product.setCity("");
            product.setCityCode("");
            product.setCounty("");
            product.setCountyCode("");
            if (regionInfo.getAreaLevel() == 1) {
                product.setLocation(regionInfo.getFullName());
                product.setLocationRelatedReg("CN" + regionInfo.getProvinceCode() + "0000");
            } else if (regionInfo.getAreaLevel() == 2) {
                product.setLocation(regionInfo.getFullName());
                product.setLocationRelatedReg("CN" + regionInfo.getCityCode() + "00");
                product.setCity(regionInfo.getCity());
                product.setCityCode(regionInfo.getCityCode());
                product.setCounty("");
                product.setCountyCode("");
            } else if (regionInfo.getAreaLevel() == 3) {
                product.setLocation(regionInfo.getFullName());
                product.setLocationRelatedReg("CN" + regionInfo.getCountyCode());
                product.setCity(regionInfo.getCity());
                product.setCityCode(regionInfo.getCityCode());
                product.setCounty(regionInfo.getCounty());
                product.setCountyCode(regionInfo.getCountyCode());
            }
            //locationOversea 和loaction 可都存在，且nation/nationCode 优先工商

            product.setNation("中国");
            product.setNationCode("CN");
            product.setProvince(regionInfo.getProvince());
            //ProvinceCode 按照之前逻辑给 前端省级代码
            product.setProvinceCode(regionInfo.getProvinceAbbr());
            product.setLocationAbbr(regionInfo.getProvince());
            //product.setLocationOversea("");
        } else {
            product.setLocationOversea(product.getLocation());
            product.setLocation("");
        }

    }
}
