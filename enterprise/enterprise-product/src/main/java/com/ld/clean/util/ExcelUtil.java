package com.ld.clean.util;

import org.apache.commons.io.FileUtils;
import org.apache.poi.POIXMLDocument;
import org.apache.poi.hssf.usermodel.HSSFCell;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.openxml4j.exceptions.InvalidFormatException;
import org.apache.poi.openxml4j.opc.OPCPackage;
import org.apache.poi.poifs.filesystem.POIFSFileSystem;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.apache.xmlbeans.impl.piccolo.io.FileFormatException;

import java.io.*;
import java.text.DecimalFormat;
import java.util.*;

/**
 * <AUTHOR>
 */
public class ExcelUtil {

    private static final String EXTENSION_XLS = "xls";
    private static final String EXTENSION_XLSX = "xlsx";

    public static void main(String[] args) throws Exception {
        ExcelUtil excelUtil = new ExcelUtil();
//        Map<Integer, List<String>> integerListMap = excelUtil.readExcel("C:\\person\\doc\\institution\\1900之前\\其他\\机构列表 (1)(1).xls", 0);
//        System.out.println("dsad");
//
        LinkedList<File> files = GetDirectory("C:\\person\\doc\\invest");
        File file3 = new File("C:\\person\\product.txt");
        for (File file : files) {
            FileUtils.writeLines(file3, Collections.singleton(file.getAbsolutePath()), true);
        }
    }


    public static LinkedList<File> GetDirectory(String path) {
        File file = new File(path);
        LinkedList<File> Dirlist = new LinkedList<File>(); // 保存待遍历文件夹的列表
        LinkedList<File> fileList = new LinkedList<File>();
        GetOneDir(file, Dirlist, fileList);// 调用遍历文件夹根目录文件的方法
        File tmp;
        while (!Dirlist.isEmpty()) {
            tmp = (File) Dirlist.removeFirst();
            // 从文件夹列表中删除第一个文件夹，并返回该文件夹赋给tmp变量
            // 遍历这个文件夹下的所有文件，并把
            GetOneDir(tmp, Dirlist, fileList);

        }
        return fileList;
    }

    // 遍历指定文件夹根目录下的文件
    private static void GetOneDir(File file, LinkedList<File> Dirlist,
                                  LinkedList<File> fileList) {
        // 每个文件夹遍历都会调用该方法
        File[] files = file.listFiles();

        if (files == null || files.length == 0) {
            return;
        }
        for (File f : files) {
            if (f.isDirectory()) {
                Dirlist.add(f);
            } else {
                // 这里列出当前文件夹根目录下的所有文件,并添加到fileList列表中
                fileList.add(f);
                // System.out.println("file==>" + f);

            }
        }
    }


    /**
     * 文件检查
     */
    private void preReadCheck(String filePath) throws FileNotFoundException, FileFormatException {

        File file = new File(filePath);
        if (!file.exists()) {
            throw new FileNotFoundException("导入的文件不存在：" + filePath);
        }
        if (!(filePath.endsWith(EXTENSION_XLS) || filePath.endsWith(EXTENSION_XLSX))) {
            throw new FileFormatException("传入的文件不是excel");
        }
    }

    /**
     * 取得WorkBook对象
     * xls:HSSFWorkbook,03版
     * xlsx:XSSFWorkbook,07版
     */
    private Workbook getWorkbook(String filePath) throws IOException, InvalidFormatException {
        //直接判断后缀来返回相应的Workbook对象多数情况没问题，但是这个更保险，第3条已经说明
        Workbook wb = null;
        InputStream is = new FileInputStream(filePath);
        if (!is.markSupported()) {
            is = new PushbackInputStream(is, 8);
        }
        if (POIFSFileSystem.hasPOIFSHeader(is)) {
            return new HSSFWorkbook(is);
        }
        if (POIXMLDocument.hasOOXMLHeader(is)) {
            return new XSSFWorkbook(OPCPackage.open(is));
        }
        throw new IllegalArgumentException("您的excel版本目前不支持poi解析");
    }

    /**
     * 读取excel文件内容
     */
    public Map<Integer, List<String>> readExcel(String filePath, Integer sheetNum) throws IOException {
        // 检查和获取workbook对象
        this.preReadCheck(filePath);
        Workbook wb = null;
        Map<Integer, List<String>> map = new HashMap<Integer, List<String>>();
        try {
            wb = this.getWorkbook(filePath);
            // 默认只读取第一个sheet
            Sheet sheet = wb.getSheetAt(sheetNum);
            int rowcount = sheet.getLastRowNum();//逻辑行，包括空行
            int cellcount = sheet.getRow(0).getLastCellNum();//第一行（将来作为字段的行）有多少个单元格
            for (int i = 0; i <= rowcount; i++) {                    //这里用最原始的for循环来保证每行都会被读取
                List<String> list = new ArrayList<String>();
                Row row = sheet.getRow(i);
                if (null != row) {
                    cellcount = sheet.getRow(i).getLastCellNum();
                    for (int j = 0; j < cellcount; j++) {
                        list.add(getCellValue(row.getCell(j)));   //这里也是用for循环，用Cell c:row这样的遍历，空单元格就被抛弃了
                    }
                    //System.out.println("第" + (row.getRowNum() + 1) + "行数据：" + list.toString());
                    map.put(row.getRowNum(), list);
                } else {
                    for (int j = 0; j < cellcount; j++) {
                        list.add("无数据");
                    }
                    //System.out.println("第" + (i + 1) + "行数据：" + list.toString());
                    map.put(i, list);
                }
            }
        } catch (Exception e) {
            System.out.println("读取Excel异常：readExcel");
            File file3 = new File("C:\\person\\diff.txt");
            FileUtils.writeLines(file3, Collections.singleton(filePath), true);
        } finally {
            if (wb != null) {

            }
        }
        return map;
    }


    /**
     * 读取excel文件内容
     */
    public Map<Integer, List<String>> readExcel(String filePath, String sheetName) throws FileNotFoundException, FileFormatException {
        // 检查和获取workbook对象
        this.preReadCheck(filePath);
        Workbook wb = null;
        Map<Integer, List<String>> map = new HashMap<Integer, List<String>>();
        try {
            wb = this.getWorkbook(filePath);
            // 默认只读取第一个sheet
            Sheet sheet = wb.getSheet(sheetName);
            int rowcount = sheet.getLastRowNum();//逻辑行，包括空行
            int cellcount = sheet.getRow(0).getLastCellNum();//第一行（将来作为字段的行）有多少个单元格
            for (int i = 0; i <= rowcount; i++) {                    //这里用最原始的for循环来保证每行都会被读取
                List<String> list = new ArrayList<String>();
                Row row = sheet.getRow(i);
                if (null != row) {
                    cellcount = sheet.getRow(i).getLastCellNum();
                    for (int j = 0; j < cellcount; j++) {
                        list.add(getCellValue(row.getCell(j)));   //这里也是用for循环，用Cell c:row这样的遍历，空单元格就被抛弃了
                    }
                    //System.out.println("第" + (row.getRowNum() + 1) + "行数据：" + list.toString());
                    map.put(row.getRowNum(), list);
                } else {
                    for (int j = 0; j < cellcount; j++) {
                        list.add("无数据");
                    }
                    //System.out.println("第" + (i + 1) + "行数据：" + list.toString());
                    map.put(i, list);
                }
            }
        } catch (Exception e) {
            System.out.println("读取Excel异常：readExcel" );
        } finally {
            if (wb != null) {

            }
        }
        return map;
    }

    public Map<Integer, List<String>> readExcelLastRow(String filePath, String sheetName) throws FileNotFoundException, FileFormatException {
        // 检查和获取workbook对象
        this.preReadCheck(filePath);
        Workbook wb = null;
        Map<Integer, List<String>> map = new HashMap<Integer, List<String>>();
        try {
            wb = this.getWorkbook(filePath);
            // 默认只读取第一个sheet
            Sheet sheet = wb.getSheet(sheetName);
            int rowcount = sheet.getLastRowNum();//逻辑行，包括空行
            int cellcount = sheet.getRow(0).getLastCellNum();//第一行（将来作为字段的行）有多少个单元格
            List<String> list = new ArrayList<String>();
            Row row = sheet.getRow(rowcount);
            if (null != row) {
                cellcount = sheet.getRow(rowcount).getLastCellNum();
                for (int j = 0; j < cellcount; j++) {
                    list.add(getCellValue(row.getCell(j)));   //这里也是用for循环，用Cell c:row这样的遍历，空单元格就被抛弃了
                }
                //System.out.println("第" + (row.getRowNum() + 1) + "行数据：" + list.toString());
                map.put(row.getRowNum(), list);
            } else {
                for (int j = 0; j < cellcount; j++) {
                    list.add("无数据");
                }
                //System.out.println("第" + (i + 1) + "行数据：" + list.toString());
                map.put(rowcount, list);
            }
        } catch (Exception e) {
            System.out.println("读取Excel异常：" );
        } finally {
            if (wb != null) {

            }
        }
        return map;
    }

    /**
     * 读取excel文件内容
     */
    public Map<Integer, List<String>> readExcel(InputStream is, int sheetNum) {
        Workbook wb = null;
        Map<Integer, List<String>> map = new HashMap<Integer, List<String>>();
        try {
            wb = new XSSFWorkbook(OPCPackage.open(is));
            // 默认只读取第一个sheet
            Sheet sheet = wb.getSheetAt(sheetNum);
            int rowcount = sheet.getLastRowNum();//逻辑行，包括空行
            int cellcount = sheet.getRow(0).getLastCellNum();//第一行（将来作为字段的行）有多少个单元格
            for (int i = 0; i < rowcount + 1; i++) {                    //这里用最原始的for循环来保证每行都会被读取
                List<String> list = new ArrayList<String>();
                Row row = sheet.getRow(i);
                if (null != row) {
                    for (int j = 0; j < cellcount; j++) {
                        list.add(getCellValue(row.getCell(j)));   //这里也是用for循环，用Cell c:row这样的遍历，空单元格就被抛弃了
                    }
                    System.out.println("第" + (row.getRowNum() + 1) + "行数据：" + list.toString());
                    map.put(row.getRowNum(), list);
                } else {
                    for (int j = 0; j < cellcount; j++) {
                        list.add("");
                    }
                    System.out.println("第" + (i + 1) + "行数据：" + list.toString());
                    map.put(i, list);
                }
            }
        } catch (Exception e) {
            System.out.println("读取Excel异常：" + e.getMessage());
        } finally {
            if (wb != null) {

            }
        }
        return map;
    }

    /**
     * 取单元格的值
     */
    private String getCellValue(Cell c) {
        if (c == null) {
            return "";
        }
        String value = "";
        switch (c.getCellType()) {
            case HSSFCell.CELL_TYPE_NUMERIC://数字
                DecimalFormat df = new DecimalFormat("##.######");
                value = df.format(c.getNumericCellValue());
                break;
            case HSSFCell.CELL_TYPE_STRING://字符串
                value = c.getStringCellValue();
                break;
            case HSSFCell.CELL_TYPE_BOOLEAN://boolean
                value = c.getBooleanCellValue() + "";
                break;
            case HSSFCell.CELL_TYPE_FORMULA://公式
                value = c.getCellFormula() + "";
                break;
            case HSSFCell.CELL_TYPE_BLANK://空值
                value = "";
                break;
            case HSSFCell.CELL_TYPE_ERROR:
                value = "非法字符";
                break;
            default:
                value = "未知类型";
                break;
        }
        return value;
    }
}