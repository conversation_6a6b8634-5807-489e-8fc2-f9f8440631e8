package com.ld.clean.beans;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.annotation.JSONField;
import com.ld.clean.enums.CtOrgEnums;
import lombok.Data;

import java.util.Objects;

/**
 * <AUTHOR>
 */
@Data
public class FaEntity {

    @J<PERSON>NField(name = "KeyNo")
    private String keyNo;
    @J<PERSON><PERSON>ield(name = "Name")
    private String name;
    @JSONField(name = "Org")
    private Integer org;


    public FaEntity() {
    }

    public FaEntity(String name) {
        this.name = name;
        this.keyNo = "";
        this.org = CtOrgEnums.FA.getOrg();
    }

    public FaEntity(String keyNo, String name) {
        this.keyNo = keyNo;
        this.name = name;
        this.org = CtOrgEnums.FA.getOrg();
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof FaEntity)) {
            return false;
        }
        FaEntity invest = (FaEntity) o;
        return Objects.equals(getName(), invest.getName());
    }

    @Override
    public int hashCode() {
        return Objects.hash(getName());
    }

    public static void main(String[] args) {
        System.out.println(JSONObject.toJSONString(new FaEntity("AAA")));
    }

}
