package com.ld.clean.constants;

import lombok.Getter;


/**
 * <AUTHOR>
 * rmb金额等级
 */

@Getter
public enum SingleAmountLevelEnum {
    OTHER(Long.MAX_VALUE, Long.MIN_VALUE, 0, "未披露"),
    A(0L, 3000000L, 1, "小于300万"),
    B(3000000L, 10000000L, 2, "300-1000万"),
    C(10000000L, 30000000L, 3, "1000-3000万"),
    D(30000000L, 80000000L, 4, "3000-8000万"),
    E(80000000L, 150000000L, 5, "8000万-1.5亿"),
    F(150000000L, Long.MAX_VALUE, 6, "大于1.5亿");

    private Long min;
    private Long max;
    private Integer level;
    private String desc;

    SingleAmountLevelEnum(Long min, Long max, Integer level, String desc) {
        this.min = min;
        this.max = max;
        this.level = level;
        this.desc = desc;
    }

    public static Integer getLevelByMoney(Long money) {
        if (money == 0) {
            return OTHER.getLevel();
        }
        for (SingleAmountLevelEnum experLevelEnum : SingleAmountLevelEnum.values()) {
            if (money >= experLevelEnum.getMin() && money < experLevelEnum.getMax()) {
                return experLevelEnum.getLevel();
            }
        }
        return OTHER.getLevel();
    }
}

