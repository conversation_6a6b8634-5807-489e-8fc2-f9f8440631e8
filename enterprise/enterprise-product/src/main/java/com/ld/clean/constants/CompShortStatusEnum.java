package com.ld.clean.constants;

import lombok.Getter;

/**
 * 工商企业短状态
 *
 * <AUTHOR>
 */
@Getter
public enum CompShortStatusEnum {
    UNKNOWN(-1, "", 0),
    XIEYE(1, "歇业", 0),
    DIAOXIAOWEIZHUXIAO(2, "吊销，未注销", 0),
    ZHUXIAO(3, "注销", 0),
    CUNXU(4, "存续", 1),
    TINGYE(5, "停业", 0),
    QIANRU(6, "迁入", 1),
    DIAOXIAO(7, "吊销", 0),
    QITA(8, "其他", 0),
    QIANCHU(9, "迁出", 0),
    CHEIXIAO(10, "撤销", 0),
    ZHENGCHANG(11, "正常", 1),
    CHOUJIAN(12, "筹建", 1),
    QINGSUAN(13, "清算", 0),
    ZELINGGUANBI(14, "责令关闭", 0),
    ZAIYE(15, "在业", 1);

    private Integer code;
    private String desc;
    private Integer impact;


    CompShortStatusEnum(Integer value, String desc, Integer impact) {
        this.code = value;
        this.desc = desc;
        this.impact = impact;
    }

    public static CompShortStatusEnum getByDesc(String desc) {
        for (CompShortStatusEnum statusEnum : CompShortStatusEnum.values()) {
            if (desc.equals(statusEnum.getDesc())) {
                return statusEnum;
            }
        }
        return UNKNOWN;
    }

    public static CompShortStatusEnum getByCode(Integer code) {
        if (null != code) {
            for (CompShortStatusEnum statusEnum : CompShortStatusEnum.values()) {
                if (code.equals(statusEnum.getCode())) {
                    return statusEnum;
                }
            }
        }
        return UNKNOWN;
    }
}
