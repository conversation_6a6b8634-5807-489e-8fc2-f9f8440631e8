package com.ld.clean.constants;

import lombok.Getter;

/**
 * 交易所类型
 *
 * <AUTHOR>
 */
@Getter
public enum ExchangeEnums {
    /*
     *上海证券交易所
     */
    SH(101, "SH", "上海证券交易所"),
    /*
     *深圳证券交易所
     */
    SZ(102, "SZ", "深圳证券交易所"),
    /*
     *北京证券交易所
     */
    BJ(107, "BJ", "北京证券交易所"),
    /*
     *全国中小企业股份转让系统
     */
//    NQ(103, "NQ", "全国中小企业股份转让系统");
    NQ(103, "NQ", "新三板");

    private Integer code;
    private String name;
    private String desc;

    ExchangeEnums(Integer code, String name, String desc) {
        this.code = code;
        this.name = name;
        this.desc = desc;
    }

    /**
     * 当出现枚举值以外的数值时，给与默认值
     */
    public static ExchangeEnums getByCode(Integer code) {
        if (null != code && 0 != code) {
            for (ExchangeEnums exchangeEnums : ExchangeEnums.values()) {
                if (exchangeEnums.getCode().equals(code)) {
                    return exchangeEnums;
                }
            }
        }
        return null;
    }

}
