package com.ld.clean.beans;

import com.ld.clean.model.manageenterprisedb.CompProductCompetitorAdmin;
import com.ld.clean.model.manageenterprisedb.CompProductMemberAdmin;
import com.ld.clean.model.manageenterprisedb.CompProductNewsAdmin;
import com.ld.clean.pojo.mo.ct.MongoProductV2;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class ProductCleanRelateEntity extends ProductCleanBaseEntity {

    private MongoProductV2 mongoProductV2;

    private List<CompProductNewsAdmin> newsList = new ArrayList<>();
    private List<MongoProductV2.News> newsMongoList = new ArrayList<>();

    private List<CompProductMemberAdmin> memberList = new ArrayList<>();
    private List<MongoProductV2.Member> memberMongoList = new ArrayList<>();

    private List<CompProductCompetitorAdmin> competitorList = new ArrayList<>();
    private List<MongoProductV2.Competitor> competitorMongoList = new ArrayList<>();

}
