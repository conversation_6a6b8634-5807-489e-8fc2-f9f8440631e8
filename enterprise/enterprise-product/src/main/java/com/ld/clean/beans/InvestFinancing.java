/*
package com.ld.clean.beans;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.util.List;

*/
/**
 * <AUTHOR>
 *//*

@Data
public class InvestFinancing {

    @J<PERSON>NField(name = "TitleId")
    private String titleId;


    @JSONField(name = "Invest")
    private List<InvestEntity> invest;

    @JSONField(name = "LogoUrl")
    private String logoUrl;

    @JSONField(name = "PublishedTime")
    private String publishedTime;

    @JSONField(name = "Source")
    private String source;

    @JSONField(name = "Title")
    private String title;

    @JSONField(name = "ContentUrl")
    private String contentUrl;

    @JSONField(name = "DataStatus")
    private Integer dataStatus;


}
*/
