package com.ld.clean.util;

import com.base.clean.entity.company.dto.FetchCompanyInfoReq;
import com.base.clean.entity.company.dto.FetchCompanyNameByKeyNoReq;
import com.base.clean.utils.common.CompanyApiUtil;
import com.ld.clean.beans.QccCompany;
import com.ld.clean.context.ApplicationCoreContextManager;
import com.ld.clean.core.dto.company.Company;
import com.ld.clean.dto.company.IndustryV3Out;
import com.ld.clean.dto.company.QccCompanyOut;
import com.ld.clean.dto.englishtranslate.AreaOut;
import com.ld.clean.dto.englishtranslate.ContactInfoOut;
import com.ld.clean.dto.englishtranslate.WebSiteOut;
import com.ld.clean.dto.person.OperInfoOut;
import com.ld.clean.dto.usstock.CompUsskBaseinfo;
import com.ld.clean.enums.DimensionEnum;
import com.ld.clean.repository.managefinancialdb.CompUsskBaseinfoRepository;
import com.ld.clean.utils.CompanyDetailsUtil;
import com.ld.clean.utils.DateUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import tk.mybatis.mapper.entity.Condition;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 */
public class CompanyUtil {

    private static List<String> COMPANY_FIELDS = Arrays.asList("_id", "Name", "No", "BelongOrg", "Oper", "MultipleOper", "StartDate", "EndDate", "Status",
            "Province", "ShortStatus", "CreditCode", "RegistCapi", "EconKind", "Address", "IsExpired", "Scope", "ImageUrl", "OrgNo",
            "EnglishName", "Type", "TermStart", "TeamEnd", "CheckDate", "TaxNo", "RecCap", "IndustryV3", "OriginalName", "Employees",
            "Partners", "ContactInfo", "Area", "Tags", "Product", "IpoOpers", "IpoPartners", "IpoEmployees", "MoveDate");

//    private static final GlobalCompanyKeynoSearchV2SyncRepository globalCompanyKeynoSearchV2SyncRepository;
    private static final CompUsskBaseinfoRepository compUsskBaseinfoRepository;

    static {
//        globalCompanyKeynoSearchV2SyncRepository = ApplicationCoreContextManager.getInstance(GlobalCompanyKeynoSearchV2SyncRepository.class);
        compUsskBaseinfoRepository = ApplicationCoreContextManager.getInstance(CompUsskBaseinfoRepository.class);
    }

    public static QccCompany getCompanyMongo(String companyId) {
        List<QccCompanyOut> companyList = CompanyDetailsUtil.getCompanyList(Collections.singletonList(companyId), false, COMPANY_FIELDS);
        if (CollectionUtils.isNotEmpty(companyList)) {
            QccCompanyOut company = companyList.get(0);

            QccCompany qccCompany = new QccCompany();
            qccCompany.setId(company.getKeyNo());
            qccCompany.setName(company.getName());
            OperInfoOut oper = company.getOper();

            if (null != oper) {
                qccCompany.setCorporation(oper.getName());
            }
            qccCompany.setImageUrl(company.getImageUrl());

            IndustryV3Out industryV3 = company.getIndustryV3();
            if (null != industryV3) {
                qccCompany.setIndustryCode(industryV3.getIndustryCode());
                qccCompany.setSubIndustryCode(industryV3.getSubIndustryCode());
                qccCompany.setMiddleIndustryCode(industryV3.getMiddleCategoryCode());
                qccCompany.setSmallIndustryCode(industryV3.getSmallCategoryCode());
            }

            Long startTime = company.getStartDate();
            if (null != startTime && startTime > 0) {
                qccCompany.setStartTime(DateUtil.getDateOfTimestamp(startTime * 1000));
            }

            ContactInfoOut contactInfo = company.getContactInfo();
            if (null != contactInfo) {
                List<WebSiteOut> webSite = contactInfo.getWebSite();
                if (CollectionUtils.isNotEmpty(webSite)) {
                    qccCompany.setWebSite(webSite.get(0).getUrl());
                }
            }

            qccCompany.setProvince(company.getProvince());
            AreaOut area = company.getArea();
            if (null != area) {
                qccCompany.setCity(area.getCity());
            }
            return qccCompany;
        }
        return null;
    }

    public static Company getCompanyByName(String name) {
        Company company = CompanyApiUtil.getCompanyKeyNo(FetchCompanyInfoReq.builder().name(name).type(DimensionEnum.RONG_ZI_DONG_TAI.getType()).build());
        if (null != company && StringUtils.isNotBlank(company.getKeyNo())) {
            String nameByKeyNo = CompanyApiUtil.getCompanyNameByKeyNo(FetchCompanyNameByKeyNoReq.builder().keyNo(company.getKeyNo()).isValid(true).build());
            company.setName(nameByKeyNo);
        }
        return company;
    }

//        Company company = CompanyApiUtil.getCompanyKeyNo(FetchCompanyInfoReq.builder().name(name).type(DimensionEnum.RONG_ZI_DONG_TAI.getType()).build());
//            if (null != company && StringUtils.isNotEmpty(company.getKeyNo())) {
//                compName = company.getName();
//                compKeyno = company.getKeyNo();
//                compOrg = company.getOrg();
//            } else {
//                // 查询海外公司
//                List<GlobalCompanyKeynoSearchV2Sync> compList = getGlobalCompByName(name);
//                // 暂时不排除“消极”状态的公司
////                                List<String> globalCompStatusList = ProductUtils.getGlobalCompStatusByEmtion(1);
////                                compList = compList.stream()
////                                        .filter(comp -> globalCompStatusList.contains(comp.getEntStatus()))
////                                        .collect(Collectors.toList());
//                if (CollectionUtils.isNotEmpty(compList)) {
//                    compName = compList.get(0).getEntName();
//                    compKeyno = compList.get(0).getId();
//                    compOrg = CommonUtil.getOrgByKeyNo(compKeyno);
//                } else {
//                    // 查询美股企业
//                    List<CompUsskBaseinfo> list = getUskCompByName(name);
//                    if (CollectionUtils.isNotEmpty(list)) {
//                        CompUsskBaseinfo compUsskBaseinfo = list.get(0);
//                        compName = compUsskBaseinfo.getCompNameEn();
//                        compKeyno = compUsskBaseinfo.getRelatedComp();
//                        compOrg = CommonUtil.getOrgByKeyNo(compKeyno);
//                    }
//                }
//            }
//        }

//    public static List<GlobalCompanyKeynoSearchV2Sync> getGlobalCompByName(String name) {
//        Condition condition = new Condition(GlobalCompanyKeynoSearchV2Sync.class);
//        condition.createCriteria().andEqualTo("entName", name);
//        List<GlobalCompanyKeynoSearchV2Sync> list = globalCompanyKeynoSearchV2SyncRepository.selectByCondition(condition);
//        List<GlobalCompanyKeynoSearchV2Sync> collect = list.stream()
//                .filter(search -> search.getDataStatus() == 1)
//                .sorted(Comparator.comparing(GlobalCompanyKeynoSearchV2Sync::getId))
//                .collect(Collectors.toList());
////        if (CollectionUtils.isEmpty(collect)) {
////            condition.clear();
////            condition.createCriteria().andEqualTo("entNameSearch", StringUtil.evaluate(name));
////            list = globalCompanyKeynoSearchV2SyncRepository.selectByCondition(condition);
////            collect = list.stream()
////                    .filter(search -> search.getDataStatus() == 1)
////                    .sorted(Comparator.comparing(GlobalCompanyKeynoSearchV2Sync::getId))
////                    .collect(Collectors.toList());
////        }
//        return collect;
//    }

    public static List<CompUsskBaseinfo> getUskCompByName(String name) {
        Condition condition = new Condition(CompUsskBaseinfo.class);
        condition.createCriteria().andEqualTo("compNameEn", name);
        List<CompUsskBaseinfo> list = compUsskBaseinfoRepository.selectByCondition(condition);
        List<CompUsskBaseinfo> collect = list.stream()
                .filter(search -> search.getDataStatus() == 1)
                .collect(Collectors.toList());
//        if (CollectionUtils.isEmpty(collect)) {
//            condition.clear();
//            condition.createCriteria().andEqualTo("compSnameEn", name);
//            list = compUsskBaseinfoRepository.selectByCondition(condition);
//            collect = list.stream()
//                    .filter(search -> search.getDataStatus() == 1)
//                    .collect(Collectors.toList());
//        }
        return collect;
    }

}
