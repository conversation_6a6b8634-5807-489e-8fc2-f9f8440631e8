package com.ld.clean.constants;

import lombok.Getter;

/**
 * 企业变更类型
 *
 * <AUTHOR>
 */
@Getter
public enum CompanyChangeTypeEnum {

    OTHER(-1, "未知"),
    NO_CHANGE(0, "没有变更"),
    NAME_CHANGE(1, "名称变更"),
    CAPITAL_CHANGE(2, "资本变更"),
    PARTNER_CHANGE(3, "股东变更"),
    STATUS_CHANGE(4, "状态变更");

    private Integer code;

    private String desc;


    CompanyChangeTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static CompanyChangeTypeEnum getByCode(int code) {
        for (CompanyChangeTypeEnum roundLevelEnum : CompanyChangeTypeEnum.values()) {
            if (code == roundLevelEnum.getCode()) {
                return roundLevelEnum;
            }
        }
        return OTHER;
    }

}

