package com.ld.clean.constants;

import lombok.Getter;


/**
 * <AUTHOR>
 * 总融资金额对应产品分数
 */

@Getter
public enum FinanceTotalRmbScoreEnum {
    OTHER(Long.MAX_VALUE, Long.MIN_VALUE, 0, "未披露"),
    A(0L, 5000000L, 0.1f, "0-500W"),
    B(5000000L, 20000000L, 0.2f, "500W-2000W"),
    C(20000000L, 50000000L, 0.3f, "2000W-5000W"),
    D(50000000L, 100000000L, 0.4f, "5000W-1亿"),
    E(100000000L, 1000000000L, 0.5f, "1亿-10亿"),
    F(1000000000L, 10000000000L, 0.6f, "10亿-100亿"),
    G(10000000000L, 50000000000L, 0.7f, "100亿-500亿"),
    H(50000000000L, 100000000000L, 0.8f, "500亿-1000亿"),
    I(100000000000L, 300000000000L, 0.9f, "1000亿-3000亿"),
    J(300000000000L, Long.MAX_VALUE, 1f, "3000亿以上");

    private Long min;
    private Long max;
    private float score;
    private String desc;

    FinanceTotalRmbScoreEnum(Long min, Long max, float score, String desc) {
        this.min = min;
        this.max = max;
        this.score = score;
        this.desc = desc;
    }

    public static float getScoreByMoney(Long money) {
        if (money == 0) {
            return OTHER.getScore();
        }
        for (FinanceTotalRmbScoreEnum experLevelEnum : FinanceTotalRmbScoreEnum.values()) {
            if (money >= experLevelEnum.getMin() && money < experLevelEnum.getMax()) {
                return experLevelEnum.getScore();
            }
        }
        return OTHER.getScore();
    }
}

