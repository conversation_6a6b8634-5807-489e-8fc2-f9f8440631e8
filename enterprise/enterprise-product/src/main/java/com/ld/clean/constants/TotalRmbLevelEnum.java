package com.ld.clean.constants;

import lombok.Getter;


/**
 * <AUTHOR>
 * rmb金额等级
 */

@Getter
public enum TotalRmbLevelEnum {
    OTHER(Long.MAX_VALUE, Long.MIN_VALUE, 0, "未披露"),
    A(0L, 10000000L, 1, "小于1000万"),
    B(10000000L, 30000000L, 2, "1000-3000万"),
    C(30000000L, 100000000L, 3, "3000万-1亿"),
    D(100000000L, 500000000L, 4, "1-5亿"),
    E(500000000L, 1500000000L, 5, "5-15亿"),
    F(1500000000L, Long.MAX_VALUE, 6, "大于15亿");

    private Long min;
    private Long max;
    private Integer level;
    private String desc;

    TotalRmbLevelEnum(Long min, Long max, Integer level, String desc) {
        this.min = min;
        this.max = max;
        this.level = level;
        this.desc = desc;
    }

    public static Integer getLevelByMoney(Long money) {
        if (money == 0) {
            return OTHER.getLevel();
        }
        for (TotalRmbLevelEnum experLevelEnum : TotalRmbLevelEnum.values()) {
            if (money >= experLevelEnum.getMin() && money < experLevelEnum.getMax()) {
                return experLevelEnum.getLevel();
            }
        }
        return OTHER.getLevel();
    }
}

