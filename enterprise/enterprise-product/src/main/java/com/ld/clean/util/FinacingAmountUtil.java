package com.ld.clean.util;

import com.ld.clean.model.EpCtCurrency;
import com.ld.clean.utils.CtCurrencyUtils;
import joptsimple.internal.Strings;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 */
public class FinacingAmountUtil {

    public static final List<String> WORD_TO_REMOVE = Arrays.asList("约", "不超过", "超", "数");
    private static final Pattern NUM_REGRX = Pattern.compile("(\\d+(\\.\\d+)?)");
    private static Map<Integer, BigDecimal> unitMap = new HashMap<Integer, BigDecimal>() {{


        put(2, new BigDecimal(1000L));
        put(3, new BigDecimal(10000L));

        //临时优化处理
        put(10, new BigDecimal(100000L));

        put(20, new BigDecimal(1000000000L));
        put(21, new BigDecimal(10000000000L));
        put(22, new BigDecimal(100000000000L));

        put(4, new BigDecimal(1000000L));
        put(5, new BigDecimal(10000000L));
        put(6, new BigDecimal(100000000L));
        put(7, new BigDecimal(1000000000000L));
    }};

    public static final Set<String> FINACING_AMOUNT_ENUMS = new HashSet<>(Arrays.asList("-",
            "金额未和",
            "金额未披露",
            "金额未透露",
            "金额未知",
            "金额未知-",
            "金额未知金额",
            "金融未知",
            "未披露",
            "未透露",
            "未知"));

    public static final Set<String> FINACING_AMOUNT_SINGLE_NUMBER = new HashSet<>(Arrays.asList(
            "一",
            "二",
            "三",
            "四",
            "五",
            "六",
            "八",
            "九",
            "十",
            "壹",
            "两",
            "贰",
            "叁",
            "肆",
            "伍",
            "陆",
            "柒",
            "捌",
            "玖",
            "拾"));

    public static final Map<String, Integer> AMOUNT_CN_NUMBER_MAP = new HashMap<>();

    static {
        AMOUNT_CN_NUMBER_MAP.put("一", 1);
        AMOUNT_CN_NUMBER_MAP.put("二", 2);
        AMOUNT_CN_NUMBER_MAP.put("三", 3);
        AMOUNT_CN_NUMBER_MAP.put("四", 4);
        AMOUNT_CN_NUMBER_MAP.put("五", 5);
        AMOUNT_CN_NUMBER_MAP.put("六", 6);
        AMOUNT_CN_NUMBER_MAP.put("七", 7);
        AMOUNT_CN_NUMBER_MAP.put("八", 8);
        AMOUNT_CN_NUMBER_MAP.put("九", 9);
        AMOUNT_CN_NUMBER_MAP.put("十", 10);
        AMOUNT_CN_NUMBER_MAP.put("壹", 1);
        AMOUNT_CN_NUMBER_MAP.put("两", 2);
        AMOUNT_CN_NUMBER_MAP.put("贰", 2);
        AMOUNT_CN_NUMBER_MAP.put("叁", 3);
        AMOUNT_CN_NUMBER_MAP.put("肆", 4);
        AMOUNT_CN_NUMBER_MAP.put("伍", 5);
        AMOUNT_CN_NUMBER_MAP.put("陆", 6);
        AMOUNT_CN_NUMBER_MAP.put("柒", 7);
        AMOUNT_CN_NUMBER_MAP.put("捌", 8);
        AMOUNT_CN_NUMBER_MAP.put("玖", 9);
        AMOUNT_CN_NUMBER_MAP.put("拾", 10);
    }


    public static String getFinacingAmount(String amount) {
        if (StringUtils.isBlank(amount)) {
            return "未披露";
        }
        if (FINACING_AMOUNT_ENUMS.contains(amount.trim())) {
            return "未披露";
        }

        if (amount.trim().startsWith("0") && !amount.startsWith("0.")) {
            return "-";
        }
        return amount;
    }

    public static String getFinancingV2Amount(String amount) {
        if (StringUtils.isBlank(amount)) {
            return "";
        }
        if (FINACING_AMOUNT_ENUMS.contains(amount.trim())) {
            return "未披露";
        }
        if (amount.contains("人民币") && !amount.contains("元")) {
            return amount.replace("人民币", "元人民币");
        }
/*
        if (amount.trim().startsWith("0") && !amount.startsWith("0.")) {
            return amount;
        }*/
        return amount;
    }

    public static int getCNNumber(String amount) {
        if (StringUtils.isBlank(amount) || FINACING_AMOUNT_ENUMS.contains(amount)) {
            return 0;
        }
        char[] chars = amount.toCharArray();
        for (char c : chars) {
            if (FINACING_AMOUNT_SINGLE_NUMBER.contains(String.valueOf(c))) {
                return AMOUNT_CN_NUMBER_MAP.get(String.valueOf(c));
            }
        }
        return 1;
    }

    public static String getConvertAmount(BigDecimal volume1, int scale) {
        if (volume1 == null) {
            return Strings.EMPTY;
        }

        String symbol = volume1.compareTo(BigDecimal.ZERO) < 0 ? "-" : Strings.EMPTY;
        BigDecimal volume = new BigDecimal(Math.abs(volume1.doubleValue())).setScale(10, RoundingMode.HALF_UP);
        if (volume.compareTo(BigDecimal.valueOf(100000000L)) >= 0) {
            return symbol + volume.divide(BigDecimal.valueOf(100000000L), scale, RoundingMode.HALF_UP).toPlainString() + "亿".trim();
        } else if (volume.compareTo(BigDecimal.valueOf(10000L)) >= 0) {
            return symbol + volume.divide(BigDecimal.valueOf(10000L), scale, RoundingMode.HALF_UP).toPlainString() + "万".trim();
        } else {
            return symbol + volume.setScale(scale, RoundingMode.HALF_UP).toPlainString().trim();
        }
    }

    /**
     * 量纲转换
     * 估值和金额处理  估值截断小数
     * flag   true : 估值  false : 金额
     */
    public static String handleDimensionAmount(String amount, Boolean flag) {
        if (StringUtils.isBlank(amount) || "未披露".equals(amount)) {
            return "";
        }
        for (String word : WORD_TO_REMOVE) {
            if (amount.contains(word)) {
                return amount;
            }
        }
        EpCtCurrency currency = CtCurrencyUtils.getEpCtCurrencyByCapital(amount);
        String res = "";
        if (StringUtils.isBlank(currency.getAmount())) {
            return amount;
        } else {
            String msg = currency.getMsg();
            BigDecimal s = new BigDecimal(currency.getAmount()).multiply(unitMap.getOrDefault(currency.getUnitCode(), BigDecimal.ONE)).setScale(4, RoundingMode.HALF_UP);

            if (s.compareTo(new BigDecimal("1000000000000")) >= 0) {
                s = s.divide(new BigDecimal("1000000000000"), 4, RoundingMode.DOWN).stripTrailingZeros();
                res = s.toPlainString();
                if (flag && res.contains(".")) {
                    res = res.replaceAll("\\..*", "");
                }
                res = res + "万亿";

            } else if (s.compareTo(new BigDecimal("100000000")) >= 0) {
                s = s.divide(new BigDecimal("100000000"), 4, RoundingMode.DOWN).stripTrailingZeros();
                res = s.toPlainString();
                if (flag && res.contains(".")) {
                    res = res.replaceAll("\\..*", "");
                }
                res = res + "亿";

            } else if (s.compareTo(new BigDecimal("10000")) >= 0) {
                s = s.divide(new BigDecimal("10000"), 4, RoundingMode.DOWN).stripTrailingZeros();
                res = s.toPlainString();
                if (flag && res.contains(".")) {
                    res = res.replaceAll("\\..*", "");
                }
                res = res + "万";
            } else {
                s = s.divide(new BigDecimal("1"), 4, RoundingMode.DOWN).stripTrailingZeros();
                res = s.toPlainString();
            }
            res = res + currency.getCurrency();
            if (res.contains("港元")) {
                res = res.replace("港元", "港币");
            }
            //补元
            if (res.contains("人民币") && !res.contains("元")) {
                res = res.replace("人民币", "元人民币");
            }
            //特殊处理的金额单位
            String[] countries = {"印度", "俄罗斯", "巴西", "南非", "加坡", "墨西哥"};
            for (String country : countries) {
                res = res.replace(country, "");
            }
            if (StringUtils.isNotBlank(msg) && msg.contains("不存在")) {
                return amount;
            }
        }
        return res;
    }

    /**
     * 估值计算
     */
    public static String divideAmountByRate(String amount, String rate) {
        rate = rate.replace("%", "").trim();
        EpCtCurrency currency = CtCurrencyUtils.getEpCtCurrencyByCapital(amount);
        BigDecimal decimalAmount = new BigDecimal(currency.getAmount());
        BigDecimal decimalRate = new BigDecimal(rate).divide(new BigDecimal("100"), 8, RoundingMode.DOWN);
        BigDecimal valuation = decimalAmount.divide(decimalRate, 2, RoundingMode.DOWN);
        return valuation + currency.getUnit() + currency.getCurrency();
    }

    /**
     * rate处理
     */
    public static String handleRate(String rate) {
        if (StringUtils.isBlank(rate)) {
            return rate;
        }
        rate = rate.replace(" ", "").replace("%", "").trim();
        try{
            BigDecimal number = new BigDecimal(rate);
            // 保留两位小数并截断
            number = number.setScale(2, RoundingMode.DOWN).stripTrailingZeros();
            if ("0".equals(number.toPlainString())) {
                return "0%";
            }
            return number.toPlainString() + "%";
        }catch (Exception e){
            e.getStackTrace();
        }
       return "";
    }

    public static String handleRateV2(String rate) {
        /*if (StringUtils.isBlank(rate)) {
            return rate;
        }
        rate = rate.replace(" ", "").replace("%", "").trim();
        try{
            BigDecimal number = new BigDecimal(rate);
            // 保留两位小数并截断
            number = number.setScale(2, RoundingMode.DOWN).stripTrailingZeros();
            if ("0".equals(number.toPlainString())) {
                return "0%";
            }
            return number.toPlainString() + "%";
        }catch (Exception e){
            e.getStackTrace();
        }
        return "";*/
        if(StringUtils.isBlank(rate)){
            return "";
        }
        String cleaned = rate.replace(" ", "").replace("%", "").replace("％","").trim();

        try {
            BigDecimal number = new BigDecimal(cleaned);

            // 检查是否为0
            if (number.compareTo(BigDecimal.ZERO) == 0) {
                return "";
            }

            // 获取绝对值，用于判断
            BigDecimal absNumber = number.abs();

            // 情况1：整数部分及小数前四位为0时采用精确数字，去掉尾部0
            if (absNumber.compareTo(new BigDecimal("0.0001")) < 0) {
                // 去除尾部0和小数点（如果后面没有数字）
                String plain = number.stripTrailingZeros().toPlainString();
                return plain + "%";
            }

            // 情况2：整数部分及小数前两位为0时保留4位小数（截断）
            if (absNumber.compareTo(new BigDecimal("0.01")) < 0) {
                BigDecimal truncated = number.setScale(4, RoundingMode.DOWN);
                // 去除可能产生的尾部0
                String result = truncated.toPlainString();
                return result + "%";
            }

            // 情况3：其他情况保留2位小数（截断）
            BigDecimal truncated = number.setScale(2, RoundingMode.DOWN);
            // 去除可能产生的尾部0（如1.00变成1）
            String result = truncated.stripTrailingZeros().toPlainString();
            // 但根据要求，其他情况要保留2位小数，所以这里特殊处理
            if (!result.contains(".")) {
                return result + ".00%";
            } else if (result.split("\\.")[1].length() < 2) {
                return result + "0%";
            }
            return result + "%";

        } catch (NumberFormatException e) {
            // 如果解析失败，返回原字符串
            return rate;
        }


    }


    //定向增发的金额优化
    public static String handleDimensionWithAmount(String amountStr){
        if(StringUtils.isBlank(amountStr)){
            return  "";
        }

        String amoutString = amountStr.replace(",", "").replace("，", "");
        Matcher m = NUM_REGRX.matcher(amoutString);
        String newValue ="";
        String unit ="";
        if (m.find()) {
          String amount =  m.group(0);
          String currency =  amountStr.replace(m.group(0),"");
          if(currency.contains("人民币") &&!currency.contains("元人民币")  ){
              currency = currency.replace("人民币","元人民币");
          }
          if (new BigDecimal(amount).compareTo(new BigDecimal("1000000000000")) >= 0) {
              newValue =   new BigDecimal(amount).divide(new BigDecimal("1000000000000"),2,RoundingMode.HALF_UP).toPlainString();
              unit ="万亿";
          }else  if (new BigDecimal(amount).compareTo(new BigDecimal("100000000")) >= 0) {
              newValue =   new BigDecimal(amount).divide(new BigDecimal("100000000"),2,RoundingMode.HALF_UP).toPlainString();
              unit ="亿";
          }else if (new BigDecimal(amount).compareTo(new BigDecimal("10000")) >= 0) {
              newValue =   new BigDecimal(amount).divide(new BigDecimal("10000"),2,RoundingMode.HALF_UP).toPlainString();
              unit ="万";
          }
          //.00 要保留整数 .50 的零要保留
          if(newValue.endsWith(".00")){
              newValue =newValue.replace(".00","");
          }
          return newValue+unit+currency;



            // epCtCurrency.setUnitAndCurrency(amoutString.replace(m.group(0),""));
        }
        return "";
    }





    //金额保留小数位逻辑
    public static String handleScale(String amountStr){
        if(StringUtils.isBlank(amountStr)){
            return "";
        }
        // 提取字符串中的数字部分
        String numberPart = amountStr.replaceAll("[^\\d.]", "");
        String unit = amountStr.replaceAll(numberPart, "");
        if(!numberPart.contains(".")){
            return amountStr;
        }
        BigDecimal number = new BigDecimal(numberPart);

        // 判断是否大于1
        if (number.compareTo(BigDecimal.ONE) <= 0) {
            String strippedString = number.stripTrailingZeros().toPlainString();
            String[] parts = strippedString.split("\\.");
            if (parts.length == 1 || (parts.length == 2 && parts[1].isEmpty())) {
                return strippedString+unit;
            } else if (parts[1].length() >= 4 && parts[1].substring(0, 4).matches("0+")) {
                // 如果小数前四位都是0，则去掉尾部0
                return number.stripTrailingZeros()+unit;
            } else if (parts[1].length() >= 2 && parts[1].substring(0, 2).matches("0+")) {
                // 如果小数前两位都是0，则保留四位小数，不足补0
                return number.setScale(4, RoundingMode.DOWN).toPlainString()+unit;
            }
        }
        return number.setScale(2, RoundingMode.DOWN).toPlainString() + unit;
    }

    /**
     * 如果首位为中文，替换为数字
     * @param amount
     * @return
     */
    public static String handleFirstCnAmount(String amount) {
        if (StringUtils.isBlank(amount)) {
            return amount;
        }
        char firstChar = amount.charAt(0);
        int number = -1;

        // 检查数字
        if (Character.isDigit(firstChar)) {
            return amount;
        }

        // 检查中文数字
        String firstCharStr = String.valueOf(firstChar);
        if (AMOUNT_CN_NUMBER_MAP.containsKey(firstCharStr)) {
            number = AMOUNT_CN_NUMBER_MAP.get(firstCharStr);
        }

        // 替换首位并返回新字符串
        if (number != -1) {
            return number + amount.substring(1);
        } else {
            return amount;
        }

    }

}
