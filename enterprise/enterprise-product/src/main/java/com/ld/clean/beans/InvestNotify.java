package com.ld.clean.beans;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

/**
 * 通知刷新投资机构
 *
 * <AUTHOR>
 */
@Data
public class InvestNotify {

    public InvestNotify(String productId) {
        projectRealtime = new ProjectRealtime();
        projectRealtime.setProductId(productId);
    }

    @JSONField(name = "project_realtime")
    private ProjectRealtime projectRealtime;

    @Data
    public static class ProjectRealtime {
        @JSONField(name = "product_id")
        private String productId;
    }
}
