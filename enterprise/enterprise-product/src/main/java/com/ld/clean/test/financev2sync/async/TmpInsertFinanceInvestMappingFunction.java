package com.ld.clean.test.financev2sync.async;

import com.google.common.collect.Lists;
import com.ld.clean.context.ApplicationCoreContextManager;
import com.ld.clean.core.base.BaseModel;
import com.ld.clean.dao.model.basecleandatatmp.ScSeFinanceInvestMapping20240320;
import com.ld.clean.dao.repository.basecleandatatmp.ScSeFinanceInvestMapping20240320Repository;
import com.ld.clean.model.manageenterprisedb.FinanceInvestMapping;
import com.ld.clean.parent.AsyncCleanParentFunction;
import com.ld.clean.utils.DateUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.flink.configuration.Configuration;
import tk.mybatis.mapper.entity.Condition;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
public class TmpInsertFinanceInvestMappingFunction extends AsyncCleanParentFunction<List<ScSeFinanceInvestMapping20240320>, List<ScSeFinanceInvestMapping20240320>> {

    private static ScSeFinanceInvestMapping20240320Repository scSeFinanceInvestMapping20240320Repository;

    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);
        scSeFinanceInvestMapping20240320Repository = ApplicationCoreContextManager.getInstance(ScSeFinanceInvestMapping20240320Repository.class);
    }

    public TmpInsertFinanceInvestMappingFunction(int corePoolSize) {
        super(corePoolSize);
    }

    @Override
    protected List<ScSeFinanceInvestMapping20240320> invoke(List<ScSeFinanceInvestMapping20240320> list) throws Exception {
        List<ScSeFinanceInvestMapping20240320> result = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(list)) {
            Lists.partition(list, 300).forEach(ships -> {
                int updateCount = scSeFinanceInvestMapping20240320Repository.insertBatch(ships);
                if (updateCount == ships.size()) {
                    result.addAll(ships);
                } else {
                    if (updateCount != 0) {
                        List<String> ids = list.stream().map(BaseModel::getId).collect(Collectors.toList());
                        Condition condition = new Condition(FinanceInvestMapping.class);
                        //查询10分钟内有更新的数据
                        condition.createCriteria().andIn("id", ids)
                                .andGreaterThan("updateDate", DateUtil.getDateOfTimestamp(System.currentTimeMillis() - 600000));
                        List<ScSeFinanceInvestMapping20240320> updateList = scSeFinanceInvestMapping20240320Repository.selectByCondition(condition);
                        result.addAll(updateList);
                    }
                }
            });
        }
        return result;
    }

}
