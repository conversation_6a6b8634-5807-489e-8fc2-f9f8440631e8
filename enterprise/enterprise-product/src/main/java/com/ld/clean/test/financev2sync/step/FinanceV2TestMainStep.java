package com.ld.clean.test.financev2sync.step;

import com.ld.clean.constants.MyConstants;
import com.ld.clean.step.MainParentStep;
import com.ld.clean.step.StepConstructorParam;
import com.ld.clean.util.KafkaBuildUtil;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.connectors.kafka.FlinkKafkaConsumer;

/**
 * <AUTHOR>
 * @date 2021年9月9日 15点13分
 */
public class FinanceV2TestMainStep extends MainParentStep {
    private StepConstructorParam stepConstructorParam;

    public FinanceV2TestMainStep(StepConstructorParam stepConstructorParam) {
        this.stepConstructorParam = stepConstructorParam;
    }

    @Override
    protected DataStream<String> getSpiderSourceData() {
        stepConstructorParam.getEnv().setParallelism(1);
        FlinkKafkaConsumer<String> consumer = KafkaBuildUtil.buildConsumer(
                "base_tmp_liupf",
                "group_base_product_baseinfo_data_test");

        return stepConstructorParam.getEnv().addSource(consumer
                .setStartFromTimestamp(System.currentTimeMillis())
                .setCommitOffsetsOnCheckpoints(true))
                .setParallelism(2);


        //final CompTrackInfoXnRepository compTrackInfoXnRepository = ApplicationCoreContextManager.getInstance(CompTrackInfoXnRepository.class);
        //return stepConstructorParam.getEnv().fromCollection(Collections.singletonList(JSON.toJSONString(new HashMap<String, List<CompTrackInfoXn>>(){{put("comp_track_info_xn",compTrackInfoXnRepository.selectByPrimaryKeyList(Arrays.asList("64eb0f14e2b647278e0c63dfc01b231c")));}})));
        //    final CompTrackInfoCompetitionMappingRepository compTrackInfoCompetitionMappingRepository = ApplicationCoreContextManager.getInstance(CompTrackInfoCompetitionMappingRepository.class);
        //   return stepConstructorParam.getEnv().fromCollection(Collections.singletonList(JSON.toJSONString(new HashMap<String, List<CompTrackInfoCompetitionMapping>>(){{put("comp_track_info_competition_mapping",compTrackInfoCompetitionMappingRepository.selectByPrimaryKeyList(Arrays.asList("a671e9c61fcf0f1fbbe52f972699256e")));}})));

    }

    @Override
    protected DataStream<String> getDapSourceData() {
      /*  return stepConstructorParam.getEnv().addSource(KafkaHelper.buildAuthKafkaSource(CleanFlinkKafkaConsumer.builder()
                .topic(MyConstants.DAP_CLEAN_SEC_OTCEE_REPORT_BASICINFO_KAFKA_TOPIC)
                .groupId(MyConstants.DAP_CLEAN_SEC_OTCEE_REPORT_BASICINFO_KAFKA_GROUPID).build())
                .setCommitOffsetsOnCheckpoints(true)).setParallelism(1);*/
        return null;
    }

    @Override
    protected void spiderStep(DataStream<String> dataStream) {
        //  int spiderProcessParallelism = stepConstructorParam.getDmpParams().getInt(MyConstants.SPIDER_PROCESS_PARALLELISM, stepConstructorParam.getPropertiesParams().getInt(PropertiesConstants.STREAM_PARALLELISM));
        stepConstructorParam.getEnv().setParallelism(2);
        int corePoolSize = MyConstants.CORE_POOL_SIZE_DEFAULT;
        stepConstructorParam.setCorePoolSize(20);
        new FinanceV2TestSpiderStep(stepConstructorParam).process(dataStream);

    }

    @Override
    protected void dapStep(DataStream<String> dataStream) {
        stepConstructorParam.getEnv().setParallelism(1);
        //  new IpoPlaceFinanceDapStep(stepConstructorParam, "").process(dataStream);

    }
}
