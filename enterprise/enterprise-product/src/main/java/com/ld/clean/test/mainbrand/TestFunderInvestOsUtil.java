package com.ld.clean.test.mainbrand;

import com.ld.clean.constants.InvestTypeEnum;
import com.ld.clean.test.mainbrand.dao.InvestEntityTest;
import com.ld.clean.utils.CommonUtil;
import org.apache.commons.lang3.StringUtils;

import java.util.*;


/**
 * <AUTHOR>
 */
public class TestFunderInvestOsUtil {

    // 给投资机构排序，机构-有限合伙-公司-个人 ，可跳转-不可跳转,领投-跟投
    public static List<InvestEntityTest> sortInvestment(Set<InvestEntityTest> investList) {
        List<InvestEntityTest> list = new ArrayList<>();
        List<InvestEntityTest> relatedInvest = new ArrayList<>();
        List<InvestEntityTest> unRelatedInvest = new ArrayList<>();
        List<InvestEntityTest> relatedLP = new ArrayList<>();
        List<InvestEntityTest> unRelatedLP = new ArrayList<>();
        List<InvestEntityTest> relatedCompany = new ArrayList<>();
        List<InvestEntityTest> unRelatedCompany = new ArrayList<>();
        List<InvestEntityTest> person = new ArrayList<>();
        List<InvestEntityTest> product = new ArrayList<>();// 该类别待废弃
        List<InvestEntityTest> other = new ArrayList<>();
        for (InvestEntityTest InvestEntityTest : investList) {
            String investName = getFinacingInvestName(InvestEntityTest.getName());
            if (StringUtils.equalsIgnoreCase(investName, "未披露")) {
                continue;
            }
            InvestEntityTest.setName(investName);
            if (InvestEntityTest.getCategory() == InvestTypeEnum.INSTITUTION.getCode()) {
                if (StringUtils.isEmpty(InvestEntityTest.getId())) {
                    unRelatedInvest.add(InvestEntityTest);
                } else {
                    relatedInvest.add(InvestEntityTest);
                }
            } else if (InvestEntityTest.getCategory() == InvestTypeEnum.COMPANY.getCode()) {
                if (StringUtils.isEmpty(InvestEntityTest.getId()) && StringUtils.isEmpty(InvestEntityTest.getKeyNo())) {
                    if (InvestEntityTest.getName().contains("有限合伙")) {
                        unRelatedLP.add(InvestEntityTest);
                    } else {
                        unRelatedCompany.add(InvestEntityTest);
                    }
                } else {
                    if (InvestEntityTest.getName().contains("有限合伙")) {
                        relatedLP.add(InvestEntityTest);
                    } else {
                        relatedCompany.add(InvestEntityTest);
                    }
                }
            } else if (InvestEntityTest.getCategory() == InvestTypeEnum.PERSON.getCode()) {
                person.add(InvestEntityTest);
            } else if (InvestEntityTest.getCategory() == InvestTypeEnum.PRODUCT.getCode()) {
                product.add(InvestEntityTest);
            } else if (InvestEntityTest.getCategory() == InvestTypeEnum.OTHER.getCode()) {
                other.add(InvestEntityTest);
            }
        }
        Collections.sort(relatedInvest, Comparator.comparing(InvestEntityTest::getType));
        Collections.sort(relatedLP, Comparator.comparing(InvestEntityTest::getType));
        Collections.sort(relatedCompany, Comparator.comparing(InvestEntityTest::getType));
        Collections.sort(unRelatedInvest, Comparator.comparing(InvestEntityTest::getType));
        Collections.sort(unRelatedLP, Comparator.comparing(InvestEntityTest::getType));
        Collections.sort(unRelatedCompany, Comparator.comparing(InvestEntityTest::getType));
        Collections.sort(person, Comparator.comparing(InvestEntityTest::getType));
        Collections.sort(product, Comparator.comparing(InvestEntityTest::getType));
        Collections.sort(other, Comparator.comparing(InvestEntityTest::getType));
        list.addAll(relatedInvest);
        list.addAll(relatedLP);
        list.addAll(relatedCompany);
        list.addAll(unRelatedInvest);
        list.addAll(unRelatedLP);
        list.addAll(unRelatedCompany);
        list.addAll(person);
        list.addAll(product);
        list.addAll(other);
        return list;
    }

    public static final Set<String> FINACING_INVEST_NAME_ENUMS = new HashSet<>(Arrays.asList("-",
            "投资机构未知",
            "未披露",
            "未知",
            "投资方未知",
            "投资人未知",
            "未透露",
            "投资者未知",
            "投资方未透露",
            "暂未透露"
    ));

    public static String getFinacingInvestName(String investName) {
        if (StringUtils.isBlank(investName)) {
            return "未披露";
        }
        if (FINACING_INVEST_NAME_ENUMS.contains(investName)) {
            return "未披露";
        }

        return CommonUtil.getCompanyNameByName(investName);
    }

}
