package com.ld.clean.beans;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

@Data
public class IndustryV3 {
    /**
     * 行业门类code
     */
    @JSONField(name = "IndustryCode")
    private String industryCode;

    /**
     * 行业门类描述
     */
    @JSONField(name = "Industry")
    private String industry;

    /**
     * 行业大类code
     */
    @JSONField(name = "SubIndustryCode")
    private String subIndustryCode;

    /**
     * 行业大类描述
     */
    @JSONField(name = "SubIndustry")
    private String subIndustry;

    @JSONField(name = "MiddleCategoryCode")
    private String middleCategoryCode;

    @JSONField(name = "MiddleCategory")
    private String middleCategory;

    @JSONField(name = "SmallCategoryCode")
    private String smallCategoryCode;

    @JSONField(name = "SmallCategory")
    private String smallCategory;
}
