package com.ld.clean.constants;

import lombok.Getter;

/**
 *企业阶段
 *
 * <AUTHOR>
 */
@Getter
public enum CompStageEnum {
    UNKNOW(0, "未披露"),
    CHU_CHUANG_QI(1, "初创期"),
    CHENG_ZHANG_QI(2, "成长期"),
    CHENG_SHU_QI(3, "成熟期"),
    ;

    private Integer code;

    private String desc;


    CompStageEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static CompStageEnum getByCode(int code) {
        for (CompStageEnum roundLevelEnum : CompStageEnum.values()) {
            if (code == roundLevelEnum.getCode()) {
                return roundLevelEnum;
            }
        }
        return UNKNOW;
    }

}

