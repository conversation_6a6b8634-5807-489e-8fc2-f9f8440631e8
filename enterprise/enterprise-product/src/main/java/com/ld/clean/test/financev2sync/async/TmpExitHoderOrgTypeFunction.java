package com.ld.clean.test.financev2sync.async;


import com.ld.clean.context.ApplicationCoreContextManager;
import com.ld.clean.dao.model.manageenterprisedb.IpoHistoryBaseinfo;
import com.ld.clean.dao.repository.manageenterprisedb.IpoHistoryBaseinfoRepository;
import com.ld.clean.model.manageenterprisedb.DapIpoBookExit;
import com.ld.clean.parent.AsyncCleanParentFunction;
import com.ld.clean.repository.manageenterprisedb.DapIpoBookExitRepository;
import com.ld.clean.test.mainbrand.dao.mergerexit.ScSeIpoExitBaseinfo20250610;
import com.ld.clean.util.ParticipantUtil;
import com.ld.clean.util.ct.CtStringUtil;
import com.ld.clean.utils.CommonUtil;
import org.apache.commons.lang3.StringUtils;
import tk.mybatis.mapper.entity.Condition;

import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @description：TODO
 * @author： liupf
 * @create： 2025/7/28 10:57
 */
public class TmpExitHoderOrgTypeFunction extends AsyncCleanParentFunction<ScSeIpoExitBaseinfo20250610, ScSeIpoExitBaseinfo20250610> {

    public TmpExitHoderOrgTypeFunction(int corePoolSize) {
        super(corePoolSize);
    }

    private static final IpoHistoryBaseinfoRepository ipoHistoryBaseinfoRepository = ApplicationCoreContextManager.getInstance(IpoHistoryBaseinfoRepository.class);
    private static final DapIpoBookExitRepository dapIpoBookExitRepository = ApplicationCoreContextManager.getInstance(DapIpoBookExitRepository.class);


    @Override
    protected ScSeIpoExitBaseinfo20250610 invoke(ScSeIpoExitBaseinfo20250610 exit) throws Exception {

        if (StringUtils.isBlank(exit.getSymbol())) {
            return null;
        }
        //获取来源表  采编数据匹配

        String id = exit.getSpiderId();
        if(StringUtils.isBlank(id)){
            return null;
        }
        DapIpoBookExit dapIpoBookExit = (DapIpoBookExit)dapIpoBookExitRepository.selectByPrimaryKey(id);

        if(null!= dapIpoBookExit && null != dapIpoBookExit.getHolderOrgType()){
            exit.setHolderOrgType(dapIpoBookExit.getHolderOrgType());
        }else {
            exit.setHolderOrgType(0);
        }


        Condition histroyBaseinfo = new Condition(IpoHistoryBaseinfo.class);
        histroyBaseinfo.createCriteria().andEqualTo("symbol", exit.getSymbol()).andEqualTo("dataStatus", 1);
        List<IpoHistoryBaseinfo> ipoHistoryBaseinfos = ipoHistoryBaseinfoRepository.selectByCondition(histroyBaseinfo);

        List<IpoHistoryBaseinfo> ipoHistoryBaseinfoList = ipoHistoryBaseinfos.stream().filter(it -> it.getDataStatus() == 1 && getFormatHolderNames(exit.getHolderName()).contains(it.getHolderName())).collect(Collectors.toList());
        int type = ParticipantUtil.changeHolderOrgTypeByKeywordsV2(exit.getHolderOrgType(), exit.getHolderName(), exit.getHolderKeyno(), ipoHistoryBaseinfoList);

        exit.setHolderOrgType(type);

        return exit;
    }

    private Set<String> getFormatHolderNames(String name) {
        Set<String> names = new HashSet<>();
        // 兼容机构当前别名处理规则
        if (StringUtils.isNotBlank(name)) {
            names.add(name.trim());
            if (CommonUtil.iscontainsChinese(name)) {
                names.add(CtStringUtil.handleCompanyName(name));
                // 创投名称统一处理
                names.add(CtStringUtil.formatCtName(name));
            }

            // 投资方名称繁简体处理
            names.add(CtStringUtil.getKeywordByInvestName(name));

            // 全半教转化
            names.add(CommonUtil.full2Half(name));
            names.add(CommonUtil.half2Full(name));
        }
        return names;

    }
}
