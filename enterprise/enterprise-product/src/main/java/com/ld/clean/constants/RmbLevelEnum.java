package com.ld.clean.constants;

import lombok.Getter;


/**
 * <AUTHOR>
 * rmb金额等级
 */

@Getter
public enum RmbLevelEnum {
    OTHER(Long.MAX_VALUE, Long.MIN_VALUE, 0, "未披露"),
    A(0L, 100000000L, 1, "0-1亿"),
    B(100000000L, 500000000L, 2, "1-5亿"),
    C(500000000L, 1000000000L, 3, "5-10亿"),
    D(1000000000L, 5000000000L, 4, "10-50亿"),
    E(5000000000L, 10000000000L, 5, "50-100亿"),
    F(10000000000L, Long.MAX_VALUE, 6, "100亿以上");

    private Long min;
    private Long max;
    private Integer level;
    private String desc;

    RmbLevelEnum(Long min, Long max, Integer level, String desc) {
        this.min = min;
        this.max = max;
        this.level = level;
        this.desc = desc;
    }

    public static Integer getLevelByMoney(Long money) {
        if (money == 0) {
            return OTHER.getLevel();
        }
        for (RmbLevelEnum experLevelEnum : RmbLevelEnum.values()) {
            if (money >= experLevelEnum.getMin() && money < experLevelEnum.getMax()) {
                return experLevelEnum.getLevel();
            }
        }
        return OTHER.getLevel();
    }
}

