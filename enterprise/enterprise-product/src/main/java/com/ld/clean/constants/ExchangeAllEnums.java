package com.ld.clean.constants;

import lombok.Getter;

/**
 * 交易所类型
 *
 * <AUTHOR>
 */
@Getter
public enum ExchangeAllEnums {
    E101(101, "上海证券交易所"),
    E102(102, "深圳证券交易所"),
    E103(103, "全国中小企业股份转让系统"),
    E104(104, "区域性股权交易市场"),
    E105(105, "全国银行间同业拆借中心"),
    E106(106, "柜台交易市场"),
    E107(107, "北京证券交易所"),
    E201(201, "香港交易所"),
    E301(301, "纳斯达克交易所"),
    E302(302, "纽约证券交易所"),
    E303(303, "美国证券交易所"),
    E304(304, "美国粉单市场"),
    E401(401, "台湾交易所"),
    E402(401, "台湾证券交易所"),
    E501(501, "新加坡交易所"),
    E502(501, "新加坡证券交易所"),
    E601(601, "伦敦证券交易所"),
    E701(701, "公募基金场外市场"),
    E801(801, "德国法兰克福证券交易所"),
    E901(901, "澳大利亚证券交易所"),
    E1001(1001, "瑞士证券交易所"),
    E1101(1101, "加拿大多伦多证券交易所"),
    E1102(1102, "加拿大CSE证券交易所"),
    E1201(1201, "日本东京证券交易所"),
    E1301(1301, "奥地利维也纳证券交易所"),
    E1401(1401, "巴黎纽约泛欧证券交易所"),
    E1501(1501, "韩国证券交易所"),
    E99901(99901, "爱尔兰证券交易所"),
    E99902(99902, "印度国家证券交易所"),
    E99903(99903, "赫尔辛基证券交易所"),
    E99904(99904, "阿姆斯特丹泛欧交易所"),
    E99905(99905, "斯德哥尔摩证券交易所"),
    E99906(99906, "意大利证交所");


    private Integer code;

    private String desc;

    ExchangeAllEnums(Integer code, String desc) {
        this.code = code;

        this.desc = desc;
    }

    /**
     * 当出现枚举值以外的数值时，给与默认值
     */
    public static ExchangeAllEnums getByCode(Integer code) {
        if (null != code && 0 != code) {
            for (ExchangeAllEnums exchangeEnums : ExchangeAllEnums.values()) {
                if (exchangeEnums.getCode().equals(code)) {
                    return exchangeEnums;
                }
            }
        }
        return null;
    }

    public static ExchangeAllEnums getByDesc(String desc) {
        if (null != desc ) {
            for (ExchangeAllEnums exchangeEnums : ExchangeAllEnums.values()) {
                if (exchangeEnums.getDesc().equals(desc)) {
                    return exchangeEnums;
                }
            }
        }
        return null;
    }

}
