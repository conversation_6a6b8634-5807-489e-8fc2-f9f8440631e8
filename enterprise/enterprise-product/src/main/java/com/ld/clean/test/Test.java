package com.ld.clean.test;

import com.alibaba.fastjson.JSON;
import com.aliyun.oss.OSSClient;
import com.ld.clean.context.ApplicationCoreContextManager;
import com.ld.clean.kafka.KafkaHelper;
import com.ld.clean.model.manageenterprisedb.ProductBaseinfo;
import com.ld.clean.repository.manageenterprisedb.ProductBaseinfoRepository;
import com.ld.clean.repository.searchsyncenterprise.EpProductFinancingSyncRepository;
import lombok.Data;

import java.io.*;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;

public class Test {

    private static final EpProductFinancingSyncRepository epProductFinancingSyncRepository = ApplicationCoreContextManager.getInstance(EpProductFinancingSyncRepository.class);

    private static final ProductBaseinfoRepository productBaseinfoRepository = ApplicationCoreContextManager.getInstance(ProductBaseinfoRepository.class);
    private static OSSClient ossClient = null;

    public static void main(String[] args) throws Exception{


        List<String> ids =readTxtFile("C:\\Users\\<USER>\\Desktop\\new9998.txt");

        List<ProductBaseinfo> productBaseinfos = productBaseinfoRepository.selectByPrimaryKeyList(ids);

        productBaseinfos.forEach(vv->{

            KafkaHelper.javaKafkaProducer("base_products_affitited_product", JSON.toJSONString(new HashMap<String,List<Object>>(){{
                put("product_baseinfo", Collections.singletonList(vv));
            }}));

        });

    }
    private static List<String> readTxtFile(String filePath) throws IOException {
        String encoding = "UTF-8";
        List<String> list = new ArrayList<>();
        InputStreamReader read = null;
        try {
            File file = new File(filePath);
            if(file.isFile() && file.exists()){
                read = new InputStreamReader(new FileInputStream(file),encoding);
                BufferedReader bufferedReader = new BufferedReader(read);
                String lineTxt = null;
                while ((lineTxt = bufferedReader.readLine())!=null){
                    list.add(lineTxt);
                }

            }
        }catch (Exception e){
            e.getStackTrace();
        }finally {
            if(read!=null){
                read.close();
            }
        }
        return list;
    }

    @Data
    private static class Inner{
        private String id;
        private String faName;
        private String title;
        private String news;
    }
}
