/*
package com.ld.clean.constants;


import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

*/
/**
 * <AUTHOR>
 *//*


@Getter
public enum ProductNotifyEnum {

    BASEINFO(0, "baseinfo"),
    ALL(1, "all"),
    FINANCE(2, "finance"),
    MEMBER(3, "member"),
    COMPETITOR(4, "competitor"),
    NEWS(5, "news"),
    BANGDAN(6, "bangdan"),
    OTHER(-1, "other");


    private Integer code;
    private String desc;

    ProductNotifyEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    */
/**
     * 当出现枚举值以外的数值时，给与默认值-1
     *//*

    public static Integer getCodeByDesc(String desc) {
        if (StringUtils.isNotBlank(desc)) {
            for (ProductNotifyEnum notifyEnum : ProductNotifyEnum.values()) {
                if (desc.trim().equals(notifyEnum.desc)) {
                    return notifyEnum.code;
                }
            }
        }
        return OTHER.code;
    }

    public static String getDescByCode(Integer code) {
        if (code != null) {
            for (ProductNotifyEnum notifyEnum : ProductNotifyEnum.values()) {
                if (code.equals(notifyEnum.code)) {
                    return notifyEnum.desc;
                }
            }
        }
        return OTHER.desc;
    }
}
*/
