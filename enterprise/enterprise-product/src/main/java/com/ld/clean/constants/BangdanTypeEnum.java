package com.ld.clean.constants;

import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

/**
 * 榜单类型
 *
 * <AUTHOR>
 */
@Getter
public enum BangdanTypeEnum {

    OTHER(-1, "未知"),
    INSTITUTION(1, "投资机构"),
    PRODUCT(2, "品牌产品"),
    ;
    private Integer code;

    private String desc;


    BangdanTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static BangdanTypeEnum getByCode(int code) {
        for (BangdanTypeEnum roundLevelEnum : BangdanTypeEnum.values()) {
            if (code == roundLevelEnum.getCode()) {
                return roundLevelEnum;
            }
        }
        return OTHER;
    }


    public static BangdanTypeEnum getByDesc(String desc) {
        if (StringUtils.isNotBlank(desc)) {
            for (BangdanTypeEnum roundLevelEnum : BangdanTypeEnum.values()) {
                if (desc.equals(roundLevelEnum.getDesc())) {
                    return roundLevelEnum;
                }
            }
        }
        return OTHER;
    }

}

