package com.ld.clean.constants;

import org.apache.commons.lang3.StringUtils;

/**
 * 股东变更类型
 *
 * <AUTHOR>
 */
public enum PartnerTypeEnums {
    /*
     * 没有变化
     */
    NO_CHANGE("0", "没有变化", 0),
    /*
     * 新增股东
     */
    ADD_PARTNER("1", "新增股东", 4),
    /*
     * 增加注资
     */
    ADD_AMOUNT("2", "增加注资", 2),
    /*
     * 股东退出
     */
    REMOVE_PARTNER("3", "股东退出", 3),
    /*
     * 减少注资
     */
    REDUCE_AMOUNT("4", "减少注资", 1);

    private String value;
    private String desc;
    private Integer priority;


    PartnerTypeEnums(String value, String desc, Integer priority) {
        this.value = value;
        this.desc = desc;
        this.priority = priority;
    }

    public String getValue() {
        return value;
    }

    public String getDesc() {
        return desc;
    }

    public Integer getPriority() {
        return priority;
    }

    /**
     * 当出现枚举值以外的数值时，给与默认值
     */
    public static Integer getPriorityByDesc(String desc) {
        if (StringUtils.isNotBlank(desc)) {
            for (PartnerTypeEnums partnerTypeEnum : PartnerTypeEnums.values()) {
                if (desc.trim().equals(partnerTypeEnum.desc)) {
                    return partnerTypeEnum.priority;
                }
            }
        }
        return NO_CHANGE.priority;
    }

}
