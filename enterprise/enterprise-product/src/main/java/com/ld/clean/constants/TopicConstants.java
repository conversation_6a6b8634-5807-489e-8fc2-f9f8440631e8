package com.ld.clean.constants;

/**
 * 常量类
 *
 * <AUTHOR>
 * @date 2021年3月8日
 */
public class TopicConstants {

    public static final String TOPIC_QCC_PRODUCT_CLEAN = "qcc_product_clean";

    // dap赛道详情
    public static String TOPIC_DAP_DAP_MANAGE_ENTERPRISE_DB_INDUSTRY_TRACK_BASEINFO = "dap_dap_manage_enterprise_db_industry_track_baseinfo";
    public static String GROUP_DAP_DAP_INDUSTRY_TRACK_BASEINFO_LUJF = "group_dap_dap_industry_track_baseinfo_lujf";

    // 旧融资清洗队列
    public static final String TOPIC_PRODUCT_FINANCING = "dap_enterprise_product_service";

    // 产品同步
    public static String TOPIC_SPIDER_PRODUCT_BASEINFO = "base_async_product_to_manage";
    public static String GROUP_SPIDER_PRODUCT_BASEINFO_LUJF = "group_base_async_product_to_manage_lujf";

    // DAP新融资事件
    public static String TOPIC_DAP_DAP_MANAGE_ENTERPRISE_DB_COMP_PRODUCT_FINANCING = "dap_dap_manage_enterprise_db_comp_product_financing";
    public static String GROUP_DAP_DAP_COMP_PRODUCT_FINANCING_LUJF = "group_dap_dap_comp_product_financing_lujf";

    // 中间表抬入采编表
    public static String TOPIC_BASE_MANAGE_ENTERPRISE_DB_COMP_PRODUCT_FINANCING = "base_manage_enterprise_db_comp_product_financing_mid";
    public static String GROUP_BASE_BASE_COMP_PRODUCT_FINANCING_LIUPF = "group_base_base_comp_product_financing_liupf_240104";

    // 新清洗融资事件  這個已經申請廢棄
    public static String TOPIC_BASE_PRODUCT_CLEAN_FINANCE_V2 = "base_product_clean_finance_v2";
    public static String GROUP_GROUP_BASE_PRODUCT_CLEAN_FINANCE_V2_LUJF = "group_base_product_clean_finance_v2_lujf";

    // DAP修改产品详情
    public static String TOPIC_DAP_DAP_MANAGE_ENTERPRISE_DB_PRODUCT_BASEINFO = "dap_dap_manage_enterprise_db_product_baseinfo";
    public static String GROUP_DAP_DAP_PRODUCT_BASEINFO_LUJF = "group_dap_dap_product_baseinfo_lujf";
    //清洗产品详情
    public static String TOPIC_BASE_PRODUCT_CLEAN_BASEINFO = "base_product_clean_baseinfo";
    public static String GROUP_BASE_PRODUCT_CLEAN_BASEINFO_LUJF = "group_base_product_clean_baseinfo_lujf";

    // DAP修改产品新闻详情
    public static String TOPIC_DAP_DAP_MANAGE_ENTERPRISE_DB_COMP_PRODUCT_NEWS_ADMIN = "dap_dap_manage_enterprise_db_comp_product_news_admin";
    public static String GROUP_DAP_DAP_COMP_PRODUCT_NEWS_ADMIN = "group_dap_dap_comp_product_news_admin_lujf";

    // 清洗产品新闻详情
    public static String TOPIC_BASE_PRODUCT_CLEAN_NEWS = "base_product_clean_news";
    public static String GROUP_BASE_PRODUCT_CLEAN_NEWS_LUJF = "group_base_product_clean_news_lujf";

    // DAP修改产品人员详情
    public static String TOPIC_DAP_DAP_MANAGE_ENTERPRISE_DB_COMP_PRODUCT_MEMBER_ADMIN = "dap_dap_manage_enterprise_db_comp_product_member_admin";
    public static String GROUP_DAP_DAP_COMP_PRODUCT_MEMBER_ADMIN = "group_dap_dap_comp_product_member_admin_lujf";
    // 清洗产品人员详情
    public static String TOPIC_BASE_PRODUCT_CLEAN_MEMBER = "base_product_clean_member";
    public static String GROUP_BASE_PRODUCT_CLEAN_MEMBER_LUJF = "group_base_product_clean_member_lujf";

    // DAP修改产品竞品详情
    public static String TOPIC_DAP_DAP_MANAGE_ENTERPRISE_DB_COMP_PRODUCT_COMPETITOR_ADMIN = "dap_dap_manage_enterprise_db_comp_product_competitor_admin";
    public static String GROUP_DAP_DAP_COMP_PRODUCT_COMPETITOR_ADMIN = "group_dap_dap_comp_product_competitor_admin_lujf";
    // 清洗产品竞品详情
    public static String TOPIC_BASE_PRODUCT_CLEAN_COMPETITOR = "base_product_clean_competitor";
    public static String GROUP_BASE_PRODUCT_CLEAN_COMPETITOR_LUJF = "group_base_product_clean_competitor_lujf";


    // 通知nlp清洗竞品
    public static String TOPIC_BASE_DATACLEAN_PRODUCT_COMPETITIORS = "base_dataclean_product_competitiors";

    // 清洗NLP竞品结果
    public static String TOPIC_BASE_DATACLEAN_PRODUCT_COMPETITIORS_RESULT = "base_dataclean_product_competitiors_result";
    public static String GROUP_BASE_PRODUCT_COMPETITIORS_RESULT_LUJF = "group_base_product_competitiors_result_lujf";

    // 通知新闻内链生成不生效的队列（废弃队列，废物利用）
    public static final String TOPIC_NOTICE_NEWS_URL = "base_affiliated_enterprise_add_company";
    public static final String GROUP_NOTICE_NEWS_URL_LUJF = "group_base_affiliated_enterprise_add_comp_lujf";

    // 处理产品logo上传延迟问题
    public static final String TOPIC_BASE_SYNC_PRODUCT_LOGO = "base_sync_product_logo";
    public static final String GROUP_BASE_SYNC_PRODUCT_LOGO_LUJF = "group_base_sync_product_logo_lujf";

    // 通知产品刷新cnt
    public static final String TOPIC_BASE_SYNC_PRODUCT_CNT = "base_sync_product_cnt";
    public static final String GROUP_BASE_SYNC_PRODUCT_CNT = "group_base_sync_product_cnt_lujf_20220907";
//可废弃 base_sync_company_product  任务已停止 40316
    // 通知企业刷新产品
    public static final String TOPIC_BASE_SYNC_COMPANY_PRODUCT = "base_sync_company_product";
    public static final String GROUP_BASE_SYNC_COMPANY_PRODUCT = "group_base_sync_company_product_lujf_20220920";

    // 存储产品和投资方的映射关系
    public static final String TOPIC_BASE_INVEST_PRODUCT_MAPPING = "base_invest_product_mapping";
    public static final String GROUP_BASE_INVEST_PRODUCT_MAPPING = "group_base_invest_product_mapping_lujf_20221101";

    // 存储产品和投资方的映射关系（并购事件）
    public static final String TOPIC_BASE_INVEST_PRODUCT_MAPPING_EVENT = "base_invest_product_mapping_event";
    public static final String GROUP_BASE_INVEST_PRODUCT_MAPPING_EVENT = "group_base_finance_invest_mapping_event_240103";

    // 工商穿透变更记录dap
    public static final String TOPIC_DAP_DAP_MANAGE_ENTERPRISE_DB_INVEST_COMPANY_HOLDER_DIFF = "dap_dap_manage_enterprise_db_invest_company_holder_diff";
    public static final String GROUP_DAP_DAP_MANAGE_ENTERPRISE_DB_INVEST_COMPANY_HOLDER_DIFF = "group_dap_dap_manage_enterprise_db_invest_company_holder_diff";

    // 工商穿透股东变更详情队列
    public static String TOPIC_BASE_COMPANY_PARTNERS_CHANGE = "base_company_partners_change";
    public static String GROUP_BASE_COMPANY_PARTNERS_CHANGE_LUJF = "group_base_company_partners_change_lujf";
    public static String GROUP_BASE_COMPANY_PARTNERS_CHANGE_LUJF_V2 = "group_base_group_base_company_partners_change_lujf_v2";

    // 工商穿透生成融资事件清洗队列
    public static final String TOPIC_DAP_DAP_CLEAN_MANAGE_ENTERPRISE_DB_DAP_BUSINESS_PRODUCT_FINANCE = "dap_dap_clean_manage_enterprise_db_dap_business_product_finance";
    public static final String GROUP_DAP_DAP_CLEAN_CLEAN_DAP_BUSINESS_PRODUCT_FINANCE_LUJF_20221214 = "group_dap_dap_clean_clean_dap_business_product_finance_lujf_20221214";

    // 工商穿透融资事件dap队列
    public static final String TOPIC_DAP_DAP_MANAGE_ENTERPRISE_DB_DAP_BUSINESS_PRODUCT_FINANCE = "dap_dap_manage_enterprise_db_dap_business_product_finance";
    public static final String GROUP_DAP_DAP_BUSINESS_PRODUCT_FINANCE_LUJF_20221214 = "group_dap_dap_business_product_finance_lujf_20221214";

    // 工商补充产品队列
    public static final String TOPIC_BASE_SPECIAL_COMPANY_NO_PRODUCT = "base_special_company_no_product";
    public static final String GROUP_BASE_SPECIAL_COMPANY_NO_PRODUCT = "group_base_special_company_no_product_lujf_20230213";

    // 工商补充产品dap队列
    public static final String TOPIC_DAP_SPECIAL_COMPANY_NO_PRODUCT = "dap_dap_manage_enterprise_db_special_company_no_product";
    public static final String GROUP_DAP_DAP_SPECIAL_COMPANY_NO_PRODUCT = "group_dap_dap_special_company_no_product";

    //    public static final String TOPIC_BASE_COMP_ABSTRACT_INFO = "base_qiulj_temp_refresh_data";
    public static final String TOPIC_BASE_COMP_ABSTRACT_INFO = "base_comp_abstract_info";

    // 品牌产品dap操作队列
    public static final String TOPIC_DAP_DAP_MANAGE_ENTERPRISE_DB_DAP_COMP_PRODUCT_BASEINFO = "dap_dap_manage_enterprise_db_dap_comp_product_baseinfo";
    public static final String GROUP_DAP_DAP_DAP_COMP_PRODUCT_BASEINFO = "group_dap_dap_dap_comp_product_baseinfo";

    // 品牌产品dap数据同步队列
    public static final String TOPIC_DAP_DAP_CLEAN_MANAGE_ENTERPRISE_DB_DAP_COMP_PRODUCT_BASEINFO = "dap_dap_clean_manage_enterprise_db_dap_comp_product_baseinfo";
    public static final String GROUP_DAP_DAP_CLEAN_CLEAN_DAP_COMP_PRODUCT_BASEINFO_LUJF_20230308 = "group_dap_dap_clean_clean_dap_comp_product_baseinfo_lujf_20230308";

    // 品牌融资dap操作队列
    public static final String TOPIC_DAP_DAP_MANAGE_ENTERPRISE_DB_COMP_BRAND_FINANCING = "dap_dap_manage_enterprise_db_comp_brand_financing";
    public static final String GROUP_DAP_DAP_COMP_BRAND_FINANCING_LUJF_20230314 = "group_dap_dap_comp_brand_financing_lujf_20230314";

    // 品牌融资dap数据同步队列
    public static final String TOPIC_DAP_DAP_CLEAN_MANAGE_ENTERPRISE_DB_COMP_BRAND_FINANCING = "dap_dap_clean_manage_enterprise_db_comp_brand_financing";
    public static final String GROUP_DAP_DAP_CLEAN_COMP_BRAND_FINANCING_LUJF_20230314 = "group_dap_dap_clean_comp_brand_financing_lujf_20230314";

    // 金融A股和新三板融资事件数据同步队列
    public static final String TOPIC_BASE_A_SHARES_AND_NQ_FINANCE = "base_a_shares_and_nq_finance";
    public static final String GROUP_BASE_A_SHARES_AND_NQ_FINANCE_LUJF_0321 = "group_base_a_shares_and_nq_finance_lujf_0321";
    public static final String TOPIC_BASE_A_SHARES_AND_NQ_FINANCE_V2 = "base_a_shares_and_nq_finance_v2";
    public static final String GROUP_BASE_A_SHARES_AND_NQ_FINANCE_V2  = "group_base_a_share_and_nqsk_250702";

    // 融资通知FA 队列
    public static final String TOPIC_BASE_PRODUCT_FINANCE_FA_LABELS_NOTICE = "base_product_finance_fa_labels_notice";

    // 新融资清洗队列
    public static final String TOPIC_BASE_CLEAN_COMP_BRAND_FINANCE = "base_clean_comp_brand_finance";
    public static final String GROUP_BASE_CLEAN_COMP_BRAND_FINANCE_SINGLE_LUJF = "group_base_clean_comp_brand_finance_lujf";
    public static final String GROUP_BASE_CLEAN_COMP_BRAND_FINANCE_SINGLE_20231207 = "group_base_clean_comp_brand_finance_231207";

    // 新品牌产品清洗队列  ->解析公告数据
    public static final String TOPIC_BASE_CLEAN_COMP_BRAND_INFO = "base_clean_comp_brand_info";
    public static final String GROUP_BASE_CLEAN_COMP_BRAND_INFO_LUJF = "group_base_clean_comp_brand_info_lujf";
    public static final String TOPIC_BASE_CLEAN_COMP_BRAND_INFO_DMP = "base_report_data_agi_result_dmp";
    public static final String GROUP_BASE_CLEAN_COMP_BRAND_INFO_DMP = "group_base_report_data_agi_250725";

    // 模型标准结果队列
    public static final String TOPIC_BASE_CLEAN_AGI_RESULT_BIAOZHU = "base_comp_fin_shareholder_bfipo_result_annotation";
    public static final String GROUP_BASE_CLEAN_AGI_RESULT_BIAOZHU  = "group_base_shareholder_result_annotation_250717";

    // 阿里云品牌产品数据同步队列
    public static final String TOPIC_BASE_SYNC_COMP_BRAND_PRODUCT_INFO = "base_sync_comp_brand_product_info";
    public static final String GROUP_BASE_SYNC_COMP_BRAND_PRODUCT_INFO_LUJF = "group_base_sync_comp_brand_product_info_lujf";

    // 产品业务dap操作队列
    public static final String TOPIC_DAP_DAP_MANAGE_ENTERPRISE_DB_DAP_COMP_PRODUCT_BUSINESS_INFO = "dap_dap_manage_enterprise_db_dap_comp_product_business_info";
    public static final String GROUP_DAP_DAP_COMP_PRODUCT_BUSINESS_INFO_LUJF = "group_dap_dap_comp_product_business_info_lujf";

    // 产品融资刷新通知工商
    public static final String TOPIC_DAP_MODIFY_COMPANY_PRODUCT = "dap_modify_company_product";
    public static final String GROUP_DAP_PRODUCT_NOTICE_COMPANY_LUJF = "group_dap_product_notice_company_lujf";

    // 金融融资合并至基础表
    public static final String TOPIC_BASE_ASYNC_INSTITUTION_NEWS = "base_async_institution_news";
    public static final String GROUP_BASE_CLEAN_ASHARES_BRAND_FINANCE_LUJF = "group_base_clean_ashares_brand_finance_lujf";

    //转内链爬虫处理
    public static final String TOPIC_DATACLEAN_PRODUCT_NEWS_CRAWL = "dataclean_product_news_crawl";

    // 同步新闻内链
    public static final String TOPIC_SPIDER_COMP_NEWS_FINANCING_ID = "spider_comp_news_financing_id";
    public static final String GROUP_SPIDER_SYNC_FINANCING_LUJF = "group_spider_sync_financing_lujf";
    //并购新闻
    public static final String GROUP_SPIDER_MERGER_FINANCING_NEW_240912 = "group_spider_merger_financing_news_240912";

    // A股三板融资dap队列
    public static final String TOPIC_DAP_DAP_MANAGE_ENTERPRISE_DB_A_SHARES_AND_NEW_THIRD_BOARD_FINANCE = "dap_dap_manage_enterprise_db_a_shares_and_new_third_board_finance";
    public static final String GROUP_DAP_DAP_A_SHARES_AND_NEW_THIRD_BOARD_FINANCE_LUJF_20230425 = "group_dap_dap_a_shares_and_new_third_board_finance_lujf_20230425";

    // 融资标准化dap队列
    public static final String TOPIC_DAP_DAP_MANAGE_ENTERPRISE_DB_COMP_FINANCE_ROUND_MAPPING = "dap_dap_manage_enterprise_db_comp_finance_round_mapping";
    public static final String GROUP_DAP_DAP_COMP_FINANCE_ROUND_MAPPING_LUJF_20230425 = "group_dap_dap_comp_finance_round_mapping_lujf_20230425";

    // 融资标准化修改通知产品刷新融资队列
    public static final String TOPIC_DAP_DAP_CLEAN_MANAGE_ENTERPRISE_DB_COMP_FINANCE_ROUND_MAPPING = "dap_dap_clean_manage_enterprise_db_comp_finance_round_mapping";
    public static final String GROUP_DAP_DAP_CLEAN_NOTICE_FINANCE_ROUND_CHANGE_LUJF_20230426 = "group_dap_dap_clean_notice_finance_round_change_lujf_20230426";

    // IPO退出dap修改队列
    public static final String TOPIC_DAP_DAP_MANAGE_ENTERPRISE_DB_DAP_IPO_BOOK_EXIT = "dap_dap_manage_enterprise_db_dap_ipo_book_exit";
    public static final String GROUP_DAP_DAP_DAP_IPO_BOOK_EXIT_LUJF_20230602 = "group_dap_dap_dap_ipo_book_exit_lujf_20230602";

    // IPO退出通知创投队列
    public static final String TOPIC_DAP_DAP_CLEAN_MANAGE_ENTERPRISE_DB_DAP_IPO_BOOK_EXIT = "dap_dap_clean_manage_enterprise_db_dap_ipo_book_exit";
    public static final String GROUP_DAP_DAP_CLEAN_CLEAN_DAP_IPO_BOOK_EXIT_LUJF_20230606 = "group_dap_dap_clean_clean_dap_ipo_book_exit_lujf_20230606";

    //历史沿革dap队列
    public static final String TOPIC_DAP_DAP_MANAGE_ENTERPRISE_DB_DAP_IPO_HISTORY = "dap_dap_manage_enterprise_db_dap_ipo_history";
    public static final String GROUP_DAP_DAP_DAP_IPO_HISTORY_LUJF_20230606 = "group_dap_dap_dap_ipo_history_lujf_20230606";

    //爬虫推送竞品机构信息
    public static final String TOPIC_BASE_SPIDER_INSTITUTION_JP = "base_spider_institution_jp";
    public static final String GROUP_BASE_CLEAN_INSTITUTION_JP_20230712 = "group_base_clean_institution_jp_20230712";

    // qk 并购事件spider队列
    public static final String TOPIC_DAP_SPIDER_FINANCIAL_INVEST_QK_BGSJ = "dap_spider_financial_invest_qk_bgsj";
    public static final String GROUP_DAP_SPIDER_FINANCIAL_INVEST_QK_BGSJ = "group_dap_spider_comp_product_merger_events_240830";

    // qk 定向增发spider队列
    public static final String TOPIC_DAP_SPIDER_FINANCIAL_INVEST_QK_ADDISSUE = "dap_spider_financial_invest_qk_tag_list";
    public static final String GROUP_DAP_SPIDER_FINANCIAL_INVEST_QK_ADDISSUE = "group_dap_spider_comp_iuuse_event_250609";

    // 通知投资机构基本信息刷新队列
    public static final String BASE_SIVS_INSTITUTION_REFRESH_NOTICE_METRICS_TOPIC = "base_sivs_institution_refresh_notice";

    // 通知清洗工商股东投资融资队列
    public static final String TOPIC_BASE_CLEAN_COMP_PARTNER_INVEST_FINANCE = "base_clean_comp_partner_invest_finance";
    public static final String GROUP_BASE_CLEAN_COMP_PARTNER_INVEST_FINANCE_20230713 = "group_base_clean_comp_partner_invest_finance_20230713";

    // 私募基金管理人投资事件清洗队列
    public static final String BASE_CLEAN_PRIVATE_FUND_MANAGER_INVEST_DYNAMIC = "base_clean_private_fund_manager_invest_dynamic";

    // 私募基金管投资事件清洗队列
    public static final String BASE_CLEAN_FUND_INVEST_EVENTS = "base_clean_fund_invest_events";

    // lp对外投资刷新通知队列
    public static final String BASE_CLEAN_LP_INVEST_METRICS_TOPIC = "base_clean_lp_invest";

    // 变更通知集团队列
    public static final String BASE_COMP_CHANGE_NOTIFY_GROUP = "base_comp_change_notify_group";

    // 榜单变更刷新通知队列
    public static final String TOPIC_BASE_NOTICE_CT_LIST_INFO = "base_notice_ct_list_info";
    public static final String GROUP_BASE_CLEAN_CT_LIST_INFO_20230831 = "group_base_clean_ct_list_info_20230831";

    // 品牌产品cnt刷新通知队列
    public static final String TOPIC_BASE_PRODUCT_TABLE_TOPIC = "base_ep_ct_product_table_data";
    //public static final String TOPIC_BASE_PRODUCT_CNT_LIST_TOPIC = "dap_ep_product_bussiness_cnt";
    //public static final String TOPIC_BASE_PRODUCT_CNT_LIST_MEMBER_GROUPID = "group_dap_ep_product_member_20230904";

    // 品牌产品cnt刷新通知队列
    public static final String TOPIC_DATACLEAN_NEWS_PID_SYNC = "dataclean_news_pid_sync";
    public static final String GROUP_DATACLEAN_PID_NOTICE_PRODUCT = "group_dataclean_pid_notice_product_20231019";

    // 品牌产品刷新关联信息表
    public static final String TOPIC_BASE_ENTERPRISE_COMP_PRODUCT_RELATIONSHIP = "base_enterprise_comp_product_relationship";
    public static final String GROUP_BASE_SPIDER_COMP_PRODUCT_RELATIONSHIP_GROUPID = "group_base_spider_comp_product_relationship_240724";

    // 品牌产品成员企业表
    public static final String TOPIC_BASE_PRODUCT_AFFITITED_PRODUCTS = "base_products_affitited_product";
    public static final String GROUP_BASE_PRODUCT_AFFITITED_PRODUCTS = "group_base_comp_affitoited_products_250311";



    // 品牌产品基金布局表
    public static final String TOPIC_BASE_ENTERPRISE_COMP_PRODUCT_LAYOUT = "base_ct_product_layout";
    public static final String GROUP_BASE_ENTERPRISE_COMP_PRODUCT_LAYOUT = "group_base_ct_product_invest_layout";
    public static final String TOPIC_BASE_ENTERPRISE_COMP_PRODUCT_LAYOUT_BUSSINESS = "base_ct_product_fund_layout_bussiness";
    public static final String GROUP_BASE_ENTERPRISE_COMP_PRODUCT_LAYOUT_BUSSINESS  = "group_base_ep_ct_fund_layout_product_241115";



    // 品牌产品基金投资事件
    public static final String TOPIC_BASE_ENTERPRISE_COMP_PRODUCT_INVEST_PROJECT = "base_ct_product_invest_project";
    public static final String GROUP_BASE_ENTERPRISE_COMP_PRODUCT_INVEST_PROJECT  = "group_base_ct_product_invest_events_241111";
    public static final String TOPIC_BASE_ENTERPRISE_COMP_PRODUCT_INVEST_PROJECT_BUSSINESS  = "base_ep_ct_product_invest_project_bussiness";
    public static final String GROUP_BASE_ENTERPRISE_COMP_PRODUCT_INVEST_PROJECT_BUSSINESS  = "group_base_ep_ct_product_invest_project_241118";

    // 集团变更通知产品
    public static final String TOPIC_BASE_GROUPV3_MEMBER_CHANGE_NOTIFY = "base_groupv3_member_change_notify";
    public static final String GROUP_BASE_BASE_GROUPV3_MEMBER_CHANGE_NOTIFY_PRODUCT = "group_base_base_groupv3_member_change_notify_product_20230703";

    // 人员变更通知产品
    public static final String TOPIC_BASE_COMP_PERSON_CHANGE = "base_comp_person_change";
    public static final String GROUP_BASE_CT_PERSON_CHANGE_NOTICE = "group_base_ct_person_change_notice_20231107";

    public static final String SIVS_BASE_INSTITUTION_COUNT_REFRESH_METRICS_TOPIC = "base_sivs_institution_count_refresh";

    /**
     * binlog品牌产品业务表
     */
    public static final String TOPIC_DW_TIDB_BINLOG_SEARCH_SYNC_ENTERPRISE = "dw_tidb_binlog_search_sync_enterprise";
    public static final String GROUP_DW_EP_PRODUCT_BASEINFO_SYNC_231110 = "group_dw_ep_product_baseinfo_sync_231110";

    // LP投资项目业务表清洗队列
    public static final String TOPIC_BASE_CLEAN_SEARCH_LP_INVEST_PROJECT = "base_clean_search_lp_invest_project";
    // 私募基金投资项目业务表清洗队列
    public static final String TOPIC_BASE_CLEAN_SEARCH_FUND_INVEST_PROJECT = "base_clean_search_fund_invest_project";


    // 企业库Dap队列
    public static final String TOPIC_DAP_DAP_MANAGE_ENTERPRISE_DB = "dap_dap_manage_enterprise_db";
    public static final String GROUP_COMP_PRODUCT_MERGER_ENENTS_QK = "group_dap_dap_comp_product_merger_events_240830";
    public static final String GROUP_EP_CT_COMP_PRODUCT_AFFITETED = "group_dap_dap_comp_product_affited_250320";


    // 并购事件dap消费组
    public static final String GROUP_DAP_DAP_PRODUCT_MERGE_EVENTS = "group_dap_dap_product_merge_events_231115";
    // 股权转让事件dap消费组
    public static final String GROUP_DAP_DAP_PRODUCT_EQUITY_TRANSFER_EVENTS = "group_dap_dap_product_equity_transfer_events_231115";

    //并购、转让事件
    public static final String TOPIC_BASE_EP_PRODUCT_MA_EVENT = "base_ep_product_ma_event";
    public static final String GROUP_BASE_COMP_PRODUCT_MERGE_EVENT = "group_base_comp_product_merge_event_231207";

    // T+1刷新产品对应的工商信息
    public static final String TOPIC_BASE_STATISTICS_PRODUCT = "base_statistics_product";
    public static final String GROUP_BASE_STATISTICS_PRODUCT_LUJF = "group_base_statistics_product_lujf_20220825";

    //事件补充中间表中转
    public static final String TOPIC_BASE_COMP_BRAND_TRANS_MID = "base_comp_brand_financing_transfer_mid";
    public static final String GROUP_BASE_COMP_BRAND_FINANCING_TRANS_MID = "group_base_comp_brand_financing_transfer_mid_231218";

    //推送瑞兽爬虫
    public static final String TOPIC_BASE_PRODUCT_TO_RS = "base_product_baseinfo_to_rs";

    //历史人员推送品牌产品无效核心人员
    public static final String TOPIC_BASE_COMPANY_PERSON_HISTORY_CHANGE = "base_company_person_history_change";
    public static final String GROUP_BASE_COMPANY_PERSON_HISTORY_CHANGE = "group_base_company_person_history_change_240521";

    //股权变更工具接入创投主体对外投资表数据
    public static final String TOPIC_BASE_INVEST_COMPANY_HOLDER_DIFF_SPIDER = "base_ct_invest_company_holder_diff_spider";
    public static final String GROUP_BASE_INVEST_COMPANY_HOLDER_DIFF_SPIDER_LIUPF = "group_base_invest_company_holder_diff_240625";

    //股权变更工具接入创投主体对外投资表数据
    public static final String TOPIC_BASE_SPECIAL_COMPANY_NO_PRODUCT_SPIDER = "base_ct_invest_company_holder_diff_confirm_data";
    public static final String GROUP_BASE_SPECIAL_COMPANY_NO_PRODUCT_SPIDER_LIUPF = "group_base_invest_company_holder_diff_240625";

    //股权变更工具接入创投主体对外投资表数据
    public static final String TOPIC_BASE_FINANCIAL_INVEST_QMP_XMK_SEARCH_SPIDER = "dap_spider_financial_invest_qmp_xmk_search";
    public static final String GROUP_DAP_SPIDER_FINANCIAL_INVEST_QMP_XMK_SEARCH_SPIDER_LIUPF = "group_dap_spider_financial_invest_qmp_xmk_search_240710";

    //投资机构基金布局基础表
    public static final String TOPIC_BASE_COMP_PRIVATE_FUND_INSTITUTION_LAYOUT = "base_comp_private_fund_institution_layout";
    public static final String GROUP_BASE_COMP_PRIVATE_FUND_INSTITUTION_LAYOUT = "group_base_comp_private_fund_institution_layout_250317";

}
