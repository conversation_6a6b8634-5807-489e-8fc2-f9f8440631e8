package com.ld.clean.util;

import com.alibaba.fastjson.JSON;
import com.amazonaws.services.s3.model.ObjectMetadata;
import com.base.clean.utils.BaseApiUtil;
import com.base.clean.utils.BaseFileUtil;

import com.ld.clean.constants.MyConstants;
import com.ld.clean.constants.TopicConstants;
import com.ld.clean.context.ApplicationCoreContextManager;
import com.ld.clean.job.product.logo.entity.SpiderProductLogo;
import com.ld.clean.kafka.KafkaHelper;
import com.ld.clean.utils.Log;
import com.qcc.clean.entity.QccPutObjectRequest;
import com.qcc.clean.util.QccCephUtil;
import org.apache.commons.lang3.StringUtils;

import java.io.InputStream;
import java.util.Date;

/**
 * <AUTHOR>
 */
public class LogoUtil {

    public static String convertProductLogo(String logoUrl, String productId, Integer count) {
        String url = logoUrl;
        try {
            if (StringUtils.isNotBlank(logoUrl)) {
                int start = logoUrl.lastIndexOf("/");
                int end = logoUrl.lastIndexOf(".");
                if (start != -1 && end != -1 && end > start + 1) {
                    String logoId = logoUrl.substring(start + 1, end);
                    if (logoUrl.contains("image.qcc.com/product") && StringUtils.isNotBlank(logoId) && !logoId.equals(productId)) {
                        int length = logoUrl.length();
                        String suffix = logoUrl.substring(logoUrl.lastIndexOf("."), length);
                        // 获取dap平台上传的logo文件流
                        String oringalImgUrl = MyConstants.PRODUCT_KEY + logoId + suffix;
                        InputStream imageIns = BaseApiUtil.cephDownload(MyConstants.BUCKET_NAME, oringalImgUrl, 1);
                        if (null != imageIns) {
                            // 重新根据产品id上传logo文件
                            String imgUrl = MyConstants.PRODUCT_KEY + productId + MyConstants.LOGO_SUFFIX_JPG;
                            QccPutObjectRequest putObjectRequest = new QccPutObjectRequest(MyConstants.BUCKET_NAME,
                                    imgUrl, imageIns, new ObjectMetadata(), false, true);
                            boolean uploadSuc = QccCephUtil.getInstance().putObject(putObjectRequest);
                            if (uploadSuc) {
                                url = MyConstants.QCC_IMAGE + imgUrl;
                                // 删除dap平台上传的logo文件
                               // QccCephUtil.getInstance().deleteObject(MyConstants.BUCKET_NAME, oringalImgUrl);
//                                BaseApiUtil.cephDelete(MyConstants.BUCKET_NAME, oringalImgUrl, 1);
                            }
                        } else {
                            //产品id和logoid不一致且图片重新上传失败的，交给轮询任务处理,处理次数+1
                            System.out.println("时间："+new Date() +",productId:"+productId+"imageIns为空");
                            KafkaHelper.javaKafkaProducer(TopicConstants.TOPIC_BASE_SYNC_PRODUCT_LOGO, JSON.toJSONString(new SpiderProductLogo(productId, count + 1)));
                        }
                    } else {
                        // 外链图片直接获取资源上传至OSS
                        InputStream imageIns = BaseFileUtil.downloadFileByUrl(logoUrl);
                        if (null != imageIns) {
                            // 重新根据产品id上传logo文件
                            String imgUrl = MyConstants.PRODUCT_KEY + productId + MyConstants.LOGO_SUFFIX_JPG;
//                            CephPutStreamRequest cephPutStreamRequest = new CephPutStreamRequest(Base64.encode(imageIns),
//                                    MyConstants.BUCKET_NAME, imgUrl, 1, true, 5);
//                            BaseApiUtil.cephUpload(cephPutStreamRequest);
                            QccPutObjectRequest putObjectRequest = new QccPutObjectRequest(MyConstants.BUCKET_NAME,
                                    imgUrl, imageIns, new ObjectMetadata(), false, true);
                            boolean uploadSuc = QccCephUtil.getInstance().putObject(putObjectRequest);
                            if (uploadSuc) {
                                url = MyConstants.QCC_IMAGE + imgUrl;
                            }
                        }
                    }
                }
            }
        } catch (Exception e) {
            Log.getInst().error("", "insert IprightPatentAttachmentInfo error:{}.trace:{},数据:{}",e.getMessage(),e.getStackTrace(),productId);
            // 产品id和logoid不一致且图片重新上传失败的，交给轮询任务处理
            System.out.println("时间："+new Date() +",productId:"+productId+"异常Error"+e.getMessage());
            KafkaHelper.javaKafkaProducer(TopicConstants.TOPIC_BASE_SYNC_PRODUCT_LOGO, JSON.toJSONString(new SpiderProductLogo(productId, count + 1)));
        }
        return url;
    }
}
