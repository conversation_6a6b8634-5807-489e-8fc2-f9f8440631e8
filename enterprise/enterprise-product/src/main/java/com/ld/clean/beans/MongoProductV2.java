/*
package com.ld.clean.beans;

import com.alibaba.fastjson.annotation.JSONField;
import com.ld.clean.job.productv2.productCntList.so.ProductCnt;
import lombok.Data;
import org.springframework.data.annotation.Transient;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import java.util.ArrayList;
import java.util.List;

*/
/**
 * <AUTHOR>
 * @Description: 新产品详情mongo表
 *//*

@Data
@Document(collection = "Qcc_Product")
public class MongoProductV2 {

    @Field("_id")
    private String _id = "";
    @Field("ProductName")
    private String name = "";
    @Field("Alias")
    private String alias = "";
    */
/**
     * 产品简介
     *//*

    @Field("CompanyIntro")
    private String intro = "";
    @Field("LastRound")
    private String lastRound = "";
    @Field("Location")
    private String location = "";
    @Field("LogoUrl")
    private String logoUrl = "";
    @Field("Tags")
    private String tags = "";
    @Field("TagsFirst")
    private String tagsFirst = "";
    @Field("StartDate")
    private Long startDate = 0L;
    @Field("PublishDate")
    private Long publishDate = 0L;
    @Field("Website")
    private String website = "";
    @Field("Domain")
    private String domain = "16";
    @Field("CompanyKeyNo")
    private String companyKeyNo = "";
    @Field("CompanyName")
    private String companyName = "";
    */
/**
     * 公司简介
     *//*

    @Field("Brief")
    private String brief = "";
    */
/**
     * 法人
     *//*

    @Field("OperName")
    private String corporation = "";

    @Field("IsValid")
    private String isValid = "1";
    @Field("IsChecked")
    private Integer checked = 1;

    @Field("ProductCntList")
    private List<ProductCnt> productCntList;

    @Field("TagEntityList")
    private List<TagEntity> tagEntityList;

    @Field("FunderInfo")
    private List<Financing> financing = new ArrayList<>();

    @Data
    public static class Financing {
        @Field("Id")
        private String id = "";
        @Field("Round")
        private String round = "";
        @Field("FundingMoney")
        private String amount = "";
        @Field("AmountRMB")
        private String amountRMB = "";
        @Field("FundingDate")
        private Long fundingDate = 0L;
        @Field("FundingDate_time")
        private String fundingDateTime = "";
        */
/**
         * 融资占比
         *//*

        @Field("Ratio")
        private String ratio = "";
        */
/**
         * 融资顾问
         *//*

        @Field("FA")
        private String advisor = "";
        */
/**
         * 估值
         *//*

        @Field("Valuation")
        private String valuation = "";
        */
/**
         * 投资机构
         *//*

        @Field("Investor")
        private String investor = "";
        @Field("NewsTitle")
        private String newsTitle = "";
        @Field("NewsUrl")
        private String newsUrl = "";
        @Field("IsValid")
        private String valid = "1";

        @Field("NameKeyNoCollection")
        private List<Investor> investors = new ArrayList<>();

        @Data
        public static class Investor {
            @Field("Id")
            @JSONField(name = "Id")
            private String id = "";
            @Field("Name")
            @JSONField(name = "Name")
            private String name = "";

            @Field("KeyNo")
            @JSONField(name = "KeyNo")
            private String keyNo = "";

            @Field("Org")
            @JSONField(name = "Org")
            private Integer org;
        }
    }

    @Field("MemberInfo")
    private List<Member> members = new ArrayList<>();

    @Data
    public static class Member {
        @Field("Intro")
        private String info = "";
        @Field("Position")
        private String position = "";
        @Field("ImgUrl")
        private String imgUrl = "";
        @Field("Name")
        private String name = "";
        @Field("KeyNo")
        private String keyNo = "";
        @Field("BossImgUrl")
        private String bossImgUrl = "";
        @Field("GraduateSch")
        private String graduateSch = "";
        @Transient
        private Integer sort;
    }

    @Field("CompatProductInfo")
    private List<Competitor> competitors = new ArrayList<>();

    @Data
    public static class Competitor {
        @Field("ID")
        private String id = "";
        @Field("ProductName")
        private String name = "";
        @Field("CompanyIntro")
        private String intro = "";
        @Field("LastRound")
        private String lastRound = "";
        @Field("Brief")
        private String brief = "";
        @Field("Location")
        private String location = "";
        @Field("LogoUrl")
        private String logoUrl = "";
        @Field("Tags")
        private String tags = "";
        @Field("StartDate")
        private Long startDate = 0L;
        @Field("PublishDate")
        private Long publishDate = 0L;
        @Field("XiniuID")
        private String sourceId = "";

        @Field("RelatedCompanyInfo")
        private CompanyBaseInfo relatedCompanyInfo;
    }

    @Data
    public static class CompanyBaseInfo {

        @JSONField(name = "Name")
        @Field("Name")
        private String name;

        @JSONField(name = "Org")
        @Field("Org")
        private Integer org = -1;

        @JSONField(name = "KeyNo")
        @Field("KeyNo")
        private String keyNo = "";
    }

    @Field("ProductInfo")
    private List<Amplification> amplifications = new ArrayList<>();

    @Data
    public static class Amplification {
        @Field("Name")
        private String name = "";
        @Field("Intro")
        private String intro = "";
        @Field("Website")
        private String website = "";
        @Field("Type")
        private String type = "";
    }

    @Field("NewsInfo")
    private List<News> news = new ArrayList<>();

    @Data
    public static class News {
        @Field("Title")
        private String title = "";
        @Field("ContentUrl")
        private String url = "";
        @Field("PublishDate")
        private Long publishDate = 0L;
        @Field("Source")
        private String source = "";
        @Field("NewsId")
        private String newsId = "-999999";
    }

}*/
