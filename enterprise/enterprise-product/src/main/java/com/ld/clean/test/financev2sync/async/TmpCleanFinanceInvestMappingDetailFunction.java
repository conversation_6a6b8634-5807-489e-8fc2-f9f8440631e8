package com.ld.clean.test.financev2sync.async;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.ld.clean.beans.InvestEntity;
import com.ld.clean.constants.InvestTypeEnum;
import com.ld.clean.context.ApplicationCoreContextManager;
import com.ld.clean.dao.model.basecleandatatmp.ScSeFinanceInvestMapping20240320;
import com.ld.clean.dao.repository.basecleandatatmp.ScSeFinanceInvestMapping20240320Repository;
import com.ld.clean.dto.company.QccCompanyOut;
import com.ld.clean.model.CompBrandFinancing;
import com.ld.clean.model.manageenterprisedb.PlaceFinance;
import com.ld.clean.model.manageenterprisedb.ProductBaseinfo;
import com.ld.clean.parent.AsyncCleanParentFunction;
import com.ld.clean.repository.manageenterprisedb.FinanceInvestMappingRepository;
import com.ld.clean.repository.manageenterprisedb.PlaceFinanceRepository;
import com.ld.clean.repository.manageenterprisedb.ProductBaseinfoRepository;
import com.ld.clean.util.StringUtil;
import com.ld.clean.util.ct.CtKeynoUtil;
import com.ld.clean.utils.CommonUtil;
import com.ld.clean.utils.MD5Util;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import tk.mybatis.mapper.entity.Condition;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
public class TmpCleanFinanceInvestMappingDetailFunction extends AsyncCleanParentFunction<CompBrandFinancing, List<ScSeFinanceInvestMapping20240320>> {

    private static final List<String> INVALID_NAME = Arrays.asList(new String[]{"未披露", "公开发行", "投资方未知"});

    private static final ProductBaseinfoRepository productBaseinfoRepository = ApplicationCoreContextManager.getInstance(ProductBaseinfoRepository.class);
    private static final PlaceFinanceRepository placeFinanceRepository = ApplicationCoreContextManager.getInstance(PlaceFinanceRepository.class);
    private static FinanceInvestMappingRepository financeInvestMappingRepository = ApplicationCoreContextManager.getInstance(FinanceInvestMappingRepository.class);
    private static ScSeFinanceInvestMapping20240320Repository scSeFinanceInvestMapping20240320Repository  = ApplicationCoreContextManager.getInstance(ScSeFinanceInvestMapping20240320Repository.class);

    public TmpCleanFinanceInvestMappingDetailFunction(int corePoolSize) {
        super(corePoolSize);
    }

    @Override
    protected List<ScSeFinanceInvestMapping20240320> invoke(CompBrandFinancing brandFinancing) throws Exception {
        List<ScSeFinanceInvestMapping20240320> result = new ArrayList<>();
        // 只保留机构和企业的投资映射关系，个人暂不处理--全部处理
        if (brandFinancing.getDataStatus() == 1) {
            String leadInvestor = brandFinancing.getLeadInvestor();
            String followInvestor = brandFinancing.getFollowInvestor();
            //IPO处理获取战略配售数据进行补充
            if("IPO".equals(brandFinancing.getStandardRound())){
                //根据productId查询companyId
                ProductBaseinfo productBaseinfo = (ProductBaseinfo) productBaseinfoRepository.selectByPrimaryKey(brandFinancing.getBrandId());
                if(ObjectUtil.isNotEmpty(productBaseinfo)){
                    String compId = productBaseinfo.getCompanyId();
                    String financeDate = brandFinancing.getFinanceDate();
                    if(StringUtils.isBlank(compId)){
                        if(!followInvestor.contains("公开发行")){
                            //基石投资数据已存在需要进行修改
                            List<InvestEntity> ipoInvests = JSONObject.parseArray(CommonUtil.getString(followInvestor), InvestEntity.class);
                            for(InvestEntity invest :ipoInvests){
                                ScSeFinanceInvestMapping20240320 ipoMapping = generate(invest, brandFinancing);
                                ipoMapping.setSourceType(3);
                                ipoMapping.setFinancingType(1);
                                ipoMapping.setParticipantType(3);
                                result.add(ipoMapping);
                            }
                        }
                    }else {
                        Condition condition = new Condition(PlaceFinance.class);
                        condition.createCriteria().andEqualTo("compKeyno", compId).andEqualTo("round","IPO").andEqualTo("listDate",financeDate).andEqualTo("dataStatus", 1);
                        List<PlaceFinance> placeFinanceList = placeFinanceRepository.selectByCondition(condition);
                        if(placeFinanceList.isEmpty() && !brandFinancing.getFollowInvestor().contains("公开发行")){
                            //基石投资数据已存在进行修改
                            List<InvestEntity> ipoInvests = JSONObject.parseArray(CommonUtil.getString(followInvestor), InvestEntity.class);
                            for(InvestEntity invest :ipoInvests){
                                ScSeFinanceInvestMapping20240320 ipoMapping = generate(invest, brandFinancing);
                                ipoMapping.setSourceType(3);
                                ipoMapping.setFinancingType(1);
                                ipoMapping.setParticipantType(3);
                                result.add(ipoMapping);
                            }
                        }else {
                            placeFinanceList = mergeAndRemoveDuplicates(placeFinanceList);
                            result.addAll(handleIPO(placeFinanceList,brandFinancing));
                        }
                    }
                }
            }else {
                // 领投详情
                List<InvestEntity> leadInvestorList = JSONObject.parseArray(CommonUtil.getString(leadInvestor), InvestEntity.class);
                if (CollectionUtils.isNotEmpty(leadInvestorList)) {
                    for (InvestEntity investEntity : leadInvestorList) {
//                    if ((investEntity.getCategory() == 1 || investEntity.getCategory() == 2)
//                            && StringUtils.isNotBlank(investEntity.getName()) && !INVALID_NAME.contains(investEntity.getName())) {
//                        result.add(generate(investEntity, brandFinancing));
//                    }
                        if (investEntity.getName().contains("个人投资者")) {
                            result.addAll(generatePerson(investEntity, brandFinancing));
                        } else {
                            result.add(generate(investEntity, brandFinancing));
                        }
                    }
                }
                // 跟投详情
                List<InvestEntity> followInvestorList = JSONObject.parseArray(CommonUtil.getString(followInvestor), InvestEntity.class);
                if (CollectionUtils.isNotEmpty(followInvestorList)) {
                    for (InvestEntity investEntity : followInvestorList) {
//                    if ((investEntity.getCategory() == 1 || investEntity.getCategory() == 2)
//                            && StringUtils.isNotBlank(investEntity.getName()) && !INVALID_NAME.contains(investEntity.getName())) {
//                        result.add(generate(investEntity, brandFinancing));
//                    }
                        if (investEntity.getName().contains("个人投资者")) {
                            result.addAll(generatePerson(investEntity, brandFinancing));
                        } else {
                            result.add(generate(investEntity, brandFinancing));
                        }
                    }
                }

            }
            // 如果投资方都为空，则默认新增一个未披露投资方
            if (CollectionUtils.isEmpty(result)) {
                InvestEntity investEntity = new InvestEntity();
                investEntity.setName("未披露");
                investEntity.setKeyNo("");
                investEntity.setId("");
                investEntity.setCategory(InvestTypeEnum.OTHER.getCode());
                investEntity.setType(2);
                investEntity.setOrg(-1);
                result.add(generate(investEntity, brandFinancing));
            }
        }


        return result;
    }

    private List<PlaceFinance> mergeAndRemoveDuplicates(List<PlaceFinance> placeFinanceList) {
        Map<String, PlaceFinance> mergedMap = placeFinanceList.stream()
                .collect(Collectors.toMap(PlaceFinance::getHolderName,
                        f -> f,
                        (existing, replacement) -> mergeFinance(existing, replacement)));

        List<PlaceFinance> result = new ArrayList<>(mergedMap.values());

        placeFinanceList.clear();
        placeFinanceList.addAll(result);

        return placeFinanceList;
    }

    private PlaceFinance mergeFinance(PlaceFinance existing, PlaceFinance replacement) {
        PlaceFinance mergedFinance = new PlaceFinance();
        mergedFinance.setHolderName(existing.getHolderName());
        mergedFinance.setHolderKeyno(existing.getHolderKeyno());
        mergedFinance.setHolderOrgType(existing.getHolderOrgType());
        mergedFinance.setListDate(existing.getListDate());
        mergedFinance.setInstitutionName(existing.getInstitutionName());
        mergedFinance.setInstitutionCode(existing.getInstitutionCode());
        BigDecimal totalAmount = existing.getPlaceAmount().add(replacement.getPlaceAmount());
        BigDecimal totalRate = existing.getPlaceSharesRatio().add(replacement.getPlaceSharesRatio());

        mergedFinance.setPlaceAmount(totalAmount);
        mergedFinance.setPlaceSharesRatio(totalRate);


        return mergedFinance;
    }

    private ScSeFinanceInvestMapping20240320 generate(InvestEntity investEntity, CompBrandFinancing brandFinancing) {
        ScSeFinanceInvestMapping20240320 mapping = new ScSeFinanceInvestMapping20240320();
        mapping.setFinanceId(brandFinancing.getId());
        mapping.setProductId(brandFinancing.getBrandId());
        mapping.setInvestDate(brandFinancing.getFinanceDate());
        mapping.setParticipantType(1);
        String originalName = investEntity.getOriginalName();
        if (StringUtils.isBlank(originalName)) {
            mapping.setInvestKeyword(StringUtil.formatCtName(investEntity.getName()));
            if (StringUtils.isNotBlank(investEntity.getKeyNo())) {
                QccCompanyOut companyOut = CtKeynoUtil.getQccCompanyOut(investEntity.getKeyNo());
                if (null != companyOut) {
                    mapping.setInvestKeyword(StringUtil.formatCtName(companyOut.getName()));
                }
            }
        } else {
            mapping.setInvestKeyword(StringUtil.formatCtName(originalName));
        }
        mapping.setInvestCompKeyno(investEntity.getKeyNo());
        mapping.setInvestFieldType(investEntity.getCategory());
        mapping.setInvestName(investEntity.getName());
        mapping.setInvestId(investEntity.getId());
        mapping.setDataStatus(brandFinancing.getDataStatus());
        mapping.setRound(brandFinancing.getRound());
        mapping.setAmount(brandFinancing.getAmount());
        mapping.setValuation(brandFinancing.getValuation());
        mapping.setSourceType(StringUtils.isNotBlank(brandFinancing.getSourceType())
                && brandFinancing.getSourceType().contains("2") && !brandFinancing.getSourceType().contains("21")
                ? 2 : Integer.valueOf(brandFinancing.getSourceType()));

        mapping.setNewsTitle(brandFinancing.getNewsTitle());
        mapping.setNewsOssId(brandFinancing.getNewsOssId());
        mapping.setNewsOriginalUrl(brandFinancing.getNewsOriginalUrl());
        mapping.setNewsSource(brandFinancing.getNewsSource());

        mapping.setId(MD5Util.encode(mapping.getFinanceId() + mapping.getInvestKeyword()));
        return mapping;
    }

    private List<ScSeFinanceInvestMapping20240320> generatePerson(InvestEntity investEntity, CompBrandFinancing brandFinancing) {
        List<ScSeFinanceInvestMapping20240320> result = new ArrayList<>();
        String personInvestor = investEntity.getName();
        if (StringUtils.equalsIgnoreCase(personInvestor, "个人投资者")) {
            // 仅个人投资者时，投资方类型设为其他
            ScSeFinanceInvestMapping20240320 mapping = generateSinglePerson(personInvestor, brandFinancing);
            mapping.setInvestFieldType(InvestTypeEnum.OTHER.getCode());
            result.add(mapping);
        } else {
            String[] split = personInvestor.replace("个人投资者（", "")
                    .replace("（", "").replace("）", "")
                    .replace("(", "").replace(")", "")
                    .split(",");
            for (String s : split) {
                if (StringUtils.isNotBlank(s.trim())) {
                    result.add(generateSinglePerson(s.trim(), brandFinancing));
                }
            }
        }
        return result;
    }

    private ScSeFinanceInvestMapping20240320 generateSinglePerson(String personName, CompBrandFinancing brandFinancing) {
        ScSeFinanceInvestMapping20240320 mapping = new ScSeFinanceInvestMapping20240320();
        mapping.setFinanceId(brandFinancing.getId());
        mapping.setProductId(brandFinancing.getBrandId());
        mapping.setInvestDate(brandFinancing.getFinanceDate());
        mapping.setInvestName(personName);
        mapping.setParticipantType(1);
        mapping.setInvestKeyword(StringUtil.formatCtName(personName));
        mapping.setInvestCompKeyno("");
        mapping.setInvestFieldType(InvestTypeEnum.PERSON.getCode());
        mapping.setInvestId("");
        mapping.setDataStatus(brandFinancing.getDataStatus());
        mapping.setRound(brandFinancing.getRound());
        mapping.setAmount(brandFinancing.getAmount());
        mapping.setValuation(brandFinancing.getValuation());
        mapping.setSourceType(StringUtils.isNotBlank(brandFinancing.getSourceType()) && brandFinancing.getSourceType().contains("2")
                ? 2 : Integer.valueOf(brandFinancing.getSourceType()));
        mapping.setId(MD5Util.encode(mapping.getFinanceId() + mapping.getInvestKeyword()));

        mapping.setNewsTitle(brandFinancing.getNewsTitle());
        mapping.setNewsOssId(brandFinancing.getNewsOssId());
        mapping.setNewsOriginalUrl(brandFinancing.getNewsOriginalUrl());
        mapping.setNewsSource(brandFinancing.getNewsSource());

        return mapping;
    }
    //ipo数据处理
    private List<ScSeFinanceInvestMapping20240320> handleIPO(List<PlaceFinance> placeFinanceList, CompBrandFinancing brandFinancing) {
        List<ScSeFinanceInvestMapping20240320> result = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(placeFinanceList)){
            for(PlaceFinance pf :placeFinanceList){
                ScSeFinanceInvestMapping20240320 mapping = new ScSeFinanceInvestMapping20240320();
                mapping.setFinanceId(brandFinancing.getId());
                mapping.setProductId(brandFinancing.getBrandId());
                mapping.setInvestDate(brandFinancing.getFinanceDate());
                mapping.setInvestName(StringUtils.isNotBlank(pf.getInstitutionName()) ? pf.getInstitutionName() : pf.getHolderName());
                mapping.setInvestId(pf.getInstitutionCode());
                mapping.setInvestCompKeyno(pf.getHolderKeyno());
                mapping.setInvestFieldType(pf.getHolderOrgType());
                mapping.setInvestDate(brandFinancing.getFinanceDate());
                mapping.setInvestKeyword(pf.getHolderName());
                mapping.setRound(brandFinancing.getRound());
                if(null != pf.getPlaceAmount()){
                    mapping.setAmount(pf.getPlaceAmount().toString());
                }
                if(null != pf.getPlaceSharesRatio()){
                    mapping.setRate(pf.getPlaceSharesRatio().toString());
                }
                mapping.setValuation(brandFinancing.getValuation());
                mapping.setSourceType(3);
                mapping.setParticipantType(3);
                mapping.setNewsSource(brandFinancing.getNewsSource());
                mapping.setNewsOssId(brandFinancing.getNewsOssId());
                mapping.setNewsOriginalUrl(brandFinancing.getNewsOriginalUrl());
                mapping.setNewsTitle(brandFinancing.getNewsTitle());
                mapping.setFinancingType(1);
                mapping.setId(MD5Util.encode(mapping.getFinanceId() + mapping.getInvestKeyword()));
                result.add(mapping);
            }
        }
        return result;
    }
}