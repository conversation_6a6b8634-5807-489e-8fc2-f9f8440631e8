package com.ld.clean.constants;

import lombok.Getter;

/**
 * 上市板块类型
 *
 * <AUTHOR>
 */
@Getter
public enum ListSectionEnum {
    MAINBOARD(1, "主板"),
    SMEBOARD(2, "中小板"),
    GEM(3, "创业板"),
    STI(4, "科创板"),
    THIRD(9, "三板"),
    BASE(901, "基础层"),
    INNOVARE(902, "创新层"),
    SELECTED(903, "精选层"),
    DELISTING(909, "两网及退市"),
    OTHER(999, "其他")
    ;
    private Integer code;

    private String desc;


    ListSectionEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static ListSectionEnum getByCode(int code) {
        for (ListSectionEnum roundLevelEnum : ListSectionEnum.values()) {
            if (code == roundLevelEnum.getCode()) {
                return roundLevelEnum;
            }
        }
        return OTHER;
    }

}

