package com.ld.clean.test.mainbrand;

import com.ld.clean.context.ApplicationCoreContextManager;
import com.ld.clean.model.CompBrandFinancing;
import com.ld.clean.parent.AsyncCleanParentFunction;
import com.ld.clean.repository.CompBrandFinancingRepository;
import com.ld.clean.test.mainbrand.dao.ScSeCompBrandFinancing20240513;
import com.ld.clean.test.mainbrand.dao.ScSeCompBrandFinancing20240513Repository;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;


/**
 * dap数据插入数据库
 *
 * <AUTHOR>
 */
public class TestInsertCompBrandFinanceFunction extends AsyncCleanParentFunction<List<ScSeCompBrandFinancing20240513>, List<ScSeCompBrandFinancing20240513>> {

    private static final ScSeCompBrandFinancing20240513Repository compBrandFinancingRepository = ApplicationCoreContextManager.getInstance(ScSeCompBrandFinancing20240513Repository.class);

    public TestInsertCompBrandFinanceFunction(int corePoolSize) {
        super(corePoolSize);
    }

    @Override
    public List<ScSeCompBrandFinancing20240513> invoke(List<ScSeCompBrandFinancing20240513> list) throws Exception {
        if (CollectionUtils.isNotEmpty(list)) {
            compBrandFinancingRepository.insertBatch(list);

        }
        return list;
    }
}
