package com.ld.clean.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.base.clean.entity.WeightProductEntity;
import com.ld.clean.NlpService;
import com.ld.clean.context.ApplicationCoreContextManager;
import com.ld.clean.core.dto.nlp.NlpSyncRequest;
import com.ld.clean.enums.DimensionEnum;
import kong.unirest.Unirest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date: 2019/2/24 10:32
 * @Description:
 */
@Slf4j
public class NlpUtil {
    private static final NlpService nlpService = ApplicationCoreContextManager.getInstance(NlpService.class);

    private static final String ADD_BLACKLIST = "http://**************:9901/data/add_blacklist_words/";
    private static final String CHECK_BLACKLIST = "http://**************:9901/data/check_blacklist/";
    private static final String DELETE_BLACKLIST = "http://**************:9901/data/delete_blacklist/";

    /**
     * 获取产品行业分类标签权重
     *
     * @param intro    产品简介
     * @param industry 行业分类code  多个以英文逗号分隔
     * @return weightProduct
     */
    public static List<WeightProductEntity> weightProduct(String intro, String industry) {
        List<WeightProductEntity> list = new ArrayList<>();
        try {
            int retry = 3;
            while (retry-- > 0) {
//                String response = QccUnirest.post(WEIGHT_PRODUCT)
//                        .header("content-type", "application/x-www-form-urlencoded; charset=UTF-8")
//                        .field("intro", intro)
//                        .field("industry", industry)
//                        .asString().getBody();

                JSONObject jsonObject = new JSONObject();
                jsonObject.put("intro", intro);
                jsonObject.put("industry", industry);
                String response = nlpService.callNlpSync(NlpSyncRequest.builder()
                        .dimType(DimensionEnum.RONG_ZI_DONG_TAI.getType())
                        .businessType(54)
                        .requestParam(jsonObject)
                        .build());
                if (StringUtils.isNotEmpty(response) && response.length() > 10) {
                    list = JSON.parseArray(response, WeightProductEntity.class);
                }
            }
        } catch (Exception e) {
            log.error("weightProduct err,input:{},e:{}", industry + ": " + intro, e.getMessage());
        } finally {
            return list;
        }
    }

    /**
     * 删除nlp黑名单
     *
     * @param words 需要删除nlp黑名单的词
     */
    public static boolean deleteBlacklist(String words) {
        if (!checkBlacklist(words)) {
            return true;
        }
        Map<String, Object> fields = new HashMap<>(5);
        fields.put("word", words);
        int retry = 5;
        while (retry-- > 0) {
            try {
                String res = Unirest.post(DELETE_BLACKLIST).header("content-type", "application/x-www-form-urlencoded; charset=UTF-8").fields(fields).asString().getBody();
                if ("移除成功".equals(res)) {
                    return true;
                }
            } catch (Exception e) {
                if (retry == 0) {
                    log.info("调用 NLPAPI Blacklist 异常");
                }
            }
        }
        return false;
    }


    /**
     * 检查词语是否在nlp黑名单
     *
     * @param words 需要检查nlp黑名单的词
     */
    private static boolean checkBlacklist(String words) {
        Map<String, Object> fields = new HashMap<>(5);
        fields.put("word", words);
        int retry = 5;
        while (retry-- > 0) {
            try {
                String res = Unirest.post(CHECK_BLACKLIST).header("content-type", "application/x-www-form-urlencoded; charset=UTF-8").fields(fields).asString().getBody();
                if ("True".equals(res)) {
                    return true;
                }
                if ("False".equals(res)) {
                    return false;
                }
            } catch (Exception e) {
                if (retry == 0) {
                    log.info("调用 NLPAPI Blacklist 异常");
                }
            }
        }
        return false;
    }

    /**
     * 添加至nlp黑名单
     *
     * @param words 需要加入nlp黑名单的词
     */
    public static boolean addToBlacklist(String words) {
        Map<String, Object> fields = new HashMap<>(5);
        fields.put("add_word", words);
        int retry = 5;
        while (retry-- > 0) {
            try {
                String res = Unirest.post(ADD_BLACKLIST).header("content-type", "application/x-www-form-urlencoded; charset=UTF-8").fields(fields).asString().getBody();
                if ("\"添加成功\"".equals(res)) {
                    log.info("添加词语：{} 至nlp黑名单成功!", words);
                    return true;
                }
                if ("\"已存在\"".equals(res)) {
                    log.info("添加词语：{} 至nlp黑名单已存在!", words);
                    return true;
                }
            } catch (Exception e) {
                if (retry == 0) {
                    log.error("调用 NLPAPI Blacklist 异常");
                }
            }
        }
        return false;
    }
}
