package com.ld.clean.constants;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 */

@Getter
@AllArgsConstructor
public enum ContactTypeEnum {
    TEL("tel", 1),
    EMAIL("email", 2),
    ADDRESS("address", 3);

    private String name;
    private Integer code;

    public static ContactTypeEnum getByCode(Integer code) {
        for (ContactTypeEnum tagEnum : ContactTypeEnum.values()) {
            if (tagEnum.getCode().equals(code)) {
                return tagEnum;
            }
        }
        return null;
    }

    public static ContactTypeEnum getByName(String name) {
        for (ContactTypeEnum tagEnum : ContactTypeEnum.values()) {
            if (tagEnum.getName().equals(name)) {
                return tagEnum;
            }
        }
        return null;
    }
}
