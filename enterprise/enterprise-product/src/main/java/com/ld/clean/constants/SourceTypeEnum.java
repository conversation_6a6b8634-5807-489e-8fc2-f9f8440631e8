package com.ld.clean.constants;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 事件来源类型
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum SourceTypeEnum {
    OTHER("异常类型", -1),
    PUBLIC("公开披露", 1),
    EQCHANGE("股权变更", 2),
    FINSUP("金融补充", 3),
    NOTICE("公告", 4),
    NOTICEBJS("公告北交所", 41);

    private Integer code;

    private String desc;



    SourceTypeEnum(String desc, Integer code) {
        this.code = code;
        this.desc = desc;
    }

    public static SourceTypeEnum getByCode(int code) {
        for (SourceTypeEnum roundLevelEnum : SourceTypeEnum.values()) {
            if (code == roundLevelEnum.getCode()) {
                return roundLevelEnum;
            }
        }
        return OTHER;
    }

    public static SourceTypeEnum getCodeByUrl(String url) {

       /* String type1 = "https://ggzyfw.beijing.gov.cn";
        String type2 = "https://www.cbex.com.cn";*/

        String type3 = "https://www.qcc.com/web/tools-finance";
        String type4 = "https://qccdata.qichacha.com/ReportData/PDF";

        if(url.contains(type3) || url.contains(type4)){
            return NOTICE;
        }
        return PUBLIC;
    }

}

