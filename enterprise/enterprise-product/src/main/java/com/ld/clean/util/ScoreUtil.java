package com.ld.clean.util;

import com.ld.clean.constants.FinanceTotalRmbScoreEnum;
import com.ld.clean.enums.RoundLevelEnum;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
public class ScoreUtil {

    private static final Integer BASE_SCORE = 100;

    /**
     * 根据范围得出归一化数据
     *
     * @param max
     * @param min
     * @param num
     * @return
     */
    public static BigDecimal getStandardByRange(BigDecimal num, BigDecimal min, BigDecimal max) {
        if (num.compareTo(min) == -1) {
            num = min;
        }
        if (num.compareTo(max) == 1) {
            num = max;
        }
        return num.subtract(min).divide(max.subtract(min)).setScale(2, BigDecimal.ROUND_HALF_UP);
    }


    public static BigDecimal generateScoreByProduct(BigDecimal financeTotal, Integer roundLevel, Integer financeTimes, Integer newsSize, Integer hotFlag) {
        BigDecimal roundScore = ScoreUtil.getStandardByRange(new BigDecimal(RoundLevelEnum.getRoundLevelByCode(roundLevel).getScore()), new BigDecimal(0), new BigDecimal(10));
        BigDecimal roundCnt = ScoreUtil.getStandardByRange(new BigDecimal(null == financeTimes ? 0 : financeTimes), new BigDecimal(0), new BigDecimal(40));
        BigDecimal newsCnt = ScoreUtil.getStandardByRange(new BigDecimal(newsSize), new BigDecimal(0), new BigDecimal(40));
//        BigDecimal financeScore = ScoreUtil.getStandardByRange(null == financeTotal ? new BigDecimal(0) : financeTotal, new BigDecimal(100000000), new BigDecimal(10000000000L));

        float scoreByMoney = FinanceTotalRmbScoreEnum.getScoreByMoney(null == financeTotal ? 0 : financeTotal.longValue());

        BigDecimal score = roundScore.multiply(new BigDecimal(0.3))
                .add(roundCnt.multiply(new BigDecimal(0.2)))
                .add(newsCnt.multiply(new BigDecimal(0.1)))
                .add(new BigDecimal(scoreByMoney).multiply(new BigDecimal(0.4)))
                .multiply(new BigDecimal(100))
                .setScale(2, BigDecimal.ROUND_HALF_UP);

        // 如果被标记为热门产品，则加上100分的基础分，保证分数大于当前最大的产品得分，如果本身产品分数已经可以超过这个基础分，则该产品就应该被标记为热门产品
        if (null != hotFlag && hotFlag == 1) {
            score = score.add(new BigDecimal(BASE_SCORE));
        }
        return score;
    }

    /**
     * 根据参数计算权重
     *
     * @param institutionId
     * @param compkeyno
     * @param persinId
     * @return
     */

    public static Long getStandardSortWeightByInstitutionIdAndKeyno(String institutionId,String compkeyno,String persinId,Integer holderType) {
        Long weight = 0L;
        if (StringUtils.isNotBlank(institutionId) && StringUtils.isNotBlank(compkeyno)) {
            weight = 1000000000L;
            for (int i = 0; i < institutionId.length(); i++) {
                char c = institutionId.charAt(i);
                int ascii = (int) c;
                weight += ascii * ascii * i * i;
            }
            weight+=institutionId.hashCode()/100000000L;
        } else if (StringUtils.isNotBlank(institutionId) && StringUtils.isBlank(compkeyno)) {
            weight = 800000000L;
            for (int i = 0; i < institutionId.length(); i++) {
                char c = institutionId.charAt(i);
                int ascii = (int) c;
                weight += ascii * ascii * i * i;
            }
            weight+=institutionId.hashCode()/100000000L;
        } else if (StringUtils.isBlank(institutionId) && StringUtils.isNotBlank(compkeyno)) {
            weight = 600000000L;
        } else if (StringUtils.isBlank(institutionId) && StringUtils.isBlank(compkeyno) && holderType == 1) {
            weight = 400000000L;
        } else if (StringUtils.isNotBlank(persinId)) {
            weight = 200000000L;
        } else {
            weight = 100000000L;
        }
        System.out.println(weight);
        return weight;
    }

    public static void main(String[] args) {
        String ins = "b880e76c242456caede03ff58070996b";
        String keyno = "dc1a2e70d4abd7fe96448bd9c59c636c";
        String pid = "";
        Integer holderType = 0;
        Long weight = 0L;
        if (StringUtils.isNotBlank(ins) && StringUtils.isNotBlank(keyno)) {
            weight = 1000000000L;


            for (int i = 0; i < ins.length(); i++) { // 确保索引不会超出范围
                char c = ins.charAt(i);
                int ascii = (int) c;// 安全访问字符
                weight += ascii * ascii * i * i;
            }
            weight+=ins.hashCode()/100000000L;
        } else if (StringUtils.isNotBlank(ins) && StringUtils.isNotBlank(keyno)) {
            weight = 800000000L;
        } else if (StringUtils.isNotBlank(ins) && StringUtils.isBlank(keyno)) {
            weight = 600000000L;
            for (int i = 0; i < ins.length(); i++) { // 确保索引不会超出范围
                char c = ins.charAt(i);
                int ascii = (int) c;// 安全访问字符
                weight += ascii * ascii * i * i;
            }
            weight+=ins.hashCode()/100000000L;
        } else if (StringUtils.isBlank(ins) && StringUtils.isNotBlank(keyno)) {
            weight = 400000000L;
        } else if (StringUtils.isBlank(ins) && StringUtils.isBlank(keyno) && holderType == 1) {
            weight = 200000000L;
        } else if (StringUtils.isNotBlank(pid)) {
            weight = 100000000L;
        } else {
            weight = 99999999L;
        }
        System.out.println(weight);


    }
}
