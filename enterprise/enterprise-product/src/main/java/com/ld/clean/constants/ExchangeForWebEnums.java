package com.ld.clean.constants;

import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

/**
 * 交易所类型简称
 *
 * <AUTHOR>
 */
@Getter
public enum ExchangeForWebEnums {
    BJ_MAIN_BOARD("北京证券交易所主板", "北交所","140"),
    SH_STAR("上海证券交易所科创板", "科创板","110"),
    SH_MAIN_BOARD("上海证券交易所主板", "沪主板","100"),
    SZ_GEM("深圳证券交易所创业板", "创业板","130"),
    SZ_MAIN_BOARD("深圳证券交易所主板", "深主板","120"),
    HK_GEM("香港交易所创业板", "港交所","150"),
    HK_MAIN_BOARD("香港交易所主板", "港交所","150"),
    AMERICAN_STOCK_EXCHANGE("美国证券交易所", "美股","160"),
    NEW_YORK_STOCK_EXCHANGE("纽约证券交易所", "美股","160"),
    NASDAQ("纳斯达克交易所", "美股","160"),
    NEW_THIRD_BOARD("新三板", "新三板","888"),
    OTHER("其它", "","999"),
    BJ_DE_LIST("北京证券交易所", "北交所","140"),
    SH_DE_LIST("上海证券交易所", "上交所",""),
    SZ_DE_LIST("深圳证券交易所", "深交所",""),
    HK_DE_LIST("香港交易所", "港交所","150");

    private String code;
    private String name;
    private String numberCode;

    ExchangeForWebEnums(String code, String name,String numberCode) {
        this.code = code;
        this.name = name;
        this.numberCode = numberCode;

    }

    /**
     * 当出现枚举值以外的数值时，给与默认值
     */
    public static ExchangeForWebEnums getByCode(String code) {
        if (StringUtils.isNotBlank(code)) {
            for (ExchangeForWebEnums exchangeEnums : ExchangeForWebEnums.values()) {
                if (exchangeEnums.getCode().equals(code)) {
                    return exchangeEnums;
                }
            }
        }
        return OTHER;
    }
    public static ExchangeForWebEnums getByName(String name) {
        if (StringUtils.isNotBlank(name)) {
            for (ExchangeForWebEnums exchangeEnums : ExchangeForWebEnums.values()) {
                if (exchangeEnums.getName().equals(name)) {
                    return exchangeEnums;
                }
            }
        }
        return OTHER;
    }
}
