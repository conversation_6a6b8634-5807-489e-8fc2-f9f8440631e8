package com.ld.clean.util;

import com.ld.clean.context.ApplicationCoreContextManager;
import com.ld.clean.model.manageenterprisedb.CompAffiliatedProducts;
import com.ld.clean.repository.manageenterprisedb.CompAffiliatedProductsRepository;
import com.ld.clean.utils.CtCommonUtils;
import com.ld.clean.utils.MD5Util;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
public class ProductUtils {

    private static final CompAffiliatedProductsRepository compAffiliatedProductsRepository = ApplicationCoreContextManager.getInstance(CompAffiliatedProductsRepository.class);

    private static final HashMap<String,Integer> PRODUCT_SORT_MAP = new HashMap<String,Integer>(){{
        put("0",0);   put("9",1);   put("3",2);   put("5",3); put("8",4); put("4",5);
    }};
    public static final Set<String> INVEST_NAME_BLACK = new HashSet(Arrays.asList("inc.", "llc", "global", "india"));
    /*public static Map<String, Integer> CITY_CODE = new HashMap<>();


    private static EciDataMongoTemplate eciDataMongoTemplate = ApplicationCoreContextManager.getInstance(EciDataMongoTemplate.class);


    private static void initCityCode() {
        List<MongoArea> areas = eciDataMongoTemplate.getMongoTemplate().findAll(MongoArea.class);
        for (MongoArea area : areas) {
            String code = area.getId().toString().substring(2, 4);
            if ("00".equals(code)) {
                continue;
            }
            CITY_CODE.put(area.getCity(), Integer.parseInt(code));
        }
    }*/

    public static String formatInvestName(String name) {
        if (StringUtils.isEmpty(name) || "未披露".equalsIgnoreCase(name)) {
            return "";
        }
        if (name.contains("}]") || name.contains("[{") || name.contains("\"KeyNo\"") || name.contains("\"Name\"")
                || name.contains("\"Id\"")) {
            return "";
        }
        name = name.replaceAll("[\t\n]", "").trim();
//        name = name.replaceAll("等|主投|领投|跟投|参投|追投|联合投资|共同投资|多家企业", "").trim();
        if (StringUtils.isEmpty(name)) {
            return "";
        }
        if (ProductUtils.INVEST_NAME_BLACK.contains(name.toLowerCase())) {
            return "";
        }
        return name;
    }

    public static Set<String> getInvestNames(String invests) {
        Set<String> set = new HashSet<>();
        if (StringUtils.isNotBlank(invests)) {
            // 全量替换成中文括号
            // 替换非法字符 "\uFEFF"
            invests = invests.replace("（", "(").replace("）", ")").replace("\uFEFF", "");

            if (StringUtils.isNotEmpty(invests)) {
                // 先截取括号内的公司名称
                set.addAll(StringUtil.extractBracketInnerStr(invests));
                // 再剔除括号内的公司名称
                invests = StringUtil.extractBracketOuterStr(invests);

                String[] leadInvests = invests.split("[,，、]");
                for (String invest : leadInvests) {
                    String name = ProductUtils.formatInvestName(invest);
                    if (StringUtils.isNotEmpty(name)) {
                        set.add(name.trim());
                    }
                }
            }
        }
        return set;
    }

    public static List<CompAffiliatedProducts> dealCurrentList(List<CompAffiliatedProducts> compAffiliatedProducts,List<CompAffiliatedProducts> parentCompAffiliatedProducts) {
        //處理當前list 重複的keyno

        List<CompAffiliatedProducts> values =compAffiliatedProducts.stream().collect(Collectors.collectingAndThen(Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(CompAffiliatedProducts::getId))), ArrayList::new));

        //无效掉和主体企业重复的数据

        List<String> mainProductsKeynos = values.stream().filter(it->it.getDataStatus() ==1 &&StringUtils.isNotBlank(it.getCompKeyno()) && (it.getType()==21 || it.getType()==25)).map(CompAffiliatedProducts::getCompKeyno).collect(Collectors.toList());

        values.forEach(it->{
           if( it.getType() != 21 && it.getType() != 25 && it.getDataStatus() ==1 &&mainProductsKeynos.contains(it.getCompKeyno())){
               it.setDataStatus(3);
               it.setRemark("和主体企业重复的主体...直接取消1");
            };
        });


        values.stream().filter(vv->vv.getDataStatus() ==1 && StringUtils.isNotBlank(vv.getCompKeyno())).collect(Collectors.groupingBy(CompAffiliatedProducts::getCompKeyno)).forEach((k,v)->{
            if(v.size()>1){
               //两个都是主体企业  随机无效一个 以及他的下级企业都无效 并且告警  这场景忽略。。数据异常场景理论不存在
                v.forEach(vv->{
                    vv.setDataStatus(3);
                    vv.setRemark("优先级过低被无效_1");
                });
                List<CompAffiliatedProducts> parentKeyno =new ArrayList<>();
               //一个主体企业  一个非主体企业   无效非主体在企业
                v.forEach(vv->{
                    CompAffiliatedProducts op =compAffiliatedProducts.stream().filter(itt->itt.getDataStatus()==1 && StringUtils.isNotBlank(itt.getCompKeyno()) &&itt.getProductId().equals(vv.getProductId())&&itt.getCompKeyno().equals(vv.getParentCompKeyno())).findFirst().orElse(parentCompAffiliatedProducts.stream().filter(itt->itt.getDataStatus() ==1 && StringUtils.isNotBlank(itt.getCompKeyno()) &&itt.getProductId().equals(vv.getProductId())&&itt.getCompKeyno().equals(vv.getParentCompKeyno())).findFirst().orElse(null));
                    if(op!=null){
                        parentKeyno.add(op);
                    }
                });

                CompAffiliatedProducts op =parentKeyno.stream().min(Comparator.comparing(vvv->{
                    return Arrays.stream(vvv.getRule().split(",")).map(vv->PRODUCT_SORT_MAP.getOrDefault(vv,100)).min(Comparator.naturalOrder()).get();
                })).orElse(null);

                if(op !=null){
                    CompAffiliatedProducts current = v.stream().filter(uu->uu.getParentCompKeyno().equals(op.getCompKeyno()) && uu.getProductId().equals(op.getProductId())).findFirst().get();

                    current.setDataStatus(1);
                    current.setRemark("");
                }


                //两个都是非主体企业  对比parent keyno
            }
        });
        return values;

    }

        public static List<CompAffiliatedProducts> compareCurentKeynos(List<CompAffiliatedProducts> newList, List<CompAffiliatedProducts> oldList,List<CompAffiliatedProducts> parentKeynoAll) {
        List<CompAffiliatedProducts> result = new ArrayList<>();

        //获取所有的parnetKeynoList
        List<String> parentKeynoList =newList.stream().filter(itt->itt.getDataStatus() ==1 &&StringUtils.isNotBlank(itt.getParentCompKeyno())).map(CompAffiliatedProducts::getParentCompKeyno).distinct().collect(Collectors.toList());

        parentKeynoList.addAll(oldList.stream().filter(itt->itt.getDataStatus() ==1 &&StringUtils.isNotBlank(itt.getParentCompKeyno())).map(CompAffiliatedProducts::getParentCompKeyno).distinct().collect(Collectors.toList()));
        List<CompAffiliatedProducts> parentKeyno =new ArrayList<>();
        if(!parentKeynoList.isEmpty()){
          //  parentKeyno.addAll(compAffiliatedProductsRepository.getProductCompByKeyNo(parentKeynoList));
            parentKeyno.addAll(parentKeynoAll.stream().filter(op->parentKeynoList.contains(op.getCompKeyno())).collect(Collectors.toList()));
        }


            //这边有comp_keyno为空的怎么处理
        List<String> compKeynos = newList.stream().map(CompAffiliatedProducts::getCompKeyno).distinct().collect(Collectors.toList());
        //合并库里面的户籍
        compKeynos.forEach(v -> {
            List<CompAffiliatedProducts> newCompList = newList.stream().filter(itt -> itt.getCompKeyno().equals(v)).collect(Collectors.toList());
            if (StringUtils.isBlank(v)) {
                result.addAll(newCompList);
                return;
            }
            List<CompAffiliatedProducts> oldCompList = oldList.stream().filter(itt -> newList.stream().noneMatch(kk -> kk.getId().equals(itt.getId())) && itt.getCompKeyno().equals(v)).collect(Collectors.toList());
          /*  CompAffiliatedProducts modifyData = oldCompList.stream().filter(itt -> itt.getDataStatus() == 2).findFirst().orElse(null);
            CompAffiliatedProducts currentData = newCompList.stream().filter(itt -> itt.getDataStatus() == 1).findFirst().orElse(null);
            if (modifyData != null && currentData != null) {
                //这边要告警
                if(currentData.getProductId().equals(modifyData.getProductId())) {

                    String content = "【告警描述】：该成员企业在当前项目存在无效记录,请确认" + "\n" + "【企业名称】：" + currentData.getCompName() + "\n" + "【企业Keyno】：" + currentData.getCompKeyno() + "\n" + "【当前新增项目id】：" + currentData.getProductId() + "(" + currentData.getProductName() + ")" + "\n" + "【新成员数据Id】：" + currentData.getId() + "\n" + "【旧成员数据Id】：" + modifyData.getId() + "\n";
                }
                if (modifyData.getProductId().equals(currentData.getProductId()) && StringUtils.isNotBlank(currentData.getParentCompKeyno())&&modifyData.getType().equals(currentData.getType())&&modifyData.getParentCompKeyno().equals(currentData.getParentCompKeyno())) {
                   currentData.setRemark("存在相同产品项目下面无效的数据");
                    if (modifyData.getArtificial() == 1) {
                        currentData.setDataStatus(3);
                        result.addAll(newCompList);
                        return;
                    }
                }
            }*/
            CompAffiliatedProducts modifyDataValid = oldCompList.stream().filter(itt -> itt.getDataStatus() == 1).findFirst().orElse(null);
            CompAffiliatedProducts currentDataValid = newCompList.stream().filter(itt -> itt.getDataStatus() == 1).findFirst().orElse(null);
            if (modifyDataValid != null && currentDataValid != null) {

               /* String content1 = "【投资机构 -下属企业库重复异常】" + "\n" + "【新下企机构Id】：" + currentDataValid.getInstitutionId() + "\n" + "【新下企企业Keyno】：" + currentDataValid.getCompKeyno() + "\n" + "【新下企数据Id】：" + currentDataValid.getId() + "\n" + "【旧下企机构Id】：" + modifyDataValid.getInstitutionId() + "\n" + "【旧下企企业Keyno】：" + modifyDataValid.getCompKeyno() + "\n" + "【旧下企数据Id】：" + modifyDataValid.getId() + "\n" + "【异常描述】：下属企业存在重复KeyNo的数据，请确认处理";
                if(!getUniqueIdFromCahce(content1)) {
                    DingDingUtil.sendDmp2(NotificationEntity.builder().sendType(Arrays.asList("2")).executorUser(Arrays.asList("xulc")).sendUser("xulc").sendTitle("【投资机构下属企业库异常】").sendMessage(content1).dingdingToken("https://oapi.dingtalk.com/robot/send?access_token=" + ProductStringUtil.SIVS_INSTITUTION_TOKEN_GROUP).alarmType(4).alarmLevel(2).textType(0).isAt(0).build());

                }*/

                if (modifyDataValid.getArtificial() == 1 || currentDataValid.getArtificial() == 1) {

                    //如果兩個都是 輸入的
                    //如果兩個都是 輸入的
                    //如果兩個都是 輸入的    //如果兩個都是 輸入的  怎麽處理

                    if (modifyDataValid.getArtificial() == 1 && currentDataValid.getArtificial() == 1) {
                        String content = "【品牌项目 -成员企业库冲突异常】" + "\n" + "【新成员项目Id】：" + currentDataValid.getProductId() + "\n" + "【新成员企业Keyno】：" + currentDataValid.getCompKeyno() + "\n" + "【新成员数据Id】：" + currentDataValid.getId() + "\n" + "【旧成员项目Id】：" + modifyDataValid.getProductId() + "\n" + "【旧成员企业Keyno】：" + modifyDataValid.getCompKeyno() + "\n" + "【旧成员数据Id】：" + modifyDataValid.getId() + "\n" + "【异常描述】：成员企业存在采编修改记录,已被修改，请确认处理";
                        CtCommonUtils.sendDingDing(content);
                        modifyDataValid.setDataStatus(3);
                        modifyDataValid.setRemark("对应数据存在采编修改记录1");
                        result.add(modifyDataValid);
                    } else if (modifyDataValid.getArtificial() == 1) {
                        if(currentDataValid.getType() ==1){
                            modifyDataValid.setDataStatus(3);
                            modifyDataValid.setRemark("对应数据是主体企业,无视采编数据");
                            result.add(modifyDataValid);
                        }else {
                            currentDataValid.setDataStatus(3);
                            currentDataValid.setRemark("对应数据存在采编修改记录2");
                        }

                    } else if (currentDataValid.getArtificial() == 1) {
                        modifyDataValid.setDataStatus(3);
                        modifyDataValid.setRemark("对应数据存在采编修改记录3");
                        result.add(modifyDataValid);
                    }
                    result.addAll(newCompList);
                    return;
                }
                //这边不等于1 要具体分情况而言：
                //第一种：就是currentDataValid 的parentkeyno在当前列表号 还没insert
                //第二种：就是两个的parentkeyno 一样的？
                //第三种：就是就是没有的那个。、、告警。。然后把没有parentkeyno 的无效
                CompAffiliatedProducts olold = newList.stream().filter(vb -> modifyDataValid.getParentCompKeyno().equals(vb.getCompKeyno())).findFirst().orElse(null);
                CompAffiliatedProducts newew = newList.stream().filter(vb -> currentDataValid.getParentCompKeyno().equals(vb.getCompKeyno())).findFirst().orElse(null);
                if (olold == null || newew == null) {
                    // 两个都为null 当前列表里找不到对应父级的数据  很有可能是主体企业 newew为null 应该是主体企业。。
                    if (StringUtils.isBlank(modifyDataValid.getParentCompKeyno()) || StringUtils.isBlank(currentDataValid.getParentCompKeyno())) {
                        String content = "【品牌项目 -主体企业与成员企业重复异常】" + "\n" + "【当前成员企业项目内码】：" + currentDataValid.getProductId() + "\n" + "【当前成员企业公司内码】：" + currentDataValid.getCompKeyno() + "\n" + "【当前成员企业公司来源】：" + currentDataValid.getId() + "\n" + "【历史成员企业项目内码】：" + modifyDataValid.getProductId() + "\n" + "【历史成员企业公司内码】：" + modifyDataValid.getCompKeyno() + "\n" + "【历史成员企业公司来源】：" + modifyDataValid.getId() + "\n" + "【异常描述】：成员企业库主体企业重复异常，请确认处理";
                       // if(!getUniqueIdFromCahce(content)) { DingDingUtil.sendDmp2(NotificationEntity.builder().sendType(Arrays.asList("2")).executorUser(Arrays.asList("wangl1")).sendUser("wangl1").sendTitle("【品牌项目成员企业库异常】").sendMessage(content).dingdingToken("https://oapi.dingtalk.com/robot/send?access_token=fc8b35a95b24d1f1687962a23ede5f69c6a759cac8bf07b0e676ff40f7245239").alarmType(4).alarmLevel(2).textType(0).isAt(0).build());}
                        CtCommonUtils.sendDingDing(content);
                        if (StringUtils.isBlank(modifyDataValid.getParentCompKeyno()) && StringUtils.isBlank(currentDataValid.getParentCompKeyno())) {
                            if (currentDataValid.getType() > modifyDataValid.getType()) {
                                currentDataValid.setDataStatus(3);
                                currentDataValid.setRemark("当前主体企业重复异常1：对应数据id："+modifyDataValid.getId());
                            } else {
                                modifyDataValid.setDataStatus(3);
                                modifyDataValid.setRemark("历史主体企业重复异常1：对应数据id："+currentDataValid.getId());
                                result.add(modifyDataValid);
                            }
                        } else if (StringUtils.isBlank(modifyDataValid.getParentCompKeyno())) {
                            currentDataValid.setDataStatus(3);
                            currentDataValid.setRemark("当前主体企业重复异常：对应数据id："+modifyDataValid.getId());
                        } else {
                            modifyDataValid.setDataStatus(3);
                            modifyDataValid.setRemark("历史主体企业重复异常：对应数据id："+currentDataValid.getId());
                            result.add(modifyDataValid);
                        }
                        result.addAll(newCompList);
                        return;
                    } else {
                        //这边else 暂时不需要逻辑
                    }
                    //  List<CompAffiliatedInstitutionNew> parentKeyno = compAffiliatedInstitutionNewRepository.getInstitutionCompByKeyNo(Arrays.asList(modifyDataValid.getParentCompKeyno(), currentDataValid.getParentCompKeyno()), Collections.singletonList(1));
                    if (olold == null) {
                        olold = parentKeyno.stream().filter(vb -> modifyDataValid.getParentCompKeyno().equals(vb.getCompKeyno())).findFirst().orElse(null);
                        if (olold == null) {
                            String content = "【品牌项目 -成员企业库获取ParentKeyno异常】" + "\n" + "【新成员项目Id】：" + currentDataValid.getProductId() + "\n" + "【新成员企业Keyno】：" + currentDataValid.getCompKeyno() + "\n" + "【新成员数据Id】：" + currentDataValid.getId() + "\n" + "【旧成员项目Id】：" + modifyDataValid.getProductId() + "\n" + "【旧成员数据Keyno】：" + modifyDataValid.getCompKeyno() + "\n" + "【旧成员数据Id】：" + modifyDataValid.getId() + "\n" + "【异常描述】：旧成员企业库获取父级数据异常，疑似穿透链断开,请确认处理";
                            //ProductStringUtil.sendMsgToDingdingWithToken(content, "",ProductStringUtil.SIVS_INSTITUTION_TOKEN_GROUP);
                           // if(!getUniqueIdFromCahce(content)) { DingDingUtil.sendDmp2(NotificationEntity.builder().sendType(Arrays.asList("2")).executorUser(Arrays.asList("xulc")).sendUser("xulc").sendTitle("【品牌项目成员企业库异常】").sendMessage(content).dingdingToken("https://oapi.dingtalk.com/robot/send?access_token=fc8b35a95b24d1f1687962a23ede5f69c6a759cac8bf07b0e676ff40f7245239").alarmType(4).alarmLevel(2).textType(0).isAt(0).build());}
                            CtCommonUtils.sendDingDing(content);
                            modifyDataValid.setDataStatus(3);
                            modifyDataValid.setRemark("获取上层企业失败");
                            result.add(modifyDataValid);
                        }
                    }
                    if (newew == null) {
                        newew = parentKeyno.stream().filter(vb -> currentDataValid.getParentCompKeyno().equals(vb.getCompKeyno())).findFirst().orElse(null);
                        if (newew == null) {
                            String content = "【品牌项目 -成员企业库获取ParentKeyno异常】" + "\n" + "【新成员项目Id】：" + currentDataValid.getProductId() + "\n" + "【新成员企业Keyno】：" + currentDataValid.getCompKeyno() + "\n" + "【新成员数据Id】：" + currentDataValid.getId() + "\n" + "【旧成员项目Id】：" + modifyDataValid.getProductId() + "\n" + "【旧成员数据Keyno】：" + modifyDataValid.getCompKeyno() + "\n" + "【旧成员数据Id】：" + modifyDataValid.getId() + "\n" + "【异常描述】：新成员企业库获取父级数据异常，疑似穿透链断开,请确认处理";
                            //ProductStringUtil.sendMsgToDingdingWithToken(content, "",ProductStringUtil.SIVS_INSTITUTION_TOKEN_GROUP);
                            //if(!getUniqueIdFromCahce(content)) { DingDingUtil.sendDmp2(NotificationEntity.builder().sendType(Arrays.asList("2")).executorUser(Arrays.asList("xulc")).sendUser("xulc").sendTitle("【品牌项目成员企业库异常】").sendMessage(content).dingdingToken("https://oapi.dingtalk.com/robot/send?access_token=fc8b35a95b24d1f1687962a23ede5f69c6a759cac8bf07b0e676ff40f7245239").alarmType(4).alarmLevel(2).textType(0).isAt(0).build());}
                            CtCommonUtils.sendDingDing(content);
                            currentDataValid.setDataStatus(3);
                            currentDataValid.setRemark("获取上层企业失败");
                        }
                    }
                }
                if (olold != null && newew != null) {
                    Integer ruleOld = Arrays.stream(olold.getRule().split(",")).map(vv->PRODUCT_SORT_MAP.getOrDefault(vv,100)).min(Comparator.naturalOrder()).get();
                    Integer ruleNew = Arrays.stream(newew.getRule().split(",")).map(vv->PRODUCT_SORT_MAP.getOrDefault(vv,100)).min(Comparator.naturalOrder()).get();
                    Integer ruleOldSelf = Arrays.stream(modifyDataValid.getRule().split(",")).map(vv->PRODUCT_SORT_MAP.getOrDefault(vv,100)).min(Comparator.naturalOrder()).get();
                    Integer ruleNewSelf = Arrays.stream(currentDataValid.getRule().split(",")).map(vv->PRODUCT_SORT_MAP.getOrDefault(vv,100)).min(Comparator.naturalOrder()).get();
                    boolean retainNew = ruleOld > ruleNew || (ruleOld == ruleNew && ruleOldSelf >ruleNewSelf);
                    if (retainNew) {
                   // if (ruleOld > ruleNew) {
                        //
                        //要把old无效
                        if (currentDataValid.getType() == 24 || currentDataValid.getType() == 28) {
                            //把old 请过来
                            CompAffiliatedProducts copy = new CompAffiliatedProducts();
                            BeanUtils.copyProperties(currentDataValid, copy);
                            copy.setParentCompKeyno(currentDataValid.getParentCompKeyno());
                            copy.setId(MD5Util.encode(copy.getCompKeyno() + copy.getCompName() + copy.getParentCompKeyno() + copy.getProductId()));
                            if (modifyDataValid.getProductId().equals(currentDataValid.getProductId())) {
                                modifyDataValid.setDataStatus(6);
                                modifyDataValid.setRemark("自身穿透链被抢夺被无效");
                            } else {
                                modifyDataValid.setDataStatus(3);
                                modifyDataValid.setRemark("优先级较低被无效1");
                            }
                            result.add(copy);
                        } else {
                            modifyDataValid.setDataStatus(3);
                            modifyDataValid.setRemark("优先级较低被无效2");
                        }
                        result.add(modifyDataValid);
                    }else {
                        if (currentDataValid.getType() == 24 || currentDataValid.getType() == 28) {
                            currentDataValid.setDataStatus(3);
                            currentDataValid.setRemark("优先级较低被无效3");
                        }else if (currentDataValid.getType() == 23 || currentDataValid.getType() == 27) {
                            currentDataValid.setDataStatus(4);
                            currentDataValid.setRemark("暂时没有这种类型数据");
                        } else {
                            //这边应该是控制企业  肯定parentkeyno 而且没下级企业
                            currentDataValid.setDataStatus(6);
                            currentDataValid.setRemark("优先级较低被无效4");
                        }
                    }
                }
            } else {
                //这个分支应该在没有有效的new 忽略处理
            }
            result.addAll(newCompList);

        });
        return result;

    }



}
