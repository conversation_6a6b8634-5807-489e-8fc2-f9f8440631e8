package com.ld.clean.util;

import com.ld.clean.context.ApplicationCoreContextManager;
import com.ld.clean.model.ecidata.CommonCodetable;
import com.ld.clean.repository.ecidata.CommonCodetableRepository;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import tk.mybatis.mapper.entity.Condition;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public class DomainUtils {

    private static final CommonCodetableRepository commonCodetableRepository = ApplicationCoreContextManager.getInstance(CommonCodetableRepository.class);

    // 内存缓存，产品领域，总共42条数据
    public static Map<String, Integer> PRODUCT_DOMAIN_MAP = new HashMap<>();
    public static Map<Integer, String> PRODUCT_DOMAIN_CODE_MAP = new HashMap<>();

    static {
        initDomain();
    }

    public static void initDomain() {
        Condition condition = new Condition(CommonCodetable.class);
        condition.createCriteria().andEqualTo("category", "ProductDomain").andEqualTo("isvalid", 1);
        List<CommonCodetable> domainList = commonCodetableRepository.selectByCondition(condition);
        if (CollectionUtils.isNotEmpty(domainList)) {
            for (CommonCodetable commonCodetable : domainList) {
                PRODUCT_DOMAIN_MAP.put(commonCodetable.getDesc(), Integer.valueOf(commonCodetable.getCode()));
                PRODUCT_DOMAIN_CODE_MAP.put(Integer.valueOf(commonCodetable.getCode()), commonCodetable.getDesc());
            }
        }
    }

    public static String getDomainByCode(Integer code) {
        if (null != code) {
            String domain = PRODUCT_DOMAIN_CODE_MAP.get(code);
            if (StringUtils.isBlank(domain)) {
                Condition condition = new Condition(CommonCodetable.class);
                condition.createCriteria().andEqualTo("category", "ProductDomain")
                        .andEqualTo("code", code).andEqualTo("isvalid", 1);
                List<CommonCodetable> domainList = commonCodetableRepository.selectByCondition(condition);
                if (CollectionUtils.isNotEmpty(domainList)) {
                    domain = domainList.get(0).getDesc();
                    PRODUCT_DOMAIN_CODE_MAP.put(code, domain);
                }
            }
            return domain;
        }
        return "";
    }

    public static Integer getCodeByDomain(String domain) {
        if (StringUtils.isNotBlank(domain)) {
            Integer code = PRODUCT_DOMAIN_MAP.get(domain);
            if (null == code) {
                Condition condition = new Condition(CommonCodetable.class);
                condition.createCriteria().andEqualTo("category", "ProductDomain")
                        .andEqualTo("desc", domain).andEqualTo("isvalid", 1);
                List<CommonCodetable> domainList = commonCodetableRepository.selectByCondition(condition);
                if (CollectionUtils.isNotEmpty(domainList)) {
                    code = Integer.valueOf(domainList.get(0).getCode());
                    PRODUCT_DOMAIN_MAP.put(domain, code);
                    return code;
                }
            }
            return code;
        }
        // 默认其他
        return 16;
    }


}
