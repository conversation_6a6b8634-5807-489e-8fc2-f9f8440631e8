package com.ld.clean.test.mainbrand;

import com.ld.clean.context.ApplicationCoreContextManager;
import com.ld.clean.model.CompBrandFinancing;
import com.ld.clean.model.manageenterprisedb.CompProductFinancing;
import com.ld.clean.repository.CompBrandFinancingRepository;
import com.ld.clean.repository.manageenterprisedb.CompProductFinancingRepository;
import com.ld.clean.test.mainbrand.dao.ScSeCompBrandFinancing20240513;
import com.ld.clean.test.mainbrand.dao.ScSeCompBrandFinancing20240513Repository;
import com.ld.clean.test.mainbrand.dao.finance.LpfMeASharesAndNewThirdBoardFinance240919;
import com.ld.clean.test.mainbrand.dao.finance.LpfMeASharesAndNewThirdBoardFinance240919Repository;
import org.apache.commons.collections.CollectionUtils;
import org.apache.flink.api.common.functions.RichFlatMapFunction;
import org.apache.flink.util.Collector;

import java.util.List;

/**
 *
 *
 * <AUTHOR>
 */
public class TestCleanFinanceFlatMap extends RichFlatMapFunction<List<String>, LpfMeASharesAndNewThirdBoardFinance240919> {

    private static CompProductFinancingRepository compProductFinancingRepository = ApplicationCoreContextManager.getInstance(CompProductFinancingRepository.class);
    private static ScSeCompBrandFinancing20240513Repository compBrandFinancingRepository = ApplicationCoreContextManager.getInstance(ScSeCompBrandFinancing20240513Repository.class);

    private static LpfMeASharesAndNewThirdBoardFinance240919Repository aSharesAndNewThirdBoardFinance240919Repository = ApplicationCoreContextManager.getInstance(LpfMeASharesAndNewThirdBoardFinance240919Repository.class);

    @Override
    public void flatMap(List<String> ids, Collector<LpfMeASharesAndNewThirdBoardFinance240919> collector) throws Exception {
        if (CollectionUtils.isNotEmpty(ids)) {
            List<LpfMeASharesAndNewThirdBoardFinance240919> list = aSharesAndNewThirdBoardFinance240919Repository.selectByPrimaryKeyList(ids);
            for (LpfMeASharesAndNewThirdBoardFinance240919 financing : list) {
                collector.collect(financing);
            }
        }

        /*if (CollectionUtils.isNotEmpty(ids)) {
            List<ScSeCompBrandFinancing20240513> list = compBrandFinancingRepository.selectByPrimaryKeyList(ids);
            for (ScSeCompBrandFinancing20240513 financing : list) {
                collector.collect(financing);
            }
        }*/
    }
}