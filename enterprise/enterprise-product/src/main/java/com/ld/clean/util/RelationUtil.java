package com.ld.clean.util;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.ld.clean.constants.ExchangeAllEnums;
import com.ld.clean.context.ApplicationCoreContextManager;
import com.ld.clean.job.productv2.baseinfo.entity.RelationInfo;
import com.ld.clean.model.manageglobalsysdb.SysSecCode;
import com.ld.clean.model.searchsyncfinancial.FinSecLabelListSync;
import com.ld.clean.repository.manageglobalsysdb.SysSecCodeRepository;
import com.ld.clean.repository.searchsyncfinancial.FinSecLabelListSyncRepository;
import com.ld.clean.utils.DateUtil;
import joptsimple.internal.Strings;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import tk.mybatis.mapper.entity.Condition;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @description：品牌关联企业
 * @author： liupf
 * @create： 2024/9/25 15:40
 */
public class RelationUtil {

    private static final SysSecCodeRepository sysSecCodeRepository = ApplicationCoreContextManager.getInstance(SysSecCodeRepository.class);
    private static final FinSecLabelListSyncRepository finSecLabelListSyncRepository = ApplicationCoreContextManager.getInstance(FinSecLabelListSyncRepository.class);

    public static void main(String[] args) {
      //  System.out.println(handleRelationInfos("hff7ba41ac7df447b6cb52d2adf628e4"));
    }


    public static String handleRelationInfosTest(String compKeyNo, List<SysSecCode> sysSecCodeList,List<FinSecLabelListSync> list) {
        List<RelationInfo> relationInfos = new ArrayList<>();
        if (compKeyNo.startsWith("l")) {
            RelationInfo usInfo = handleUsAndHkShares(compKeyNo);
            if (ObjectUtil.isNotEmpty(usInfo)) {
                relationInfos.add(usInfo);
            }
        } else {
           /* Condition secCodeCondition = new Condition(SysSecCode.class);
            secCodeCondition.createCriteria().andEqualTo("compKeyno", compKeyNo)
                    .andEqualTo("dataStatus", 1)
                    .andIn("listStatus", Arrays.asList(1, 3))
                    .andNotIn("secType", Arrays.asList(5, 6, 9, 10));
            List<SysSecCode> sysSecCodeList = sysSecCodeRepository.selectByCondition(secCodeCondition);*/
            if (CollectionUtils.isEmpty(sysSecCodeList)) {
                return Strings.EMPTY;
            }
            // List<SysSecCode> sysSecCodeList
            Set<Integer> setEx = sysSecCodeList.stream().map(SysSecCode::getExchange).collect(Collectors.toSet());
            //三板数据查询金融标签业务表
            Set<Integer> setSec = sysSecCodeList.stream().map(SysSecCode::getSecType).collect(Collectors.toSet());

            if ((sysSecCodeList.size() > 1 && compKeyNo.startsWith("h") && setEx.size() != sysSecCodeList.size()) || (CollectionUtils.isNotEmpty(setSec) && setSec.contains(3))) {
                //港股查询多个/新三板数据
                RelationInfo hkInfo = handleUsAndHkSharesTest(compKeyNo,list);
                if(null != hkInfo){
                    relationInfos.add(hkInfo);
                }
            } else {
                return handleRelationInfo(sysSecCodeList);
            }
        }
        return JSONObject.toJSONString(relationInfos);
    }

    public static String handleRelationInfos(String compKeyNo) {
        List<RelationInfo> relationInfos = new ArrayList<>();
        if (compKeyNo.startsWith("l")) {
            RelationInfo usInfo = handleUsAndHkShares(compKeyNo);
            if (ObjectUtil.isNotEmpty(usInfo)) {
                relationInfos.add(usInfo);
            }
        } else {
            Condition secCodeCondition = new Condition(SysSecCode.class);
            secCodeCondition.createCriteria().andEqualTo("compKeyno", compKeyNo)
                    .andEqualTo("dataStatus", 1)
                    .andIn("listStatus", Arrays.asList(1, 3))
                    .andNotIn("secType", Arrays.asList(5, 6, 9, 10));
            List<SysSecCode> sysSecCodeList = sysSecCodeRepository.selectByCondition(secCodeCondition);
            if (CollectionUtils.isEmpty(sysSecCodeList)) {
                return Strings.EMPTY;
            }
            // List<SysSecCode> sysSecCodeList
            Set<Integer> setEx = sysSecCodeList.stream().map(SysSecCode::getExchange).collect(Collectors.toSet());
            //三板数据查询金融标签业务表
            Set<Integer> setSec = sysSecCodeList.stream().map(SysSecCode::getSecType).collect(Collectors.toSet());

            if ((sysSecCodeList.size() > 1 && compKeyNo.startsWith("h") && setEx.size() != sysSecCodeList.size()) || (CollectionUtils.isNotEmpty(setSec) && setSec.contains(3))) {
                //港股查询多个/新三板数据
                RelationInfo hkInfo = handleUsAndHkShares(compKeyNo);
                if(null != hkInfo){
                    relationInfos.add(hkInfo);
                }
            } else {
                return handleRelationInfo(sysSecCodeList);
            }
        }
        return JSONObject.toJSONString(relationInfos);
    }

    public static RelationInfo handleUsAndHkSharesTest(String CompKeyNo, List<FinSecLabelListSync> list) {
        //美股直接查询
      /*  Condition condition = new Condition(FinSecLabelListSync.class);
        condition.createCriteria()
                .andEqualTo("dataStatus", 1)
                .andEqualTo("compKeyno", CompKeyNo)
                .andIn("secType",Arrays.asList(3,7,8));
        List<FinSecLabelListSync> list = finSecLabelListSyncRepository.selectByCondition(condition);*/
        RelationInfo relationInfo = new RelationInfo();
        if (CollectionUtils.isNotEmpty(list)) {
            String today = DateUtil.getStringFormatTimeOfDate(new Date(), DateUtil.YMD);
            FinSecLabelListSync secLabelListSync = list.get(0);
            if (StringUtils.isNotBlank(secLabelListSync.getExchange())) {
                relationInfo.setStockExchange(Integer.parseInt(secLabelListSync.getExchange()));
            }
            relationInfo.setStockCode(secLabelListSync.getSymbol());
            if (StringUtils.isNotBlank(secLabelListSync.getListSection())) {
                relationInfo.setListSection(Integer.parseInt(secLabelListSync.getListSection()));
            }
            if (null != secLabelListSync.getSecType()) {
                relationInfo.setSecType(secLabelListSync.getSecType());
            }
            String listDate = "";
            String deListDate = "";
            if (ObjectUtil.isNotEmpty(secLabelListSync.getListDate())) {
                listDate = DateUtil.getStringFormatTimeOfDate(secLabelListSync.getListDate(), DateUtil.YMD);
                if (listDate.compareTo(today) > 0) {
                    return null;
                }
                relationInfo.setListDate(listDate);
            }
            if (null != secLabelListSync.getDelistDate()) {
                deListDate = DateUtil.getStringFormatTimeOfDate(secLabelListSync.getDelistDate(), DateUtil.YMD);
                if (deListDate.compareTo(today) > 0) {
                    return null;
                }
                relationInfo.setDelistDate(deListDate);
            }
            relationInfo.setIsDelist(0);
            if (StringUtils.isNotBlank(listDate) && StringUtils.isNotBlank(deListDate)) {
                if (deListDate.compareTo(listDate) > 0) {
                    relationInfo.setIsDelist(1);
                }
            }
            if (StringUtils.isBlank(deListDate)) {
                relationInfo.setRelatedSec(secLabelListSync.getRelatedSec());
            }
            //金融标签信息
            StringBuilder content = new StringBuilder();
            if(relationInfo.getSecType() == 3){
                content.append("新三板").append("（").append(secLabelListSync.getSymbol()).append("）");
                if (relationInfo.getIsDelist() == 1) {
                    content.append("摘牌");
                }
            }else {
                String exchangeType = (relationInfo.getSecType() == 2 || relationInfo.getSecType() == 7) ? "美股" : "港交所";
                content.append(exchangeType)
                        .append("（").append(secLabelListSync.getSymbol()).append("）");
                if (relationInfo.getIsDelist() == 1) {
                    content.append("退市");
                }
            }
            relationInfo.setFtags(content.toString());
            return relationInfo;
        }
        return null;
    }

    //处理港股（多地）、美股上市数据  企业金融标签业务表
    public static RelationInfo handleUsAndHkShares(String CompKeyNo) {
        //美股直接查询
        Condition condition = new Condition(FinSecLabelListSync.class);
        condition.createCriteria()
                .andEqualTo("dataStatus", 1)
                .andEqualTo("compKeyno", CompKeyNo)
                .andIn("secType",Arrays.asList(3,7,8))
                .andIn("listStatus",Arrays.asList(1,3));
        List<FinSecLabelListSync> list = finSecLabelListSyncRepository.selectByCondition(condition);
        RelationInfo relationInfo = new RelationInfo();
        if (CollectionUtils.isNotEmpty(list)) {
            String today = DateUtil.getStringFormatTimeOfDate(new Date(), DateUtil.YMD);
            FinSecLabelListSync secLabelListSync = list.get(0);
            if (StringUtils.isNotBlank(secLabelListSync.getExchange())) {
                relationInfo.setStockExchange(Integer.parseInt(secLabelListSync.getExchange()));
            }
            relationInfo.setStockCode(secLabelListSync.getSymbol());
            if (StringUtils.isNotBlank(secLabelListSync.getListSection())) {
                relationInfo.setListSection(Integer.parseInt(secLabelListSync.getListSection()));
            }
            if (null != secLabelListSync.getSecType()) {
                relationInfo.setSecType(secLabelListSync.getSecType());
            }
            String listDate = "";
            String deListDate = "";
            if (ObjectUtil.isNotEmpty(secLabelListSync.getListDate())) {
                listDate = DateUtil.getStringFormatTimeOfDate(secLabelListSync.getListDate(), DateUtil.YMD);
                if (listDate.compareTo(today) > 0) {
                    return null;
                }
                relationInfo.setListDate(listDate);
            }
            if (null != secLabelListSync.getDelistDate()) {
                deListDate = DateUtil.getStringFormatTimeOfDate(secLabelListSync.getDelistDate(), DateUtil.YMD);
                if (deListDate.compareTo(today) > 0) {
                    return null;
                }
                relationInfo.setDelistDate(deListDate);
            }
            relationInfo.setIsDelist(0);
            if (StringUtils.isNotBlank(listDate) && StringUtils.isNotBlank(deListDate)) {
                if (deListDate.compareTo(listDate) > 0) {
                    relationInfo.setIsDelist(1);
                }
            }
            if (StringUtils.isBlank(deListDate)) {
                relationInfo.setRelatedSec(secLabelListSync.getRelatedSec());
            }
            //金融标签信息
            StringBuilder content = new StringBuilder();
            if(relationInfo.getSecType() == 3){
                content.append("新三板").append("（").append(secLabelListSync.getSymbol()).append("）");
                if (relationInfo.getIsDelist() == 1) {
                    content.append("摘牌");
                }
            }else {
                String exchangeType = (relationInfo.getSecType() == 2 || relationInfo.getSecType() == 7) ? "美股" : "港交所";
                content.append(exchangeType)
                        .append("（").append(secLabelListSync.getSymbol()).append("）");
                if (relationInfo.getIsDelist() == 1) {
                    content.append("退市");
                }
            }
            relationInfo.setFtags(content.toString());
            return relationInfo;
        }
        return null;
    }

    public static String handleRelationInfo(List<SysSecCode> sysSecCodeList) {
        if (CollectionUtils.isEmpty(sysSecCodeList)) {
            return Strings.EMPTY;
        }
        List<RelationInfo> relationInfos = new ArrayList<>();
        String today = DateUtil.getStringFormatTimeOfDate(new Date(), DateUtil.YMD);
        for (SysSecCode secCode : sysSecCodeList) {
            if (secCode.getSecType() == 3) {
                if (secCode.getListSection() != 901 && secCode.getListSection() != 902 && secCode.getListSection() != 903) {
                    continue;
                }
                if (secCode.getSecStype() != 301) {
                    continue;
                }
            }
            RelationInfo relationInfo = new RelationInfo();
            relationInfo.setStockExchange(secCode.getExchange());
            relationInfo.setStockCode(secCode.getSymbol());
            relationInfo.setListSection(secCode.getListSection());
            if (null != secCode.getSecType()) {
                relationInfo.setSecType(secCode.getSecType());
            }
            String listDate = secCode.getListDate();
            String deListDate = secCode.getDelistDate();
            if (listDate.compareTo(today) > 0) {
                continue;
            }
            relationInfo.setListDate(listDate);
            relationInfo.setDelistDate(deListDate);
            relationInfo.setIsDelist(0);
            if (StringUtils.isNotBlank(listDate) && StringUtils.isNotBlank(deListDate)) {
                if (deListDate.compareTo(today) > 0) {
                    continue;
                }
                if (deListDate.compareTo(listDate) > 0) {
                    relationInfo.setIsDelist(1);
                }
            }
            if (StringUtils.isBlank(secCode.getEndDate())) {
                relationInfo.setRelatedSec(secCode.getRelatedSec());
            }
            relationInfo.setFtags(handleTags(secCode, relationInfo.getIsDelist()));
            relationInfos.add(relationInfo);
        }
        //排序
        if (CollectionUtils.isNotEmpty(relationInfos) && relationInfos.size() > 1) {
            relationInfos.sort(new Comparator<RelationInfo>() {
                @Override
                public int compare(RelationInfo r1, RelationInfo r2) {
                    int priority1 = getPriority(r1.getSecType());
                    int priority2 = getPriority(r2.getSecType());
                    return Integer.compare(priority1, priority2);
                }
            });
        }
        return JSONObject.toJSONString(relationInfos);
    }

    public static String handleTags(SysSecCode secCode, Integer isDelist) {
        String first = "";
        if (secCode.getSecType() == 1) {
            switch (secCode.getListSection()) {
                case 1:
                    switch (secCode.getExchange()) {
                        case 101:
                            first = "沪主板";
                            break;
                        case 102:
                            first = "深主板";
                            break;
                        case 107:
                            first = "北交所";
                            break;
                        default:
                            first = "";
                    }
                    break;
                case 4:
                    first = "科创板";
                    break;
                case 3:
                    first = "创业板";
                    break;
                default:
                    first = "";
            }
        } else if (secCode.getSecType() == 4 || secCode.getSecType() == 8) {
            first = "港交所";
        } else if (secCode.getSecType() == 2 || secCode.getSecType() == 7) {
            first = "美股";
        } else if (secCode.getSecType() == 3) {
            first = "新三板";
        } else {
            // 其它交易所情况处理
            ExchangeAllEnums enums = ExchangeAllEnums.getByCode(secCode.getExchange());
            if (ObjectUtil.isNotEmpty(enums)) {
                first = enums.getDesc();
            }
        }
        if (StringUtils.isBlank(first)) {
            return "";
        }
        StringBuilder content = new StringBuilder(first);
        content.append("（").append(secCode.getSymbol()).append("）");
        if (isDelist == 1) {
            content.append(secCode.getSecType() == 3 ? "摘牌" : "退市");
        }
        return content.toString();
    }

    public static int getPriority(Integer secType) {
        // 定义优先级映射
        switch (secType) {
            case 1:
                return 1;
            case 8:
                return 2;
            case 4:
                return 3;
            case 7:
                return 4;
            case 2:
                return 5;
            default:
                return Integer.MAX_VALUE; // 其余类型排在最后
        }
    }
}
