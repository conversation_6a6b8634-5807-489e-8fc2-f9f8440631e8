package com.ld.clean.test.financev2sync.async;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.ld.clean.beans.ParticipantEntity;
import com.ld.clean.constants.ListSectionEnum;
import com.ld.clean.constants.RoundStageEnum;
import com.ld.clean.model.tidbsearchsyncenterprise.EpProductFinancingV2Sync;
import com.ld.clean.parent.AsyncCleanParentFunction;
import com.ld.clean.util.FinacingAmountUtil;
import com.ld.clean.utils.DateUtil;
import joptsimple.internal.Strings;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
public class CleanEpFinanceTestDescInfoFunction extends AsyncCleanParentFunction<List<EpProductFinancingV2Sync>, List<EpProductFinancingV2Sync>> {

    public CleanEpFinanceTestDescInfoFunction(int corePoolSize) {
        super(corePoolSize);
    }

    @Override
    protected List<EpProductFinancingV2Sync> invoke(List<EpProductFinancingV2Sync> eps) throws Exception {
        for (EpProductFinancingV2Sync ep : eps) {
            if (null != ep && ep.getDataStatus() == 1) {
//            ep.setNewsBrief(getNewsBriefByRound(ep));
                ep.setEventDecCt(generateEventDesc(ep));
                ep.setInformationContent(generateInformationContent(ep));
            }
        }

        return eps;
    }

    public String generateInformationContent(EpProductFinancingV2Sync ep) {
        String nameKeynoCollection = ep.getParticipantDetails();
        List<ParticipantEntity> participantEntityList = JSONObject.parseArray(nameKeynoCollection, ParticipantEntity.class);
        //投资方名称获取
        participantEntityList.stream()
                .filter(entity -> entity.getCategory() == 2)
                .forEach(entity -> {
                    if (entity.getRelationInfo() != null && !entity.getRelationInfo().isEmpty()) {
                        ParticipantEntity.RelationInfo info = entity.getRelationInfo().get(0);
                        if (StringUtils.isNotBlank(info.getName())) {
                            entity.setName(info.getName());
                        }
                    }
                });
        List<String> leadInvestorNames = participantEntityList.stream().filter(x -> x.getParticipantType() == 1
                && x.getType() == 1).map(ParticipantEntity::getName).distinct().collect(Collectors.toList());
        List<String> followInvestorNames = participantEntityList.stream().filter(x -> x.getParticipantType() == 1
                && x.getType() == 2).map(ParticipantEntity::getName).distinct().collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(leadInvestorNames) && CollectionUtils.isNotEmpty(followInvestorNames)) {
            followInvestorNames = followInvestorNames.stream()
                    .filter(name -> !leadInvestorNames.contains(name))
                    .collect(Collectors.toList());
        }

        List<String> chuRangFangNames = participantEntityList.stream().filter(x -> x.getParticipantType() == 2)
                .map(ParticipantEntity::getName).distinct().collect(Collectors.toList());
        List<String> shouRangFangNames = participantEntityList.stream().filter(x -> x.getParticipantType() == 1)
                .map(ParticipantEntity::getName).distinct().collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(chuRangFangNames) && CollectionUtils.isNotEmpty(shouRangFangNames)) {
            shouRangFangNames = shouRangFangNames.stream()
                    .filter(name -> !chuRangFangNames.contains(name))
                    .collect(Collectors.toList());
        }

        Long financeDate = ep.getFinanceDate();
        String formatDate = DateUtil.getStringFormatTimeOfTimestamp(financeDate * 1000L, DateUtil.YMD_DASH_CHINESE);

        StringBuffer content = new StringBuffer(formatDate).append("，");
        if (StringUtils.isNotBlank(ep.getSlogan())) {
            content.append(ep.getSlogan());
        }
        String mainProduct = String.format("<a href=\"/product/%s.html\" target=\"_blank\">%s</a>",
                ep.getProductId(), ep.getProductName());
        content.append(mainProduct);
        if (StringUtils.isNotBlank(ep.getCompanyName())) {
            if (StringUtils.isNotBlank(ep.getCompanyKeyno())) {
                String mainComp = String.format("<a href=\"/firm/%s.html\" target=\"_blank\">%s</a>",
                        ep.getCompanyKeyno(), ep.getCompanyName());
                content.append("（" + mainComp + "）");
            } else {
                content.append("（" + ep.getCompanyName() + "）");
            }
        }

        content.append("完成");

        Integer roundSortStage = ep.getRoundSortStage();
        String round = ep.getRound();
        if (roundSortStage == RoundStageEnum.RONGZI.getCode() && !StringUtils.equalsIgnoreCase(round, "股权转让")) {
            if (StringUtils.isNotBlank(ep.getAmount()) && !FinacingAmountUtil.FINACING_AMOUNT_ENUMS.contains(ep.getAmount())) {
                content.append(ep.getAmount());
            }
            content.append(round);
            content.append(generateLeadAndFollowInvestorDesc(leadInvestorNames, followInvestorNames));
        }
        if (roundSortStage == RoundStageEnum.OTHER.getCode()) {
            if (StringUtils.isNotBlank(ep.getAmount()) && !FinacingAmountUtil.FINACING_AMOUNT_ENUMS.contains(ep.getAmount())) {
                content.append(ep.getAmount());
            }
            content.append(round);
            content.append(generateInvestorDesc(leadInvestorNames, followInvestorNames));
        }
        //round =定向增发时，不拼接投资方
        if (roundSortStage == RoundStageEnum.ADDISSUE.getCode()) {
            if (StringUtils.isNotBlank(ep.getAmount()) && !FinacingAmountUtil.FINACING_AMOUNT_ENUMS.contains(ep.getAmount())) {
                content.append(ep.getAmount());
            }
            content.append(round).append("。");
            //content.append(generateInvestorDesc(leadInvestorNames, followInvestorNames));
        }

        if (roundSortStage == RoundStageEnum.COMMON.getCode()) {
            if (StringUtils.isNotBlank(ep.getAmount()) && !FinacingAmountUtil.FINACING_AMOUNT_ENUMS.contains(ep.getAmount())) {
                content.append(ep.getAmount());
            }
            content.append(round).append("融资");
            content.append(generateLeadAndFollowInvestorDesc(leadInvestorNames, followInvestorNames));
        }

        if (roundSortStage == RoundStageEnum.MERGE.getCode() || StringUtils.equalsIgnoreCase(round, "股权转让")) {
            if (StringUtils.isNotBlank(ep.getAmount()) && !FinacingAmountUtil.FINACING_AMOUNT_ENUMS.contains(ep.getAmount())) {
                content.append(ep.getAmount());
            }
            /*if(StringUtils.isNotBlank(ep.getRate())){
                content.append(ep.getRate());
            }*/
            content.append(round);
            /*if (StringUtils.isNotBlank(ep.getAmount()) && !FinacingAmountUtil.FINACING_AMOUNT_ENUMS.contains(ep.getAmount())) {
                content.append("，交易金额为").append(ep.getAmount());
            }*/

            content.append(generateMergeDesc(chuRangFangNames, shouRangFangNames));
        }
        if (roundSortStage == RoundStageEnum.IPO.getCode() || roundSortStage == RoundStageEnum.IPOOUT.getCode()) {
            content.delete(content.length() - 2, content.length());
            String stockExchange = ep.getStockExchange();
            String names = participantEntityList.stream()
                    .map(ParticipantEntity::getName)
                    .distinct()
                    .collect(Collectors.joining("、"));
            if (StringUtils.isBlank(stockExchange)) {
                content.append("完成").append(round).append("，");
            } else {
                content.append("在").append(stockExchange);
                if (null != ep.getListSection()) {
                    String section = ListSectionEnum.getByCode(ep.getListSection()).getDesc();
                    content.append(section);
                }
                content.append("完成").append(round).append("，");
            }

            if (StringUtils.isNotBlank(names)) {
                if (ep.getSecType() == 1) {
                    content.append("战略配售方为").append(names).append("。");
                } else {
                    content.append("基石投资者为").append(names).append("。");

                }
            }
            //.append("完成").append(round)
            /*if (StringUtils.isNotBlank(names)) {


                String role = (ep.getSecType() == 1) ? "战略配售方" : ((ep.getListSection() != null) ? "基石投资者" : "战略配售方");
                content.append(role).append("为").append(names).append("。");
            }*/
            if (content.charAt(content.length() - 1) == '，') {
                content.deleteCharAt(content.length() - 1).append("。");
            }
            return content.toString();
        }

        String purpose = ep.getPurpose();
        if (StringUtils.isNotBlank(purpose)) {
            content.append(purpose);
            if (!purpose.endsWith("。")) {
                content.append("。");
            }
        }

        return content.toString().replaceAll("元人民币", "元");
    }

    private String generateMergeDesc(List<String> chuRangFangNames, List<String> shouRangFangNames) {
        if (CollectionUtils.isEmpty(chuRangFangNames) && CollectionUtils.isEmpty(shouRangFangNames)) {
            return "，出让方未披露，投资方未披露。";
        } else {
            String churang = StringUtils.join(chuRangFangNames, "、");
            String shourang = StringUtils.join(shouRangFangNames, "、");
            if (StringUtils.isBlank(churang) || StringUtils.equalsIgnoreCase(churang, "未披露")) {
                if (StringUtils.isBlank(shourang) || StringUtils.equalsIgnoreCase(shourang, "未披露")) {
                    return "，出让方未披露，投资方未披露。";
                } else {
                    return "，出让方未披露，投资方为" + shourang + "。";
                }
            } else {
                if (StringUtils.isBlank(shourang) || StringUtils.equalsIgnoreCase(shourang, "未披露")) {
                    return "，出让方为" + churang + "，投资方未披露。";
                } else {
                    return "，出让方为" + churang + "，投资方为" + shourang + "。";
                }
            }
        }
        /*if (CollectionUtils.isEmpty(chuRangFangNames) && CollectionUtils.isEmpty(shouRangFangNames)) {
            return "。";
        } else {
            String churang = StringUtils.join(chuRangFangNames, "、");
            String shourang = StringUtils.join(shouRangFangNames, "、");
            if (StringUtils.isBlank(churang) || StringUtils.equalsIgnoreCase(churang, "未披露")) {
                if (StringUtils.isBlank(shourang) || StringUtils.equalsIgnoreCase(shourang, "未披露")) {
                    return "。";
                } else {
                    return "，投资方为" + shourang + "。";
                }
            } else {
                if (StringUtils.isBlank(shourang) || StringUtils.equalsIgnoreCase(shourang, "未披露")) {
                    return "，出让方为" + churang + "。";
                } else {
                    return "，出让方为" + churang + "，投资方为" + shourang + "。";
                }
            }
        }*/
    }

    // 不区分领投跟投
    private String generateInvestorDesc(List<String> leadInvestorNames, List<String> followInvestorNames) {
        if (CollectionUtils.isEmpty(leadInvestorNames) && CollectionUtils.isEmpty(followInvestorNames)) {
            return "，投资方未披露。";
        } else {
            List<String> investor = new ArrayList<>();
            investor.addAll(leadInvestorNames);
            investor.addAll(followInvestorNames);
            List<String> collect = investor.stream().filter(StringUtils::isNotBlank).collect(Collectors.toList());
            String joined = StringUtils.join(collect, "、");
            //String joined = generateLinks(collect,infoMap);
            if (StringUtils.isBlank(joined) || StringUtils.equalsIgnoreCase(joined, "未披露")) {
                return "，投资方未披露。";
            } else {
                return "，投资方为" + joined + "。";
            }
        }
    }

    // 区分领投跟投
    private String generateLeadAndFollowInvestorDesc(List<String> leadInvestorNames, List<String> followInvestorNames) {
        if (CollectionUtils.isEmpty(leadInvestorNames) && CollectionUtils.isEmpty(followInvestorNames)) {
            return "，投资方未披露。";
        } else {
            String leadJoin = StringUtils.join(leadInvestorNames, "、");
            String followJoin = StringUtils.join(followInvestorNames, "、");
            //String leadJoin = generateLinks(leadInvestorNames,infoMap);
            //String followJoin = generateLinks(followInvestorNames,infoMap);
            if (StringUtils.isBlank(leadJoin) || StringUtils.equalsIgnoreCase(leadJoin, "未披露")) {
                if (StringUtils.isBlank(followJoin) || StringUtils.equalsIgnoreCase(followJoin, "未披露")) {
                    return "，投资方未披露。";
                } else {
                    return "，投资方为" + followJoin + "。";
                }
            } else {
                if (StringUtils.isBlank(followJoin) || StringUtils.equalsIgnoreCase(followJoin, "未披露")) {
                    return "，本轮融资由" + leadJoin + "领投。";
                } else {
                    return "，本轮融资由" + leadJoin + "领投，" + followJoin + "跟投。";
                }
            }
        }
    }

    public String generateEventDesc(EpProductFinancingV2Sync ep) {
        String desc = "";
        Integer stageCode = ep.getRoundSortStage();
        if (ObjectUtil.isEmpty(stageCode)) {
            return "";
        }
        if (stageCode != 0) {
//            Long financeDate = ep.getFinanceDate();
            //           String formatDate = DateUtil.getStringFormatTimeOfTimestamp(financeDate * 1000L, DateUtil.YMD_DASH_CHINESE);
//            String nameKeynoCollection = ep.getParticipantDetails();
//            List<String> investNames = JSONObject.parseArray(nameKeynoCollection, ParticipantEntity.class).stream().map(ParticipantEntity::getName).collect(Collectors.toList());
//            String investNameBf = "未披露";
//            if (CollectionUtils.isNotEmpty(investNames)) {
//                investNameBf = StringUtils.join(investNames, "，");
//            }

            if (stageCode == RoundStageEnum.COMMON.getCode() || stageCode == RoundStageEnum.OTHER.getCode()) {
                String changeRound = ep.getRound();
                if (StringUtils.isNotBlank(changeRound)) {
                    if (changeRound.contains("融资")) {
                        changeRound = changeRound.replace("融资", "");
                    }
                }
                if (StringUtils.equalsIgnoreCase(FinacingAmountUtil.getFinancingV2Amount(ep.getAmount()), "未披露") || StringUtils.equalsIgnoreCase(FinacingAmountUtil.getFinancingV2Amount(ep.getAmount()), "0元人民币")) {
                    desc = ep.getProductName() + "完成" + changeRound + "融资";
                } else {
                    desc = ep.getProductName() + "完成" + ep.getAmount() + changeRound + "融资";
                }
//                if (!StringUtils.equalsIgnoreCase(investNameBf, "未披露")) {
//                    desc += "；投资方：" + investNameBf;
//                }
            } else if (stageCode == RoundStageEnum.ADDISSUE.getCode() || stageCode == RoundStageEnum.RONGZI.getCode() || stageCode == RoundStageEnum.MERGE.getCode()) {
                if (StringUtils.equalsIgnoreCase(FinacingAmountUtil.getFinancingV2Amount(ep.getAmount()), "未披露") || StringUtils.equalsIgnoreCase(FinacingAmountUtil.getFinancingV2Amount(ep.getAmount()), "0元人民币")) {
                    desc = ep.getProductName() + "完成" + ep.getRound();
                } else {
                    desc = ep.getProductName() + "完成" + ep.getAmount() + ep.getRound();
                }
//                if (!StringUtils.equalsIgnoreCase(investNameBf, "未披露")) {
//                    desc += "；投资方：" + investNameBf;
//                }
            }/*else if(stageCode == RoundStageEnum.MERGE.getCode()){
                if (StringUtils.isBlank(ep.getRate())) {
                    desc = ep.getProductName() + "完成" + ep.getRound();
                } else {
                    desc = ep.getProductName() + "完成" + ep.getRate() + ep.getRound();
                }
            }*/ else if (stageCode == RoundStageEnum.IPO.getCode() || stageCode == RoundStageEnum.IPOOUT.getCode()) {
                if (StringUtils.isBlank(ep.getStockExchange())) {
                    desc = ep.getProductName() + "完成" + ep.getRound();
                } else {
                    desc = ep.getProductName() + "在" + ep.getStockExchange() + "完成" + ep.getRound();
                }
            }
        }
        return desc.replaceAll("元人民币", "元");
    }

    public static String getNewsBriefByRound(EpProductFinancingV2Sync ep) {
        String productName = ep.getProductName();
        Integer roundSortStage = ep.getRoundSortStage();
        String round = ep.getRound();
        if (roundSortStage == RoundStageEnum.RONGZI.getCode() || roundSortStage == RoundStageEnum.ADDISSUE.getCode()) {
            return productName + "完成" + round;
        }
        if (roundSortStage == RoundStageEnum.COMMON.getCode()) {
            return productName + "完成" + round + "融资";
        }

        if (roundSortStage == RoundStageEnum.MERGE.getCode()) {
            return productName + "被并购";
        }
        String stockCode = ep.getStockCode();
        String stockExchange = ep.getStockExchange();
        if (roundSortStage == RoundStageEnum.IPO.getCode()) {
            if (StringUtils.isNotBlank(stockCode) && stockCode.contains("NQ")) {
                return productName + "在新三板挂牌";
            } else {
                if (StringUtils.isNotBlank(stockExchange)) {
                    return productName + "在" + stockExchange + "挂牌";
                } else {
                    return productName + "IPO挂牌交易";
                }
            }
        }
        if (roundSortStage == RoundStageEnum.IPOOUT.getCode()) {
            if (StringUtils.isNotBlank(stockCode) && stockCode.contains("NQ")) {
                return productName + "从新三板退市";
            } else {
                if (StringUtils.isNotBlank(stockExchange)) {
                    return productName + "从" + stockExchange + "退市";
                } else {
                    return productName + "退市";
                }
            }
        }
        return Strings.EMPTY;
    }


}