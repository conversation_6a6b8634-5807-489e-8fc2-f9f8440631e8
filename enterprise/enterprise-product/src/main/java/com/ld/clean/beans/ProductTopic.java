package com.ld.clean.beans;

import lombok.Data;


@Data
public class ProductTopic {
    /**
     * 爬虫队列：数据源的唯一id，如烯牛数据则是XiNiuId；鲸准数据则是ProductId
     * 清洗队列：产品id
     */
    private String id;
    /**
     * 标记数据来源，人工-AI，烯牛-XN，鲸准-JZ ...
     */
    private String source;
    /**
     * 本次更新的产品信息所属块
     * BASE_INFO		-基本信息
     * FINANCING		-融资信息
     * MEMBER			-核心人员
     * NEWS			    -相关新闻
     * AMPLIFICATION	-引申出的次级产品信息(主要产品)
     * COMPETITOR		-竞品关系
     */
    private String type;

    /*
     * 代码里推送数据的位置标志，不做业务处理，仅做标识
     * */
    private String codeStage;
}
