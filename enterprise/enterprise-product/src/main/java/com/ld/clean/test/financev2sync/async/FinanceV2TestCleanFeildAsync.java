package com.ld.clean.test.financev2sync.async;


import com.ld.clean.model.tidbsearchsyncenterprise.EpProductFinancingV2Sync;
import com.ld.clean.parent.AsyncCleanParentFunction;
import com.ld.clean.test.financev2sync.so.JsonRootBean;
import com.ld.clean.util.FinacingAmountUtil;
import com.ld.clean.util.FinacingValuationUtil;
import com.ld.clean.utils.CtCurrencyUtils;
import com.ld.clean.utils.DateUtil;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;


/**
 * <AUTHOR>
 * @date 2021年9月9日 17点27分
 */
public class FinanceV2TestCleanFeildAsync extends AsyncCleanParentFunction<JsonRootBean, List<EpProductFinancingV2Sync>> {

   /* private static final CompBrandFinancingRepository compBrandFinancingRepository = ApplicationCoreContextManager.getInstance(CompBrandFinancingRepository.class);
    private static final CompVcpeInfoRepository compVcpeInfoRepository = ApplicationCoreContextManager.getInstance(CompVcpeInfoRepository.class);
    private static PlaceFinanceRepository placeFinanceRepository = ApplicationCoreContextManager.getInstance(PlaceFinanceRepository.class);
    private static EpProductFinancingV2SyncRepository epProductFinancingV2SyncRepository = ApplicationCoreContextManager.getInstance(EpProductFinancingV2SyncRepository.class);*/


    public FinanceV2TestCleanFeildAsync(int corePoolSize) {
        super(corePoolSize);
    }

    @Override
    protected List<EpProductFinancingV2Sync> invoke(JsonRootBean input) throws Exception {
        List<EpProductFinancingV2Sync> sync20240129s = new ArrayList<>();

        if (input.getEp_product_financing_v2_sync() != null) {
            input.getEp_product_financing_v2_sync().forEach(vv -> {
                if (null != vv && vv.getDataStatus() == 1) {
                    String financeDate = DateUtil.getStringFormatTimeOfTimestamp(vv.getFinanceDate() * 1000, DateUtil.YMD);

                    // 融资金额和估值
                    String amount = vv.getAmount();
                    String valuation = vv.getValuation();

                    vv.setAmount(FinacingAmountUtil.getFinancingV2Amount(amount));
                    if ("未披露".equals(vv.getAmount())) {
                        //未披露 AmountRmb 默认为null
                    } else if ("0元人民币".equals(vv.getAmount())) {
                        vv.setAmountRmb(BigDecimal.valueOf(0.00).setScale(2, BigDecimal.ROUND_HALF_UP));
                    } else {
                        //人民币计算切换新表计算
                        BigDecimal rm = CtCurrencyUtils.generateDecimalRmbByDesc(
                                CtCurrencyUtils.convertWithUnitAndCurrency(CtCurrencyUtils.getEpCtCurrencyByCapital(amount), financeDate, 4), amount);
                        vv.setAmountRmb(rm);
                    }
                    valuation = FinacingValuationUtil.getFinacingValuation(valuation);
                    //ep.setValuation(FinacingValuationUtil.getFinacingValuation(valuation));
                    if ("未披露".equals(valuation)) {
                        //未披露
                        vv.setValuationRmb(new BigDecimal(0.0000));
                    } else {
                        BigDecimal rm = CtCurrencyUtils.generateDecimalRmbByDesc(
                                CtCurrencyUtils.convertWithUnitAndCurrency(CtCurrencyUtils.getEpCtCurrencyByCapital(valuation), financeDate, 4), valuation);
                        if (null == rm) {
                            vv.setValuationRmb(new BigDecimal(0.0000));
                        } else {
                            vv.setValuationRmb(rm);
                            valuation = FinacingValuationUtil.handleValuation(valuation, financeDate, rm);
                        }
                    }
                    vv.setValuation(valuation);

                }
                sync20240129s.add(vv);
            });
        }
        return sync20240129s;
    }

}
