package com.ld.clean.test.productandfinance;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.base.clean.utils.investment.InvestmentDapUtil;
import com.ld.clean.beans.ProductNotifyEntity;
import com.ld.clean.constants.SourceTypeEnum;
import com.ld.clean.constants.TopicConstants;
import com.ld.clean.context.ApplicationCoreContextManager;
import com.ld.clean.enums.ProductNotifyEnum;
import com.ld.clean.job.productv2.mergercompevents.entity.MergerDetailEntity;
import com.ld.clean.kafka.KafkaHelper;
import com.ld.clean.model.ProductNewsCrawl;
import com.ld.clean.model.manageenterprisedb.*;
import com.ld.clean.model.tidbsearchsyncenterprise.EpProductFinancingV2Sync;
import com.ld.clean.repository.manageenterprisedb.*;
import com.ld.clean.repository.tidbsearchsyncenterprise.EpProductFinancingV2SyncRepository;
import com.ld.clean.utils.DateUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.api.common.functions.RichFlatMapFunction;
import org.apache.flink.util.Collector;
import tk.mybatis.mapper.entity.Condition;

import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 */
public class TmpMergerFlatMap extends RichFlatMapFunction<String, SpecialCompanyNoProduct> {

    private static final DapProductMergerEventsRepository dapProductMergerEventsRepository = ApplicationCoreContextManager.getInstance(DapProductMergerEventsRepository.class);
    private static final CompProductMergerEventsRepository compProductMergeEventsRepository = ApplicationCoreContextManager.getInstance(CompProductMergerEventsRepository.class);

    private static final SpecialCompanyNoProductRepository specialCompanyNoProductRepository = ApplicationCoreContextManager.getInstance(SpecialCompanyNoProductRepository.class);

    private static final ProductBaseinfoRepository productBaseinfoRepository = ApplicationCoreContextManager.getInstance(ProductBaseinfoRepository.class);
    private static final InvestCompanyHolderDiffRepository investCompanyHolderDiffRepository = ApplicationCoreContextManager.getInstance(InvestCompanyHolderDiffRepository.class);


    @Override
    public void flatMap(String s, Collector<SpecialCompanyNoProduct> collector) throws Exception {

        InvestCompanyHolderDiff diff1 = (InvestCompanyHolderDiff)investCompanyHolderDiffRepository.selectByPrimaryKey(s);

        Condition productCondition = new Condition(ProductBaseinfo.class);
        productCondition.createCriteria().andEqualTo("companyId", diff1.getCompanyKeyno()).andEqualTo("dataStatus", 1);
        List<ProductBaseinfo> productBaseinfoList = productBaseinfoRepository.selectByCondition(productCondition);

        if(CollectionUtils.isNotEmpty(productBaseinfoList)){
            diff1.setDataStatus(2);
        }
        if(diff1.getDataStatus() == 2){
            investCompanyHolderDiffRepository.insertBatch(Collections.singletonList(diff1));
            System.out.println(1);
        }
       return;

        /*if (StringUtils.isNotBlank(s)) {
            SpecialCompanyNoProduct pf = (SpecialCompanyNoProduct) specialCompanyNoProductRepository.selectByPrimaryKey(s);
            if(ObjectUtil.isEmpty(pf)){
                return;
            }
            collector.collect(pf);
        }*/
        /*if (StringUtils.isNotBlank(s)) {
            CompProductMergerEvents events = (CompProductMergerEvents) compProductMergeEventsRepository.selectByPrimaryKey(s);
            if(StringUtils.isNotBlank(events.getMergerDetails())){
                List<MergerDetailEntity> mergerDetails = JSON.parseArray(events.getMergerDetails(), MergerDetailEntity.class);
                if (null != mergerDetails && !mergerDetails.isEmpty()) {
                    //获取最后一个对象 time最大的对象
                    Optional<MergerDetailEntity> maxTimeEntity = mergerDetails.stream()
                            .filter(entity -> entity.getTime() != null)
                            .max(Comparator.comparing(entity -> entity.getTime() != null ? entity.getTime() : ""));
                    if (maxTimeEntity.isPresent()) {
                        MergerDetailEntity entity = maxTimeEntity.get();
                        if(StringUtils.isNotBlank(entity.getLink()) && StringUtils.isNotBlank(entity.getDesc()) ){
                            ProductNewsCrawl productNewsCrawl = new ProductNewsCrawl(entity.getDesc(), entity.getLink(), DateUtil.getStringFormatTimeOfTimestamp(DateUtil.getTimestampOfStringDate(events.getFinanceDate(), DateUtil.YMD), DateUtil.YMD_DASH_WITH_TIME));
                            productNewsCrawl.setFinancingId(events.getId());
                            productNewsCrawl.setProductId(events.getProductId());
                            KafkaHelper.javaKafkaProducer("dataclean_product_news_crawl", JSON.toJSONString(productNewsCrawl));
                        }
                    } else {
                        return;
                    }
                }
            }
        }*/


       /* KafkaHelper.javaKafkaProducer(TopicConstants.TOPIC_BASE_CLEAN_COMP_BRAND_FINANCE,
                JSON.toJSONString(new ProductNotifyEntity(ProductNotifyEnum.FINANCE.getCode(), "", s)), true, false);*/

        /*KafkaHelper.javaKafkaProducer("dap_dap_clean_manage_enterprise_db_comp_brand_financing",itt);

        if (StringUtils.isNotBlank(s)) {
            DapProductMergerEvents pf = (DapProductMergerEvents) dapProductMergerEventsRepository.selectByPrimaryKey(s);
            if(ObjectUtil.isEmpty(pf)){
                return;
            }
            collector.collect(pf);
        }*/
    }
}