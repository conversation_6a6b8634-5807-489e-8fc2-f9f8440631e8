package com.ld.clean.util;

import com.ld.clean.beans.InvestEntity;
import com.ld.clean.constants.InvestTypeEnum;
import com.ld.clean.utils.CommonUtil;
import org.apache.commons.lang3.StringUtils;

import java.util.*;


/**
 * <AUTHOR>
 */
public class FunderInvestOsUtil {

    // 给投资机构排序，机构-有限合伙-公司-个人 ，可跳转-不可跳转,领投-跟投
    public static List<InvestEntity> sortInvestment(Set<InvestEntity> investList) {
        List<InvestEntity> list = new ArrayList<>();
        List<InvestEntity> relatedInvest = new ArrayList<>();
        List<InvestEntity> unRelatedInvest = new ArrayList<>();
        List<InvestEntity> relatedLP = new ArrayList<>();
        List<InvestEntity> unRelatedLP = new ArrayList<>();
        List<InvestEntity> relatedCompany = new ArrayList<>();
        List<InvestEntity> unRelatedCompany = new ArrayList<>();
        List<InvestEntity> person = new ArrayList<>();
        List<InvestEntity> product = new ArrayList<>();// 该类别待废弃
        List<InvestEntity> other = new ArrayList<>();
        for (InvestEntity investEntity : investList) {
            String investName = getFinacingInvestName(investEntity.getName());
            if (StringUtils.equalsIgnoreCase(investName, "未披露")) {
                continue;
            }
            investEntity.setName(investName);
            if (investEntity.getCategory() == InvestTypeEnum.INSTITUTION.getCode()) {
                if (StringUtils.isEmpty(investEntity.getId())) {
                    unRelatedInvest.add(investEntity);
                } else {
                    relatedInvest.add(investEntity);
                }
            } else if (investEntity.getCategory() == InvestTypeEnum.COMPANY.getCode()) {
                if (StringUtils.isEmpty(investEntity.getId()) && StringUtils.isEmpty(investEntity.getKeyNo())) {
                    if (investEntity.getName().contains("有限合伙")) {
                        unRelatedLP.add(investEntity);
                    } else {
                        unRelatedCompany.add(investEntity);
                    }
                } else {
                    if (investEntity.getName().contains("有限合伙")) {
                        relatedLP.add(investEntity);
                    } else {
                        relatedCompany.add(investEntity);
                    }
                }
            } else if (investEntity.getCategory() == InvestTypeEnum.PERSON.getCode()) {
                person.add(investEntity);
            } else if (investEntity.getCategory() == InvestTypeEnum.PRODUCT.getCode()) {
                product.add(investEntity);
            } else if (investEntity.getCategory() == InvestTypeEnum.OTHER.getCode()) {
                other.add(investEntity);
            }
        }
        Collections.sort(relatedInvest, Comparator.comparing(InvestEntity::getType));
        Collections.sort(relatedLP, Comparator.comparing(InvestEntity::getType));
        Collections.sort(relatedCompany, Comparator.comparing(InvestEntity::getType));
        Collections.sort(unRelatedInvest, Comparator.comparing(InvestEntity::getType));
        Collections.sort(unRelatedLP, Comparator.comparing(InvestEntity::getType));
        Collections.sort(unRelatedCompany, Comparator.comparing(InvestEntity::getType));
        Collections.sort(person, Comparator.comparing(InvestEntity::getType));
        Collections.sort(product, Comparator.comparing(InvestEntity::getType));
        Collections.sort(other, Comparator.comparing(InvestEntity::getType));
        list.addAll(relatedInvest);
        list.addAll(relatedLP);
        list.addAll(relatedCompany);
        list.addAll(unRelatedInvest);
        list.addAll(unRelatedLP);
        list.addAll(unRelatedCompany);
        list.addAll(person);
        list.addAll(product);
        list.addAll(other);
        return list;
    }

    public static final Set<String> FINACING_INVEST_NAME_ENUMS = new HashSet<>(Arrays.asList("-",
            "投资机构未知",
            "未披露",
            "未知",
            "投资方未知",
            "投资人未知",
            "未透露",
            "投资者未知",
            "投资方未透露",
            "暂未透露"
    ));

    public static String getFinacingInvestName(String investName) {
        if (StringUtils.isBlank(investName)) {
            return "未披露";
        }
        if (FINACING_INVEST_NAME_ENUMS.contains(investName)) {
            return "未披露";
        }

        return CommonUtil.getCompanyNameByName(investName);
    }

}
