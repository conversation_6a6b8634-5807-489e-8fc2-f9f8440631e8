/*
package com.ld.clean.beans;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

*/
/**
 * <AUTHOR>
 *//*

@Data
public class CompanyTagEntity {
    @JSONField(name = "type", ordinal = 1)
    private Integer type;
    @JSONField(name = "Name", ordinal = 2)
    private String name;
    @JSONField(name = "ShortName", ordinal = 3)
    private String shortName;
    @JSONField(name = "DataExtend", ordinal = 4)
    private String dataExtend;
    @JSONField(name = "TradingPlaceCode", ordinal = 5)
    private String tpCode;
    @JSONField(name = "TradingPlaceName", ordinal = 6)
    private String tpNme;
    @JSONField(name = "SourceFrom", ordinal = 7)
    private String sourceFrom;
    @JSONField(name = "DataExtend2", ordinal = 8)
    private String dataExtend2;

    @Data
    public static class DataExtend2 {
        @JSONField(name = "Id", ordinal = 1)
        private String id;

        @JSONField(name = "RoundStage", ordinal = 2)
        private Integer roundStage;

        public DataExtend2(String id, Integer roundStage) {
            this.id = id;
            this.roundStage = roundStage;
        }
    }
}
*/
