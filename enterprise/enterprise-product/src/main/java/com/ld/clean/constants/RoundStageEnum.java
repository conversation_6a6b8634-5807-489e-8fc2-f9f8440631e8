package com.ld.clean.constants;

import lombok.Getter;

/**
 * 轮次阶段
 *
 * <AUTHOR>
 */
@Getter
public enum RoundStageEnum {
    UNKNOW(0, "未知"),
    COMMON(1, "常规轮次"),
    RONGZI(2, "战略/股权融资"),
    ADDISSUE(3, "定向增发"),
    IPO(4, "上市/挂牌"),
    MERGE(5, "并购"),
    IPOOUT(6, "退市/摘牌"),
    OTHER(7, "其他"),
    ;
    private Integer code;

    private String desc;


    RoundStageEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static RoundStageEnum getByCode(int code) {
        for (RoundStageEnum roundLevelEnum : RoundStageEnum.values()) {
            if (code == roundLevelEnum.getCode()) {
                return roundLevelEnum;
            }
        }
        return OTHER;
    }

}

