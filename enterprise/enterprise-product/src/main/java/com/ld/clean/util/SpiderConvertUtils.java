package com.ld.clean.util;

import org.apache.commons.lang3.StringUtils;
import tk.mybatis.mapper.entity.Condition;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public class SpiderConvertUtils {

    public static String checkAndReturnString(String value, String defaultValue) {
        return (StringUtils.isNotEmpty(value) && !value.equals("\"null\"") && !value.equalsIgnoreCase("null") && !value.equalsIgnoreCase("nan") && !value.equals("---") && !value.trim().equals("——") && !value.trim().equals("--") &&
                !value.equals("-") && !value.equals("*") && !value.equals("**") && !value.equals("未持股") &&
                !value.equals("/") && !value.equals("—")) ? value : (StringUtils.isNotEmpty(defaultValue) ? defaultValue : "");
    }



    public static BigDecimal checkAndReturnBigDecimal(String value, int round) {
        return (!"None".equals(value) && StringUtils.isNotEmpty(value)
                && !value.equals("------")
                && !value.equals("%--")
                && !value.equals("---")
                && !value.equals("--")
                && !value.equals("-")
                && !value.contains("待")
                && !"/".equals(value)
                && !value.equalsIgnoreCase("null")
                /*     && !value.contains("-")*/
                && !value.equals("*")
                && !value.equals("**")) ? new BigDecimal(value.replaceAll(",", "").replaceAll("/td>", "").replaceAll("td>", "")).setScale(round, BigDecimal.ROUND_HALF_UP) : null;
    }
}
