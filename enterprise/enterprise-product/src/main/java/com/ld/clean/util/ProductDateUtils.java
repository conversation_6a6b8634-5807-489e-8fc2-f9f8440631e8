package com.ld.clean.util;

import joptsimple.internal.Strings;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 */
public class ProductDateUtils {


    /**
     * @param dateStr
     * 1、20230101
     * 2、2023-01-01
     * 3、2023/01/01
     * 4、2023.01.01
     * 5、2023年01月01日
     * @return 10位标准日期格式
     */
    public static String formartDate(String dateStr) {
        if (StringUtils.isNotBlank(dateStr)) {
            String format = dateStr.replace("年", "#").replace("月", "#").replace("日", "#").replace(".", "#").replace("-", "#").replace("/","#");

            String[] arr = format.split("#");
            StringBuilder sb = new StringBuilder();
            sb.append(arr[0]);
            if(arr.length ==3){
                if(arr[1].length() <2){
                    sb.append("0").append(arr[1]);
                }else{
                    sb.append(arr[1]);
                }
                if(arr[2].length() <2){
                    sb.append("0").append(arr[2]);
                }else{
                    sb.append(arr[2]);
                }

            }
            if(sb.toString().length() ==8){
              //  return  sb.toString().substring(0,4)+"-"+sb.toString().substring(4,6)+"-"+sb.toString().substring(6,8);
                return  sb.toString();
            }
        }
        return Strings.EMPTY;
    }

    public static void main(String[] args) {
        System.out.println(formartDate("20230101"));
        System.out.println(formartDate("2023101"));
        System.out.println(formartDate("2023-01-01"));
        System.out.println(formartDate("2023/01/01"));
        System.out.println(formartDate("2023/1/01"));
        System.out.println(formartDate("2023/01/1"));
        System.out.println(formartDate("2023.01.01"));
        System.out.println(formartDate("2023年01月01日"));
        System.out.println(formartDate("2023年01月01"));
    }
}
