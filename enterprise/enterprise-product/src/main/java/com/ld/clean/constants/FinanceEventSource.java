package com.ld.clean.constants;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 */

@Getter
@AllArgsConstructor
public enum FinanceEventSource {
    PUBLIC("public_event", 1),
    PARTNER("partner_change", 2),
    FINANCIAL("financial_event", 3);

    private String name;
    private Integer code;

    public static FinanceEventSource getByCode(Integer code) {
        for (FinanceEventSource eventSource : FinanceEventSource.values()) {
            if (eventSource.getCode().equals(code)) {
                return eventSource;
            }
        }
        return null;
    }

    public static FinanceEventSource getByName(String name) {
        for (FinanceEventSource eventSource : FinanceEventSource.values()) {
            if (eventSource.getName().equals(name)) {
                return eventSource;
            }
        }
        return null;
    }
}
