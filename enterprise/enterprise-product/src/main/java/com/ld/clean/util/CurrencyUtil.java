package com.ld.clean.util;

import com.ld.clean.beans.Currency;
import com.ld.clean.context.ApplicationCoreContextManager;
import com.ld.clean.model.baseenterprisedb.TExchangeRate;
import com.ld.clean.repository.baseenterprisedb.TExchangeRateRepository;
import com.ld.clean.util.ct.CtCacheDataUtil;
import com.ld.clean.utils.DateUtil;
import com.ld.clean.utils.Log;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import tk.mybatis.mapper.entity.Condition;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 */
public class CurrencyUtil {

    private static final TExchangeRateRepository tExchangeRateRepository = ApplicationCoreContextManager.getInstance(TExchangeRateRepository.class);


    public static Currency getCurrenyByMoneyNew(String amount, String amountHide, String financeDate) {
        String unitFromAmount = Currency.getUnitFromAmount(amount);
        Double rateAmount = getCurrentRateNew(unitFromAmount, financeDate);
        Currency currencyAmount = Currency.getRmb(amount, unitFromAmount, rateAmount);
        if (StringUtils.isNotEmpty(amountHide)) {
            unitFromAmount = Currency.getUnitFromAmount(amountHide);
            // 实际计算amount，如果没有AmountHide，才用Amount
            Currency currencyAmountHide = Currency.getRmb(amountHide, unitFromAmount, rateAmount);
            currencyAmount.setAmountHide(currencyAmountHide.getAmount());
            currencyAmount.setAmountRmb(currencyAmountHide.getAmountRmb());
        }
        return currencyAmount;
    }

    public static Currency getCurrenyByMoney(String amount, String amountHide, String financeDate) {
        String unitFromAmount = Currency.getUnitFromAmount(amount);
        Double rateAmount = getCurrentRate(unitFromAmount, financeDate);
        Currency currencyAmount = Currency.getRmb(amount, unitFromAmount, rateAmount);
        if (StringUtils.isNotEmpty(amountHide)) {
            unitFromAmount = Currency.getUnitFromAmount(amountHide);
            // 实际计算amount，如果没有AmountHide，才用Amount
            Currency currencyAmountHide = Currency.getRmb(amountHide, unitFromAmount, rateAmount);
            currencyAmount.setAmountHide(currencyAmountHide.getAmount());
            currencyAmount.setAmountRmb(currencyAmountHide.getAmountRmb());
        }
        return currencyAmount;
    }

    public static Double getCurrentRateNew(String unitFromAmount, String financeDate) {
        if (StringUtils.isNotBlank(unitFromAmount)) {
            if (unitFromAmount.equalsIgnoreCase("人民币") || unitFromAmount.equalsIgnoreCase("RMB")) {
                return 1.0d;
            }
            TExchangeRate exchangeRate =  CtCacheDataUtil.getTExchangeRateByCurrenCn(unitFromAmount,financeDate);
            if(exchangeRate !=null){
                return Double.parseDouble(exchangeRate.getConversionRate()) / 100;
            }

           /* Condition condition = new Condition(TExchangeRate.class);
            condition.setOrderByClause("PublishDate DESC, PublishTime DESC LIMIT 1");
            condition.createCriteria().andEqualTo("currencyCN", unitFromAmount)
                    .andLessThanOrEqualTo("publishDate", DateUtil.getLocalDateOfStringFormatTime(financeDate, DateUtil.YMD).toString());
            List<TExchangeRate> list = tExchangeRateRepository.selectByCondition(condition);
            if (CollectionUtils.isNotEmpty(list)) {
                return Double.parseDouble(list.get(0).getConversionRate()) / 100;
            }*/
        }
        return Currency.CURRENCY.get(unitFromAmount);
    }

    public static Double getCurrentRate(String unitFromAmount, String financeDate) {
        if (StringUtils.isNotBlank(unitFromAmount)) {
            if (unitFromAmount.equalsIgnoreCase("人民币") || unitFromAmount.equalsIgnoreCase("RMB")) {
                return 1.0d;
            }
            Condition condition = new Condition(TExchangeRate.class);
            condition.setOrderByClause("PublishDate DESC, PublishTime DESC LIMIT 1");
            condition.createCriteria().andEqualTo("currencyCN", unitFromAmount)
                    .andLessThanOrEqualTo("publishDate", DateUtil.getLocalDateOfStringFormatTime(financeDate, DateUtil.YMD).toString());
            List<TExchangeRate> list = tExchangeRateRepository.selectByCondition(condition);
            if (CollectionUtils.isNotEmpty(list)) {
                return Double.parseDouble(list.get(0).getConversionRate()) / 100;
            }
        }
        return Currency.CURRENCY.get(unitFromAmount);
    }

    /**
     * 管理规模字段值处理
     */
    public static Currency getRegisterCapiScale(String amount) {
        Currency currency = new Currency();
        currency.setAmount(amount.replace("元", ""));
        currency.setAmountRmb(0L);

        try {
            if (StringUtils.isNotBlank(amount)) {
                /**
                 * 管理规模数值统一处理为"亿"或"万"
                 * 小数点取后两位，直接截取处理
                 * 当规模小于10000万时，按万人民币/美元显示。
                 */
                Matcher matcher = Pattern.compile("([0-9\\.]+)(元人民币|万元人民币|亿元人民币)").matcher(amount.trim().replace(" ", ""));
                if (matcher.find()) {
                    BigDecimal num = new BigDecimal(matcher.group(1)).setScale(2, RoundingMode.HALF_UP).stripTrailingZeros();
                    String unitDesc = matcher.group(2);
                    String unit = unitDesc.contains("人民币") ? "人民币" : unitDesc.replace("万", "").replace("亿", "");
                    if (unitDesc.contains("亿")) {
                        currency.setAmount(num.stripTrailingZeros().toPlainString() + "亿" + unit);
                        currency.setAmountDecimalRmb(num.multiply(new BigDecimal(100000000L)));
                        currency.setAmountRmb(currency.getAmountDecimalRmb().longValue());
                    } else if (unitDesc.contains("万")) {
                        if (num.compareTo(new BigDecimal(10000)) > 0) {
                            String scaleValue = num.divide(new BigDecimal(10000)).setScale(2, RoundingMode.HALF_UP).stripTrailingZeros().toPlainString();
                            currency.setAmount(scaleValue + "亿" + unit);
                        } else {
                            currency.setAmount(num.stripTrailingZeros().toPlainString() + "万" + unit);
                        }
                        currency.setAmountDecimalRmb(num.multiply(new BigDecimal(10000L)));
                        currency.setAmountRmb(currency.getAmountDecimalRmb().longValue());
                    } else {
                        if (num.compareTo(new BigDecimal(100000000)) > 0) {
                            String scaleValue = num.divide(new BigDecimal(100000000)).setScale(2, RoundingMode.HALF_UP).stripTrailingZeros().toPlainString();
                            currency.setAmount(scaleValue + "亿" + unit);
                        } else if (num.compareTo(new BigDecimal(10000)) > 0) {
                            String scaleValue = num.divide(new BigDecimal(10000)).setScale(2, RoundingMode.HALF_UP).stripTrailingZeros().toPlainString();
                            currency.setAmount(scaleValue + "万" + unit);
                        }

                        currency.setAmountDecimalRmb(num);
                        currency.setAmountRmb(num.longValue());
                    }
                }
            }
        } catch (Exception e) {
            Log.getInst().error("金额转换失败", "error:{},data:{}", ExceptionUtils.getStackTrace(e), amount);
        }
        return currency;
    }

}
