package com.ld.clean.constants;

/**
 * 记录处理状态
 *
 * <AUTHOR>
 */
public enum RecordStatusEnum {
    /*
     * 未处理
     */
    NOT(0, "未处理"),
    /*
     * 已处理
     */
    YES(1, "已处理"),
    /*
     * 无需处理
     */
    NO_NEED(2, "无需处理"),
    /*
     * 待审核
     */
    CHECK(3, "待审核");

    private Integer value;
    private String desc;


    RecordStatusEnum(Integer value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public Integer getValue() {
        return value;
    }

    public String getDesc() {
        return desc;
    }

}
