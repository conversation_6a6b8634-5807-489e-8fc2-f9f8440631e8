package com.ld.clean.test.financev2sync;

import com.ld.clean.context.ApplicationCoreContextManager;
import com.ld.clean.step.StepConstructorParam;
import com.ld.clean.test.financev2sync.step.FinanceV2TestMainStep;
import com.ld.clean.utils.CheckPointUtil;
import com.ld.clean.utils.ExecutionEnvUtil;
import org.apache.flink.api.java.utils.ParameterTool;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;

/**
 * <AUTHOR>
 * @date 2023/07/07 19:33
 */
public class FinanceV2TestApplication {

    public static void main(String[] args) throws Exception {
        ParameterTool propertiesParams = ExecutionEnvUtil.PARAMETER_TOOL;
        StreamExecutionEnvironment env = CheckPointUtil.setCheckpointConfig(ExecutionEnvUtil.prepare(propertiesParams), propertiesParams);
        ParameterTool dmpParams = ParameterTool.fromArgs(args);
        new FinanceV2TestMainStep(StepConstructorParam.builder().env(env).propertiesParams(propertiesParams).dmpParams(dmpParams).build()).process();
        env.execute(ApplicationCoreContextManager.getAppId());
    }
}
