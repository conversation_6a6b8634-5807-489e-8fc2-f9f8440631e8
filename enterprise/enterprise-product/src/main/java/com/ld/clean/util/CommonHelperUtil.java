package com.ld.clean.util;

import com.alibaba.fastjson.JSONObject;
import com.ld.clean.constants.MyConstants;
import com.ld.clean.dto.company.CompanyCommonOut;
import com.ld.clean.dto.company.CompanyTagOut;
import com.ld.clean.dto.company.QccCompanyOut;
import com.ld.clean.job.product.monitorcompanychange.entity.NameKeyNoTagsEntity;
import com.ld.clean.utils.CommonUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 */
public class CommonHelperUtil {

    /**
     * 返回统一的跳转结构
     */
 /*   public static NameKeyNoTagsEntity getNameKeyNoTagsEntity(String name, String keyNo) {
        try {
            NameKeyNoTagsEntity nameKeyTagsEntity = new NameKeyNoTagsEntity();
            nameKeyTagsEntity.setName(name);
            // 根据keyNo的情况，决定基本字段信息
            List<NameKeyNoTagsEntity> tagList = new ArrayList<>();
            if (StringUtils.isNotBlank(keyNo)) {
                if (CommonUtil.isCompanyKeyNo(keyNo)) {
                    QccCompanyOut qccCompanyOut = CtKeynoUtil.getQccCompanyOut(keyNo);
                    // 判断是否关联投资机构 / 私募基金
                    if (qccCompanyOut != null) {
                        List<CompanyTagOut> companyTagOutList = CommonHelperUtil.getList(qccCompanyOut.getTags());
                        for (CompanyTagOut companyTagOut : companyTagOutList) {
                            if (companyTagOut != null && companyTagOut.getType() != null) {
                                if (companyTagOut.getType() == 208) { // 投资机构
                                    String institutionId = CommonUtil.getString(companyTagOut.getDataExtend());
                                    String institutionName = CommonUtil.getString(companyTagOut.getName());
                                    tagList.add(new NameKeyNoTagsEntity("投资机构：" + institutionName, institutionId,
                                            CommonHelperUtil.getUrlByPrefixAndId(MyConstants.INVESTOR_PREFIX, institutionId)));
                                }
                            }
                        }
                        nameKeyTagsEntity.setKeyno(keyNo);
                        nameKeyTagsEntity.setNameUrl(MyConstants.FIRM_PREFIX + keyNo + MyConstants.HTML);
                    }
                }
            }

            nameKeyTagsEntity.setTags(tagList);
            return nameKeyTagsEntity;
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        return null;
    }*/

    /**
     * 创建公司的跳转格式 {"Name":"xxx","KeyNo":"xxx","Org":1,"HasImage":0}
     */
    public static JSONObject getNameAndKeyNo(String companyName, String companyKeyNo, QccCompanyOut qccCompanyOut) {
        if (StringUtils.isNotBlank(companyName)) {
            JSONObject nameAndKeyNo = new JSONObject();
            nameAndKeyNo.put("Name", companyName);
            nameAndKeyNo.put("KeyNo", CommonUtil.getString(companyKeyNo));
            nameAndKeyNo.put("Org", -1);
            nameAndKeyNo.put("HasImage", 0);

            if (StringUtils.isNotBlank(nameAndKeyNo.getString("KeyNo"))) {
                Integer org = CompanyCommonUtil.getOrgByKeyNo(nameAndKeyNo.getString("KeyNo"));
                nameAndKeyNo.put("Org", org);

                if (qccCompanyOut != null && StringUtils.isNotBlank(qccCompanyOut.getImageUrl())) {
                    nameAndKeyNo.put("HasImage", 1);
                }
            }
            return nameAndKeyNo;
        }
        return new JSONObject();
    }

    /**
     * 返回统一的跳转结构
     */
    public static NameKeyNoTagsEntity getNameKeyNoTagsEntity(QccCompanyOut qccCompanyOut) {
        try {
            NameKeyNoTagsEntity nameKeyTagsEntity = new NameKeyNoTagsEntity();
            nameKeyTagsEntity.setName(qccCompanyOut.getName());
            // 根据keyNo的情况，决定基本字段信息
            List<NameKeyNoTagsEntity> tagList = new ArrayList<>();

            // 判断是否关联投资机构 / 私募基金
            if (qccCompanyOut != null) {
                String keyNo = qccCompanyOut.getKeyNo();
                List<CompanyTagOut> companyTagOutList = CommonHelperUtil.getList(qccCompanyOut.getTags());
                for (CompanyTagOut companyTagOut : companyTagOutList) {
                    if (companyTagOut != null && companyTagOut.getType() != null) {
                        if (companyTagOut.getType() == 207) { // 私募基金管理人
                            tagList.add(new NameKeyNoTagsEntity("私募基金管理人", keyNo));
                        } else if (companyTagOut.getType() == 209) { // 私募基金产品
                            JSONObject dataExtendJsonObject = getJSONObject(companyTagOut.getDataExtend());
                            if (dataExtendJsonObject != null && StringUtils.isNotBlank(dataExtendJsonObject.getString("ProductId2"))) {
                                tagList.add(new NameKeyNoTagsEntity("私募基金", dataExtendJsonObject.getString("ProductId2")));
                            }
                        }
                    }
                }

                List<CompanyCommonOut> commonList = qccCompanyOut.getCommonList();
                for (CompanyCommonOut companyCommonOut : commonList) {
                    // 投资机构
                    if (companyCommonOut.getKey() == 2&&StringUtils.isNotBlank(companyCommonOut.getValue())) {
                        //{"Id":"fd46f07f78219e35f975ac596d5077b8","Name":"新物种私募基金","Logo":"","NC":0,"FC":0}
                        JSONObject jsonObject = JSONObject.parseObject(companyCommonOut.getValue());
                        String institutionId = CommonUtil.getString(jsonObject.getString("Id"));
                        String institutionName = CommonUtil.getString(jsonObject.getString("Name"));
                        tagList.add(new NameKeyNoTagsEntity("投资机构：" + institutionName, institutionId,
                                CommonHelperUtil.getUrlByPrefixAndId(MyConstants.INVESTOR_PREFIX, institutionId)));
                    }
                }

                nameKeyTagsEntity.setKeyno(keyNo);
                nameKeyTagsEntity.setNameUrl(MyConstants.FIRM_PREFIX + keyNo + MyConstants.HTML);
            }

            nameKeyTagsEntity.setTags(tagList);
            return nameKeyTagsEntity;
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        return null;
    }

    /**
     * 获取非null的集合对象
     *
     * @param list
     * @return
     */
    public static <T> List<T> getList(List<T> list) {
        if (CollectionUtils.isEmpty(list)) {
            return new ArrayList<>();
        }
        return list;
    }

    /**
     * 获取非null的Set集合对象
     */
    public static <T> Set<T> getSet(Set<T> set) {
        if (CollectionUtils.isEmpty(set)) {
            return new HashSet<>();
        }
        return set;
    }

    public static JSONObject getJSONObject(String input) {
        JSONObject jsonObject = new JSONObject();
        try {
            if (StringUtils.isNotBlank(input)) {
                jsonObject = JSONObject.parseObject(input);
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        return jsonObject;
    }

    public static String getUrlByPrefixAndId(String prefix, String id) {
        return prefix + id + MyConstants.HTML;
    }
}
