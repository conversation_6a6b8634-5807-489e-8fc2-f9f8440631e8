package com.ld.clean.util;

import com.ld.clean.context.ApplicationCoreContextManager;
import com.ld.clean.model.ecidata.ProductTagCode;
import com.ld.clean.model.qccproduct.ProductIndustry;
import com.ld.clean.pojo.mo.ct.TagEntity;
import com.ld.clean.repository.ecidata.ProductTagCodeRepository;
import com.ld.clean.repository.qccproduct.ProductIndustryRepository;
import com.ld.clean.utils.MD5Util;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import tk.mybatis.mapper.entity.Condition;

import java.util.*;

/**
 * <AUTHOR>
 */
public class IndustryUtils {

    private static ProductIndustryRepository productIndustryRepository = ApplicationCoreContextManager.getInstance(ProductIndustryRepository.class);
    private static ProductTagCodeRepository productTagCodeRepository = ApplicationCoreContextManager.getInstance(ProductTagCodeRepository.class);

    public static Map<String, String> PRODUCT_CODE_MAP = new HashMap<>();
    public static Map<String, String> INDUSTRY_CODE_MAP = new HashMap<>();
    public static Map<String, String> INDUSTRY_NAME_MAP = new HashMap<>();
    public static Map<String, ProductIndustry> INDUSTRY_MAP = new HashMap<>();

    static {
        initIndustry();
        initProductTagCodes();
    }

    public static void initIndustry() {
        Condition condition = new Condition(ProductIndustry.class);
        condition.createCriteria().andIsNotNull("id");
        List<ProductIndustry> list = productIndustryRepository.selectByCondition(condition);
        for (ProductIndustry industry : list) {
            INDUSTRY_CODE_MAP.put(industry.getCode(), industry.getName());
            INDUSTRY_NAME_MAP.put(industry.getName(), industry.getCode());
            INDUSTRY_MAP.put(industry.getCode(), industry);
        }
    }

    public static void initProductTagCodes() {
        Condition condition = new Condition(ProductTagCode.class);
        condition.createCriteria().andIsNotNull("id");
        List<ProductTagCode> list = productTagCodeRepository.selectByCondition(condition);
        for (ProductTagCode tagCode : list) {
            PRODUCT_CODE_MAP.put(tagCode.getTag(), tagCode.getId());
        }
    }

    public static String getTagCode(String tag) {
        if (StringUtils.isNotBlank(tag)) {
            String code = PRODUCT_CODE_MAP.get(tag);
            if (StringUtils.isBlank(code)) {
                Condition condition = new Condition(ProductTagCode.class);
                condition.createCriteria().andEqualTo("tag", tag);
                List<ProductTagCode> list = productTagCodeRepository.selectByCondition(condition);
                if (CollectionUtils.isNotEmpty(list)) {
                    PRODUCT_CODE_MAP.put(tag, list.get(0).getId());
                } else {
                    code = MD5Util.encode(tag);
                    ProductTagCode productTagCode = new ProductTagCode();
                    productTagCode.setId(code);
                    productTagCode.setTag(tag);
                    productTagCodeRepository.insertBatch(Collections.singletonList(productTagCode));
                    PRODUCT_CODE_MAP.put(tag, code);
                }
            }
            return code;
        }
        return null;
    }

    public static String getIndustryTagCode(String tagName) {
        if (StringUtils.isNotBlank(tagName)) {
            String code = INDUSTRY_NAME_MAP.get(tagName);
            if (StringUtils.isBlank(code)) {
                Condition condition = new Condition(ProductIndustry.class);
                condition.createCriteria().andEqualTo("name", tagName);
                List<ProductIndustry> list = productIndustryRepository.selectByCondition(condition);
                if (CollectionUtils.isNotEmpty(list)) {
                    code = list.get(0).getCode();
                    INDUSTRY_NAME_MAP.put(tagName, code);
                }
            }
            return code;
        }
        return null;
    }

    public static String getIndustryTagName(String code) {
        if (StringUtils.isNotBlank(code)) {
            String name = INDUSTRY_CODE_MAP.get(code);
            if (StringUtils.isBlank(name)) {
                Condition condition = new Condition(ProductIndustry.class);
                condition.createCriteria().andEqualTo("code", code);
                List<ProductIndustry> list = productIndustryRepository.selectByCondition(condition);
                if (CollectionUtils.isNotEmpty(list)) {
                    name = list.get(0).getName();
                    INDUSTRY_NAME_MAP.put(code, name);
                }
            }
            return name;
        }
        return null;
    }

    public static List<TagEntity> processTags(String tags) {
        Set<String> productTagCodes = new HashSet();
        List<TagEntity> entities = new ArrayList<>();
        if (StringUtils.isNotEmpty(tags)) {
            for (String tag : tags.split(",")) {
                tag = tag.replaceAll("[/\r\n\t]", "").toUpperCase().trim();
                if (StringUtils.isEmpty(tag)) {
                    continue;
                }
                String code = IndustryUtils.getTagCode(tag);
                if (StringUtils.isNotEmpty(code) && !productTagCodes.contains(code)) {
                    productTagCodes.add(code);

                    TagEntity entity = new TagEntity();
                    entity.setCode(code);
                    entity.setTag(tag);
                    entities.add(entity);
                }
            }
        }
        return entities;
    }

}
