package com.ld.clean.constants;

/**
 * 新闻主体类型
 *
 * <AUTHOR>
 */
public enum NewsIdentificationTypeEnums {
    /*
     * 独立公司（默认）
     */
    INDEPENDENCE(0),
    /*
     * 机构
     */
    INSTITUTION(1),
    /*
     * 机构别名
     */
    INSTITUTION_ALIAS(2),
    /*
     * 机构下属企业
     */
    INSTITUTION_ENTERPRISE(3),
    /*
     * 企业别名
     */
    INSTITUTION_ENTERPRISE_ALIAS(4),
    /*
     * 产品
     */
    PRODUCT(5),
    /*
     * 产品别名
     */
    PRODUCT_ALIAS(6),
    /*
     * 产品所属企业
     */
    PRODUCT_ENTERPRISE(7),
    /*
     * 产品所属企业别名
     */
    PRODUCT_ENTERPRISE_ALIAS(8),
    /*
     * 人物
     */
    PERSON(9),
    /*
     * 未知
     */
    UNKNOW(10);

    private Integer value;

    NewsIdentificationTypeEnums(Integer value) {
        this.value = value;
    }

    public Integer getValue() {
        return value;
    }

}
