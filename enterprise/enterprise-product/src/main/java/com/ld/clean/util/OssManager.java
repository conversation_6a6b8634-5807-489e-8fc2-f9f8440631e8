/*
package com.ld.clean.util;

import com.aliyun.oss.OSSClient;
import com.aliyun.oss.model.OSSObject;
import com.aliyun.oss.model.ObjectMetadata;
import com.aliyun.oss.model.SimplifiedObjectMeta;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.io.*;
import java.nio.charset.StandardCharsets;

*/
/**
 * 阿里oss相关方法
 *
 * <AUTHOR>
 *//*

@Slf4j
public class OssManager {
    private final static String ENDPOINT_DEV = "http://oss-cn-hangzhou.aliyuncs.com";
    private final static String ACCESS_KEY_ID = "";
    private final static String ACCESS_KEY_SECRET = "fekrZbvdFfAQl96GpYk47XSPwnKnQN";

    private static OSSClient getOssClient() {
        //默认dev
        return new OSSClient(ENDPOINT_DEV, ACCESS_KEY_ID, ACCESS_KEY_SECRET);
    }

    */
/**
     * 上传本地图片到OSS
     *
     * @param ins        输入流
     * @param key        上传后在OSS上面的文件名
     * @param bucketName 包名
     * @return true-成功，false-失败
     *//*

    public static boolean upLoadImage(OSSClient client, InputStream ins, String type, String key, String bucketName) {

        boolean result = true;
        try {
            if (client == null) {
                client = getOssClient();
            }
            ObjectMetadata objectMetadata = new ObjectMetadata();
            if (StringUtils.isEmpty(type)) {
                objectMetadata.setContentType("image/jpeg");
            } else {
                objectMetadata.setContentType(type);
            }
            if (StringUtils.isNotEmpty(type) && !type.contains("pdf")) {
                objectMetadata.setContentLength(ins.available());
            }
            client.putObject(bucketName, key, ins, objectMetadata);
        } catch (Exception e) {
            log.error("上传文件到OSS异常：", e);
            result = false;
        }
        return result;
    }

    */
/**
     * 删除某个Object
     *
     * @param client     client
     * @param bucketName 桶名
     * @param key        文件路径
     * @return 是否删除成功
     *//*

    public static boolean deleteObject(OSSClient client, String bucketName, String key) {
        try {
            // 删除Object
            client.deleteObject(bucketName, key);
        } catch (Exception e) {
            log.error("删除oss文件失败：", e);
            e.printStackTrace();
            return false;
        } finally {
            client.shutdown();
        }
        return true;
    }


    public static boolean getImage(OSSClient client, String bucketName, String key) {
        BufferedReader reader = null;
        int retry = 3;
        while (retry-- > 0) {
            try {
                if (client == null) {
                    client = getOssClient();
                }
                OSSObject object = client.getObject(bucketName, key);
                if (null == object) {
                    return false;
                }
                reader = new BufferedReader(new InputStreamReader(object.getObjectContent()));
                return true;
            } catch (Exception e) {
                log.error("获取OSS异常：{}", key);
            } finally {
                if (reader != null) {
                    try {
                        reader.close();
                    } catch (IOException ignored) {

                    }
                }
            }
        }
        return false;
    }

    public static InputStream getImageIns(OSSClient client, String bucketName, String key) {
        try {
            if (client == null) {
                client = getOssClient();
            }
            OSSObject object = client.getObject(bucketName, key);
            if (null == object) {
                return null;
            }
            return object.getObjectContent();
        } catch (Exception e) {
            log.error("获取OSS异常：{}", key);
        }
        return null;
    }

    public static SimplifiedObjectMeta getSimplifiedObjectMeta(OSSClient client, String bucketName, String key) {
        try {
            if (client == null) {
                client = getOssClient();
            }
            return client.getSimplifiedObjectMeta(bucketName, key);
        } catch (Exception e) {
            log.error("获取OSS元数据异常：{}", key);
        }
        return null;
    }

    public static boolean uploadText(OSSClient client, String context, String key, String bucketName) {
        boolean result = true;
        ByteArrayInputStream bi = null;
        try {
            ObjectMetadata objectMetadata = new ObjectMetadata();
            objectMetadata.setContentType("text/plain");
            if (client == null) {
                getOssClient();
            }
            bi = new ByteArrayInputStream(context.getBytes(StandardCharsets.UTF_8));
            // 不存在文件
            client.putObject(bucketName, key, bi, objectMetadata);
        } catch (Exception oe) {
            log.error("数据上传时间" + key);
            result = false;
        } finally {
            if (bi != null) {
                try {
                    bi.close();
                } catch (IOException e) {
                    log.error(e.getMessage(), e);
                }
            }
        }
        return result;
    }

    */
/**
     * 获取文件内容
     *
     * @param ossClient  client 连接
     * @param bucketName bucket
     * @param file       bucket下的路径
     *//*

    public static String getObject(OSSClient ossClient, String bucketName, String file) {
        try {
            OSSObject ossObject = ossClient.getObject(bucketName, file);
            BufferedReader reader = new BufferedReader(new InputStreamReader(ossObject.getObjectContent()));
            StringBuilder stringBuilder = new StringBuilder();
            String line;
            while (true) {
                line = reader.readLine();
                if (line == null) {
                    break;
                }
                stringBuilder.append(line);
            }
            reader.close();
            return stringBuilder.toString();
        } catch (Exception e) {
            return "";
        }
    }
}
*/
