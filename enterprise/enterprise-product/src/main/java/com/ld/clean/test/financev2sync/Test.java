package com.ld.clean.test.financev2sync;

import com.alibaba.fastjson.JSON;
import com.alibaba.nacos.shaded.com.google.common.collect.Lists;
import com.ld.clean.beans.ProductNotifyEntity;
import com.ld.clean.constants.TopicConstants;
import com.ld.clean.context.ApplicationCoreContextManager;
import com.ld.clean.dao.model.basecleandatatmp.ScSeEpProductFinancingV2Sync20240129;
import com.ld.clean.dao.repository.basecleandatatmp.ScSeEpProductFinancingV2Sync20240129Repository;
import com.ld.clean.kafka.KafkaHelper;
import com.ld.clean.repository.CompBrandFinancingRepository;
import com.ld.clean.repository.manageenterprisedb.CompProductFinancingRepository;
import com.ld.clean.utils.CtCurrencyUtils;
import com.ld.clean.utils.DateUtil;

import java.io.*;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static com.ld.clean.utils.DateUtil.YMD;

public class Test {
    private static final CompProductFinancingRepository compProductFinancingRepository = ApplicationCoreContextManager.getInstance(CompProductFinancingRepository.class);
    private static final CompBrandFinancingRepository compBrandFinancingRepository = ApplicationCoreContextManager.getInstance(CompBrandFinancingRepository.class);

    private static final ScSeEpProductFinancingV2Sync20240129Repository scSeEpProductFinancingV2Sync20240129Repository = ApplicationCoreContextManager.getInstance(ScSeEpProductFinancingV2Sync20240129Repository.class);
    private static List<String> readTxtFile(String filePath) throws IOException {
        String encoding = "UTF-8";
        List<String> list = new ArrayList<>();
        InputStreamReader read = null;
        try {
            File file = new File(filePath);
            if(file.isFile() && file.exists()){
                read = new InputStreamReader(new FileInputStream(file),encoding);
                BufferedReader bufferedReader = new BufferedReader(read);
                String lineTxt = null;
                while ((lineTxt = bufferedReader.readLine())!=null){
                    list.add(lineTxt);
                }

            }
        }catch (Exception e){
            e.getStackTrace();
        }finally {
            if(read!=null){
                read.close();
            }
        }
        return list;
    }

    public static void mainxxx(String[] args) {
        List<String> ids = Arrays.asList("4dda834d-0ef5-45b3-96e9-f69275e96558",
                "30e04daab0b00ada558b4de275462dd9",
                "30e04daab0b00ada558b4de275462dd9",
                "c1717024-93c1-440d-955d-2401c13d7be0",
                "4dc276d8928da99e205e6fae97f05388",
                "1fcda717951e0fe90cd9748f33febdfe",
                "5e89a652909056619935d5f14a536fa0",
                "f24295a2cc4ee6e84c240413ea04c429",
                "96800639-9960-4dc6-bfd7-66950a7968af",
                "96800639-9960-4dc6-bfd7-66950a7968af",
                "5e89a652909056619935d5f14a536fa0",
                "68d501427cb078ecc11bd70172dc3313",
                "4dc276d8928da99e205e6fae97f05388",
                "bc21a1ba76ebdc7cd0cac915809b75e3",
                "90b1bd1288b13deb4b9a4de33ccce34f",
                "9619ac578e0a7b21a32dc488fcb66eda",
                "06d39900161a2210ab089262b03ecf13",
                "bc21a1ba76ebdc7cd0cac915809b75e3",
                "4dc276d8928da99e205e6fae97f05388",
                "ae18ceb448b12bd3e545f960d41b7de6",
                "57e38388-530a-4e42-8abd-c2941d771e09",
                "4dc276d8928da99e205e6fae97f05388",
                "34465fee9f4288eb44b0cf49e3277145",
                "913632a8-a8cd-4d72-9ad5-a6b18bf9d19f"


        );

      /*  Condition c =new Condition(CompBrandFinancing.class);
        c.createCriteria().andIn("brandId",ids);
        List<CompBrandFinancing> list = compBrandFinancingRepository.selectByCondition(c);*/
       /* list.forEach(ittt->{*/
            ProductNotifyEntity productNotifyEntity =new ProductNotifyEntity();
            productNotifyEntity.setFinanceIdList("a6651a87860c01df097b225343a1db6c,05828fdbbe69a9e3683de65495335f10");
            productNotifyEntity.setType(2);
            KafkaHelper.javaKafkaProducer(TopicConstants.TOPIC_BASE_CLEAN_COMP_BRAND_FINANCE, JSON.toJSONString(productNotifyEntity));

            //    System.out.println(ittt.getId());
         //   KafkaHelper.javaKafkaProducer("dap_dap_clean_manage_enterprise_db_comp_brand_financing",ittt.getId());
      //  });
    }
    public static void main(String[] args) throws Exception {

        List<String> keynoList =new ArrayList<>();
        try {
            FileReader f = new FileReader("C:\\Users\\<USER>\\Desktop\\new113.txt");
            BufferedReader b = new BufferedReader(f);
            String s;

            while ((s = b.readLine()) != null) {
                keynoList.add(s);
            }

            b.close();
            f.close();

        } catch (IOException e) {

        }
        System.out.println(keynoList.size());
        Lists.partition(keynoList,1500).forEach(k->{
            System.out.println(k.size());
            List<ScSeEpProductFinancingV2Sync20240129> v2List =scSeEpProductFinancingV2Sync20240129Repository.selectByPrimaryKeyList(k);
            v2List.forEach(itt->{
                if("未披露".equals(itt.getAmount())){
                    itt.setAmountRmb(null);
                }else{
                    itt.setAmountRmb(CtCurrencyUtils.generateDecimalRmbByDesc(CtCurrencyUtils.convertWithUnitAndCurrency(CtCurrencyUtils.getEpCtCurrencyByCapital(itt.getAmount()),itt.getFinanceDate() !=null? DateUtil.getStringFormatTimeOfTimestamp(itt.getFinanceDate()*1000,YMD) :"",2),itt.getAmount()));

                }

            });
            scSeEpProductFinancingV2Sync20240129Repository.insertBatch(v2List);
        });

    }


}
