package com.ld.clean.test.financev2sync.step;

import com.alibaba.fastjson.JSONObject;
import com.ld.clean.context.ApplicationCoreContextManager;
import com.ld.clean.dao.model.basecleandatatmp.ScSeEpProductFinancingV2Sync20240129;
import com.ld.clean.dao.repository.basecleandatatmp.ScSeEpProductFinancingV2Sync20240129Repository;
import com.ld.clean.datasteam.AsyncCleanDataSteam;
import com.ld.clean.datasteam.WindowCleanDataStream;
import com.ld.clean.job.productv2.finance.process.v2.CleanEpFinanceAgencyInfoFunction;
import com.ld.clean.model.tidbsearchsyncenterprise.EpProductFinancingV2Sync;
import com.ld.clean.repository.tidbsearchsyncenterprise.EpProductFinancingV2SyncRepository;
import com.ld.clean.step.SpiderParentStep;
import com.ld.clean.step.StepConstructorParam;
import com.ld.clean.test.financev2sync.async.CleanEpFinanceTestDescInfoFunction;
import com.ld.clean.test.financev2sync.async.FinanceV2TestCleanFeildAsync;
import com.ld.clean.test.financev2sync.so.JsonRootBean;
import org.apache.flink.api.common.functions.FlatMapFunction;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.windowing.time.Time;
import org.apache.flink.util.Collector;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021年10月19日 15点13分
 */
public class FinanceV2TestSpiderStep extends SpiderParentStep<JsonRootBean, EpProductFinancingV2Sync> {
    //private static final ScSeEpProductFinancingV2Sync20240129Repository scSeEpProductFinancingV2Sync20240129Repository = ApplicationCoreContextManager.getInstance(ScSeEpProductFinancingV2Sync20240129Repository.class);

    private static final EpProductFinancingV2SyncRepository epProductFinancingV2SyncRepository = ApplicationCoreContextManager.getInstance(EpProductFinancingV2SyncRepository.class);

    private StepConstructorParam stepConstructorParam;

    public FinanceV2TestSpiderStep(StepConstructorParam stepConstructorParam) {
        this.stepConstructorParam = stepConstructorParam;
    }

    @Override
    protected DataStream<JsonRootBean> transformSpiderStringToObjectFlatMap(DataStream<String> input) {
        DataStream<JsonRootBean> dataStream = input.flatMap(new FlatMapFunction<String, JsonRootBean>() {
            @Override
            public void flatMap(String s, Collector<JsonRootBean> collector) throws Exception {

                collector.collect(JSONObject.parseObject(s, JsonRootBean.class));
            }
        });
        return dataStream;
    }

    @Override
    protected void saveSpiderToMongoDB(DataStream<JsonRootBean> input) {

    }

    @Override
    protected DataStream<EpProductFinancingV2Sync> cleanSpiderFieldFlatMap(DataStream<JsonRootBean> input) {
        DataStream<List<EpProductFinancingV2Sync>> agencyInfo = AsyncCleanDataSteam.orderedWait(input, new FinanceV2TestCleanFeildAsync(stepConstructorParam.getCorePoolSize()));

        return AsyncCleanDataSteam.orderedWait(agencyInfo, new CleanEpFinanceTestDescInfoFunction(stepConstructorParam.getCorePoolSize())).flatMap(new FlatMapFunction<List<EpProductFinancingV2Sync>, EpProductFinancingV2Sync>() {
            @Override
            public void flatMap(List<EpProductFinancingV2Sync> baseinfos, Collector<EpProductFinancingV2Sync> collector) throws Exception {
                baseinfos.forEach(collector::collect);
            }

        });
    }

    @Override
    protected DataStream<EpProductFinancingV2Sync> queryCompanyKeyNoFunction(DataStream<EpProductFinancingV2Sync> input) {
        return null;
    }

    @Override
    protected DataStream<EpProductFinancingV2Sync> queryPersonKeyNoFunction(DataStream<EpProductFinancingV2Sync> input) {
        return null;
    }

    @Override
    protected DataStream<EpProductFinancingV2Sync> mergeSpiderAndDapFunction(DataStream<EpProductFinancingV2Sync> input) {
        return null;
        //  return AsyncCleanDataSteam.unorderedWait(input, new BaseMergeDapAsync(stepConstructorParam.getCorePoolSize(), CompTrackMatchInfo.class));
    }

    @Override
    protected void cleanStep(DataStream<EpProductFinancingV2Sync> input) {

    }

    @Override
    protected void saveBaseDB(DataStream<EpProductFinancingV2Sync> input) {
        WindowCleanDataStream.toDataStreamList(input, Time.seconds(10L), 250).flatMap(new FlatMapFunction<List<EpProductFinancingV2Sync>, List<EpProductFinancingV2Sync>>() {
            @Override
            public void flatMap(List<EpProductFinancingV2Sync> compTrackMatchInfos, Collector<List<EpProductFinancingV2Sync>> collector) throws Exception {
                epProductFinancingV2SyncRepository.insertBatch(compTrackMatchInfos);
                collector.collect(compTrackMatchInfos);
            }
        });
    }
}
