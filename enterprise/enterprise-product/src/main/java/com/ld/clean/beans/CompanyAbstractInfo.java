package com.ld.clean.beans;

import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class CompanyAbstractInfo {
    private Integer dataStatus;
    private String companykey;
    private String logo;
    private String logoSource;
    private String content;
    private String contentSource;


    public CompanyAbstractInfo() {
    }

    public CompanyAbstractInfo(Integer dataStatus, String companykey, String logo, String content) {
        this.dataStatus = dataStatus;
        this.companykey = companykey;
        this.logo = logo;
        this.logoSource = "18";
        this.content = content;
        this.contentSource = "18";
    }

    public CompanyAbstractInfo(Integer dataStatus, String companykey) {
        this.dataStatus = dataStatus;
        this.companykey = companykey;
        this.logoSource = "18";
        this.contentSource = "18";
    }

}
