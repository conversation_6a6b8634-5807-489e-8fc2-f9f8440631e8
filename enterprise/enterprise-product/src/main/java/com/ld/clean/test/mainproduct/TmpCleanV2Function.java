package com.ld.clean.test.mainproduct;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.ld.clean.beans.Currency;
import com.ld.clean.beans.ParticipantEntity;
import com.ld.clean.constants.InvestTypeEnum;
import com.ld.clean.constants.ListSectionEnum;
import com.ld.clean.context.ApplicationCoreContextManager;
import com.ld.clean.dao.repository.manageenterprisedb.CompBrandPhaseInfoRepository;
import com.ld.clean.dto.company.QccCompanyOut;
import com.ld.clean.job.productv2.mergercompevents.entity.TransferInvestorDetailEntity;
import com.ld.clean.mapper.manageenterprisedb.CompVcpeInfoRepository;
import com.ld.clean.model.ASharesAndNewThirdBoardFinance;
import com.ld.clean.model.CompBrandFinancing;
import com.ld.clean.model.EpCtCurrency;
import com.ld.clean.model.manageenterprisedb.CompProductFinancing;
import com.ld.clean.model.manageenterprisedb.CompProductMergerEvents;
import com.ld.clean.model.manageenterprisedb.CompVcpeInfo;
import com.ld.clean.model.manageenterprisedb.ProductBaseinfo;
import com.ld.clean.model.tidbsearchsyncenterprise.EpProductFinancingV2Sync;
import com.ld.clean.parent.AsyncCleanParentFunction;
import com.ld.clean.repository.CompBrandFinancingRepository;
import com.ld.clean.repository.manageenterprisedb.CompMaEventCvsRepository;
import com.ld.clean.repository.manageenterprisedb.CompVcpeLabelBaseinfoRepository;
import com.ld.clean.repository.manageenterprisedb.ProductBaseinfoRepository;
import com.ld.clean.repository.searchsyncenterprise.EpProductBaseinfoSyncRepository;
import com.ld.clean.repository.searchsyncenterprise.EpProductFinancingSyncRepository;
import com.ld.clean.repository.tidbsearchsyncenterprise.EpProductFinancingV2SyncRepository;
import com.ld.clean.test.mainbrand.dao.ScSeCompBrandFinancing20240513;
import com.ld.clean.test.mainbrand.dao.finance.LpfSeEpProductFinancingV2Sync241011;
import com.ld.clean.test.mainbrand.dao.finance.LpfSeEpProductFinancingV2Sync241011Repository;
import com.ld.clean.util.CurrencyUtil;
import com.ld.clean.util.FinacingAmountUtil;
import com.ld.clean.util.FinacingValuationUtil;
import com.ld.clean.util.ct.CtKeynoUtil;
import com.ld.clean.utils.CtCurrencyUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import tk.mybatis.mapper.entity.Condition;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
public class TmpCleanV2Function extends AsyncCleanParentFunction<CompProductMergerEvents, LpfSeEpProductFinancingV2Sync241011> {

    private static CompVcpeInfoRepository compVcpeInfoRepository = ApplicationCoreContextManager.getInstance(CompVcpeInfoRepository.class);
    //private static final EpProductFinancingV2SyncRepository epProductFinancingV2SyncRepository = ApplicationCoreContextManager.getInstance(EpProductFinancingV2SyncRepository.class);
    private static CompBrandFinancingRepository compBrandFinancingRepository = ApplicationCoreContextManager.getInstance(CompBrandFinancingRepository.class);
    private static LpfSeEpProductFinancingV2Sync241011Repository epProductFinancingV2SyncRepository = ApplicationCoreContextManager.getInstance(LpfSeEpProductFinancingV2Sync241011Repository.class);


    public TmpCleanV2Function(int corePoolSize) {
        super(corePoolSize);
    }


    @Override
    protected LpfSeEpProductFinancingV2Sync241011 invoke(CompProductMergerEvents events) throws Exception {

        LpfSeEpProductFinancingV2Sync241011 ep = (LpfSeEpProductFinancingV2Sync241011)epProductFinancingV2SyncRepository.selectByPrimaryKey(events.getId());
        if(null != ep){
            String in = events.getInvestorDetails();
            String tr = events.getTransferorDetails();

            Set<String> participantSearchSet = new HashSet<>();
            List<ParticipantEntity> participantEntityList = new ArrayList<>();
            //投资方
            List<ParticipantEntity> invest = generateParticipant(in, 1);
            //分别排序
            sortInvest(invest);
            //出让方
            List<ParticipantEntity> transfer = generateParticipant(tr, 2);
            sortInvest(transfer);

             participantEntityList.addAll(invest);
            participantEntityList.addAll(transfer);
            //排序

            if (participantEntityList.isEmpty()) {
                ep.setParticipantDetails(JSON.toJSONString(participantEntityList));
                ep.setParticipantSearch("");
            } else {
                for (ParticipantEntity entity : participantEntityList) {
                    participantSearchSet.add(entity.getKeyNo());
                    participantSearchSet.add(entity.getName());
                    List<ParticipantEntity.RelationInfo> relationInfo = entity.getRelationInfo();
                    for (ParticipantEntity.RelationInfo relation : relationInfo) {
                        participantSearchSet.add(relation.getName());
                        participantSearchSet.add(relation.getKeyNo());
                    }
                }
                ep.setParticipantDetails(JSON.toJSONString(participantEntityList));
                if (!participantSearchSet.isEmpty()) {
                    ep.setParticipantSearch(StringUtils.join(participantSearchSet.stream().filter(StringUtils::isNotBlank).collect(Collectors.toSet()), ","));
                } else {
                    ep.setParticipantSearch("");
                }
            }


        }


        /*if(StringUtils.isNotBlank(valuation) && !"未披露".equals(valuation)){
            events.setValuation(FinacingAmountUtil.handleDimensionAmount(valuation,true));
        }else {
            events.setValuation("未披露");
            events.setValuationRmb(new BigDecimal("0.00"));
        }*/




        /*LpfSeEpProductFinancingV2Sync241011 v2Sync241011 = (LpfSeEpProductFinancingV2Sync241011) epProductFinancingV2SyncRepository.selectByPrimaryKey(events.getId());
        if(null != v2Sync241011){
            String valuation = events.getValuation();

            v2Sync241011.setValuationRmb(events.getValuationRmb());
            //量纲转换
            v2Sync241011.setValuation(FinacingAmountUtil.handleDimensionAmount(valuation,true));

        }*/



        /*if (null != v2Sync241011) {
            //并购 金额 和估值量纲转换
            String amount = events.getAmount();
            String valuation = events.getValuation();
            if (StringUtils.isNotBlank(amount) && !"未披露".equals(amount)) {
                v2Sync241011.setAmount(FinacingAmountUtil.handleDimensionAmount(amount,false));
            }
            if (StringUtils.isNotBlank(valuation) && !"未披露".equals(valuation)) {

                BigDecimal rm = CtCurrencyUtils.generateDecimalRmbByDesc(
                        CtCurrencyUtils.convertWithUnitAndCurrency(CtCurrencyUtils.getEpCtCurrencyByCapital(valuation), events.getFinanceDate(), 4), valuation);
                v2Sync241011.setValuationRmb(rm);
                if (null != rm) {
                    //转币种 估值转换
                    valuation = FinacingValuationUtil.handleValuation(valuation, events.getFinanceDate(), rm);
                }
                //量纲转换
                v2Sync241011.setValuation(FinacingAmountUtil.handleDimensionAmount(valuation,true));
            } else {
                v2Sync241011.setValuation("未披露");
                v2Sync241011.setValuationRmb(BigDecimal.valueOf(0.00).setScale(2, BigDecimal.ROUND_HALF_UP));
                System.out.println(v2Sync241011.getValuationRmb());
            }

        }*/


        //CompBrandFinancing brandFinancing = (CompBrandFinancing) compBrandFinancingRepository.selectByPrimaryKey(productFinancing.getId());

        //amount  和 valuation 量纲转换



        /*if (StringUtils.isNotBlank(events.getAmount()) && StringUtils.isNotBlank(events.getRate()) && !"未披露".equals(events.getAmount()) && !"0人民币".equals(events.getAmount())) {
            //相除  精确amout
            boolean flag = WORD_TO_REMOVE.stream()
                    .anyMatch(word -> events.getAmount().contains(word));
            if (!flag) {
                String valuation = FinacingAmountUtil.divideAmountByRate(events.getAmount(), events.getRate());
                events.setValuation(valuation.replace("元人民币","人民币"));
            }

        }else {
            events.setValuation("未披露");
        }*/


        return ep;
    }

    private List<ParticipantEntity> sortInvest(List<ParticipantEntity> list){
        if(CollectionUtils.isNotEmpty(list)){
            Collections.sort(list, new Comparator<ParticipantEntity>() {
                @Override
                public int compare(ParticipantEntity o1, ParticipantEntity o2) {
                    // 定义category的排序顺序
                    int[] order = {1, 2, 0, -1};
                    // 查找o1和o2的category在排序顺序中的位置
                    int index1 = -1, index2 = -1;
                    for (int i = 0; i < order.length; i++) {
                        if (order[i] == o1.getCategory()) index1 = i;
                        if (order[i] == o2.getCategory()) index2 = i;
                    }
                    // 根据位置进行比较
                    return Integer.compare(index1, index2);
                }
            });

        }
        return list;
    }

    private List<ParticipantEntity> generateParticipant(String investor, Integer mergerType) {

        List<ParticipantEntity> addParticipantList = new ArrayList<>();

        List<TransferInvestorDetailEntity> mergerDetails = JSONObject.parseArray(investor, TransferInvestorDetailEntity.class);
        if (CollectionUtils.isNotEmpty(mergerDetails)) {
            Set<String> keynoSet = mergerDetails.stream().map(TransferInvestorDetailEntity::getKeyno).filter(StringUtils::isNotBlank).collect(Collectors.toSet());
            Map<String, Integer> vcpeTypeMap = new HashMap<>();
            if (CollectionUtils.isNotEmpty(keynoSet)) {
                // vcpe type 1是私募基金 2是管理人
                List<CompVcpeInfo> vcpeList = compVcpeInfoRepository.getVcpeListByKeynoList(new ArrayList<>(keynoSet), Arrays.asList("1", "2"));

                vcpeList.forEach(x -> {
                    Integer type = vcpeTypeMap.get(x.getCompKeyno());
                    Integer newType = Integer.valueOf(x.getVcpeType());
                    if (null == type) {
                        vcpeTypeMap.put(x.getCompKeyno(), newType);
                    } else {
                        if (newType < type) {
                            vcpeTypeMap.put(x.getCompKeyno(), newType);
                        }
                    }
                });
            }
            for (TransferInvestorDetailEntity entity : mergerDetails) {
                ParticipantEntity participant = new ParticipantEntity();
                participant.setParticipantType(mergerType);
                participant.setCategory(entity.getCategory());
                participant.setKeyNo(entity.getKeyno());
                participant.setName(entity.getName());
                participant.setOrg(entity.getOrg());
                participant.setType(2);
                participant.setAmount(entity.getAmount());
                participant.setRate(entity.getRate());
                participant.setIdentity(vcpeTypeMap.get(entity.getKeyno()) == null ?
                        StringUtils.isBlank(entity.getKeyno()) ? 0 : 3 : vcpeTypeMap.get(entity.getKeyno()));
                if (participant.getCategory() == InvestTypeEnum.COMPANY.getCode()) {
                    ParticipantEntity.RelationInfo relationInfo = new ParticipantEntity.RelationInfo();
                    if(StringUtils.isNotBlank(entity.getInvestName()) || StringUtils.isNotBlank(entity.getInvestId())){
                        relationInfo.setOrg(13);
                        relationInfo.setName(entity.getInvestName());
                        relationInfo.setKeyNo(entity.getInvestId());
                        participant.setRelationInfo(Collections.singletonList(relationInfo));
                    }
                }
                addParticipantList.add(participant);
            }
        }

        return addParticipantList;
    }

    public static final List<String> WORD_TO_REMOVE = Arrays.asList("约", "不超过", "超", "数");

    private static Map<Integer, BigDecimal> unitMap = new HashMap<Integer, BigDecimal>() {{


        put(2, new BigDecimal(1000L));
        put(3, new BigDecimal(10000L));

        //临时优化处理
        put(10, new BigDecimal(100000L));

        put(20, new BigDecimal(1000000000L));
        put(21, new BigDecimal(10000000000L));
        put(22, new BigDecimal(100000000000L));

        put(4, new BigDecimal(1000000L));
        put(5, new BigDecimal(10000000L));
        put(6, new BigDecimal(100000000L));
        put(7, new BigDecimal(1000000000000L));
    }};

}