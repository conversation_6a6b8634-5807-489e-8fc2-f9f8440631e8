package com.ld.clean.constants;

import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

/**
 * 定增投资方的类型
 *
 * <AUTHOR>
 */
@Getter
public enum AddIssueInvestTypeEnum {

    UNKNOWN("", "未披露", -1),
    COMMON_OPERATOR("一般法人", "企业", 2),
    PERSON("个人", "个人", 0),
    FUND_MANAGE_COMP("基金管理公司", "企业", 2),
    SECURITIES_INVESTMENT_FUNDS("证券投资基金", "企业", 2),
    BROKER("券商", "企业", 2),
    QFII("QFII", "企业", 2),
    ENTERPRISE_ANNUITY("企业年金", "产品", 3),
    INSURANCE_ACCOUNT("保险账号", "产品", 3),
    TRUST_PRODUCTS("信托产品", "产品", 3),
    SOCIAL_SECURITY_FUND("社保基金", "企业", 2),
    TRUST_COMPANY("信托公司", "企业", 2),
    FINANCE_COMPANY("财务公司", "企业", 2),
    BROKER_SECURITIES("券商集合理财", "产品", 3),
    OVERSEAS_INSTITUTIONS("境外机构或个人", "个人", 0),
    ;

    private String desc;
    private String type;

    private Integer code;

    AddIssueInvestTypeEnum(String desc, String type, Integer code) {
        this.desc = desc;
        this.type = type;
        this.code = code;
    }

    public static AddIssueInvestTypeEnum getByCode(int code) {
        for (AddIssueInvestTypeEnum roundLevelEnum : AddIssueInvestTypeEnum.values()) {
            if (code == roundLevelEnum.getCode()) {
                return roundLevelEnum;
            }
        }
        return UNKNOWN;
    }


    public static AddIssueInvestTypeEnum getByDesc(String desc) {
        if (StringUtils.isNotBlank(desc)) {
            for (AddIssueInvestTypeEnum roundLevelEnum : AddIssueInvestTypeEnum.values()) {
                if (desc.equals(roundLevelEnum.getDesc())) {
                    return roundLevelEnum;
                }
            }
        }
        return UNKNOWN;
    }

}

