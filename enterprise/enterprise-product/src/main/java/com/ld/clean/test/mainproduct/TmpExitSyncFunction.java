package com.ld.clean.test.mainproduct;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.ld.clean.context.ApplicationCoreContextManager;
import com.ld.clean.job.productv2.mergercompevents.entity.MergerDetailEntity;
import com.ld.clean.job.productv2.mergercompevents.entity.TransferInvestorDetailEntity;
import com.ld.clean.model.manageenterprisedb.CompProductMergerEvents;
import com.ld.clean.model.manageenterprisedb.ProductBaseinfo;
import com.ld.clean.parent.AsyncCleanParentFunction;
import com.ld.clean.repository.manageenterprisedb.ProductBaseinfoRepository;
import com.ld.clean.test.mainbrand.dao.mergerexit.LpfMeIpoExitBaseinfo250208;
import com.ld.clean.test.mainbrand.dao.mergerexit.LpfMeIpoExitBaseinfo250208Repository;
import com.ld.clean.utils.CtCompanyUtils;
import com.ld.clean.utils.CtCurrencyUtils;
import com.ld.clean.utils.MD5Util;
import org.apache.commons.lang3.StringUtils;
import tk.mybatis.mapper.entity.Condition;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * @description：TODO
 * @author： liupf
 * @create： 2025/2/10 14:45
 */
public class TmpExitSyncFunction extends AsyncCleanParentFunction<CompProductMergerEvents, List<LpfMeIpoExitBaseinfo250208>> {
    private static final ProductBaseinfoRepository productBaseinfoRepository = ApplicationCoreContextManager.getInstance(ProductBaseinfoRepository.class);
    private static final LpfMeIpoExitBaseinfo250208Repository ipoExitBaseinfoRepository = ApplicationCoreContextManager.getInstance(LpfMeIpoExitBaseinfo250208Repository.class);

    public TmpExitSyncFunction(int corePoolSize) {
        super(corePoolSize);
    }

    @Override
    protected List<LpfMeIpoExitBaseinfo250208> invoke(CompProductMergerEvents mergerEvents) throws Exception {
        List<LpfMeIpoExitBaseinfo250208> result = new ArrayList<>();
        if (null == mergerEvents) {
            return result;
        }
        //过滤
        if (!("股权转让".equals(mergerEvents.getRound()) || "并购".equals(mergerEvents.getRound()))) {
            return null;
        }
        if (mergerEvents.getLastSchedule() != 2) {
            return null;
        }
        if (StringUtils.isBlank(mergerEvents.getTransferorDetails()) || "[]".equals(mergerEvents.getTransferorDetails())) {
            return null;
        }
        if (StringUtils.isBlank(mergerEvents.getProductId())) {
            return null;
        }

        //查询项目基础表
        ProductBaseinfo product = (ProductBaseinfo) productBaseinfoRepository.selectByPrimaryKey(mergerEvents.getProductId());
        if (null == product) {
            return null;
        }

        String trans = mergerEvents.getTransferorDetails();
        List<TransferInvestorDetailEntity> detailList = JSON.parseArray(trans, TransferInvestorDetailEntity.class);
        for (TransferInvestorDetailEntity investorDetail : detailList) {
            //只获取个人和企业
            if (investorDetail.getCategory() == 2 || investorDetail.getCategory() == 0) {
                LpfMeIpoExitBaseinfo250208 exit = new LpfMeIpoExitBaseinfo250208();
                exit.setProductId(mergerEvents.getProductId());
                exit.setDataStatus(product.getDataStatus());
                exit.setSymbol(product.getSymbol());
                exit.setCompName(product.getCompanyName());
                exit.setCompKeyno(product.getCompanyId());
                exit.setAmountStandard(investorDetail.getAmount());
                exit.setListDate(mergerEvents.getFinanceDate());
                exit.setHolderKeyno(investorDetail.getKeyno());
                exit.setHolderName(investorDetail.getName());
                String compName = CtCompanyUtils.getCompanyName(investorDetail.getKeyno(), true);
                if (StringUtils.isNotBlank(compName)) {
                    exit.setHolderName(compName.replace("(", "（").replace(")", "）"));
                }

                if (investorDetail.getCategory() == 2) {
                    exit.setHolderOrgType(1);
                } else {
                    exit.setHolderOrgType(2);
                }
                exit.setHolderInvestId(investorDetail.getInvestId());
                exit.setHolderInvestName(investorDetail.getInvestName());
                String inType = investorDetail.getInType();
                exit.setHolderInstitutionType(null);
                if(StringUtils.isNotBlank(inType) && inType.contains("VC/PE")){
                    exit.setHolderInstitutionType(10);
                }
                String amount = investorDetail.getAmount();
                exit.setAmountStandard(amount);
                if (StringUtils.isNotBlank(amount)) {
                    BigDecimal rm = CtCurrencyUtils.generateDecimalRmbByDesc(
                            CtCurrencyUtils.convertWithUnitAndCurrency(CtCurrencyUtils.getEpCtCurrencyByCapital(amount), exit.getListDate(), 4), amount);
                    exit.setAmount(rm);
                }
                String rate = investorDetail.getRate();
                if (null != rate) {
                    exit.setSumOfBeforeSharesRatio(rate.replace("%", "").replace("％", ""));
                }

                List<MergerDetailEntity> mergerDetails = JSON.parseArray(mergerEvents.getMergerDetails(), MergerDetailEntity.class);

                if (null != mergerDetails && !mergerDetails.isEmpty()) {
                    //获取最后一个对象 time最大的对象
                    Optional<MergerDetailEntity> maxTimeEntity = mergerDetails.stream()
                            .filter(entity -> entity.getTime() != null)
                            .max(Comparator.comparing(entity -> entity.getTime() != null ? entity.getTime() : ""));
                    if (maxTimeEntity.isPresent()) {
                        MergerDetailEntity maxEntity = maxTimeEntity.get();
                        if (StringUtils.isNotBlank(maxEntity.getLink())) {
                            exit.setListingAnnounceLink(maxEntity.getLink());
                        }
                    }
                }
                exit.setSpiderId(mergerEvents.getId());
                exit.setExitType(3);
                if ("股权转让".equals(mergerEvents.getRound())) {
                    exit.setExitType(2);
                }
                exit.setId(MD5Util.encode(exit.getSymbol() + exit.getHolderName() + exit.getListDate() + exit.getProductId()));
                result.add(exit);
            }
        }
        if (CollectionUtil.isNotEmpty(result)) {
            List<String> spiderIds = result.stream()
                    .map(LpfMeIpoExitBaseinfo250208::getSpiderId)
                    .distinct().collect(Collectors.toList());
            if (CollectionUtil.isEmpty(spiderIds)) {
                return result;
            }
            Condition c = new Condition(LpfMeIpoExitBaseinfo250208.class);
            c.createCriteria().andIn("spiderId", spiderIds).andEqualTo("dataStatus", 1);
            List<LpfMeIpoExitBaseinfo250208> ipoExitBaseinfos = ipoExitBaseinfoRepository.selectByCondition(c);

            ipoExitBaseinfos.forEach(itt -> {
                if (result.stream().noneMatch(vb -> vb.getId().equals(itt.getId()))) {
                    itt.setDataStatus(3);
                    result.add(itt);
                }
            });
        }
        return result;
    }
}
