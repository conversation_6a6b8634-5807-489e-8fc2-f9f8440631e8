package com.ld.clean.test.mainbrand;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.shaded.com.google.common.reflect.TypeToken;
import com.alibaba.nacos.shaded.com.google.gson.Gson;
import com.base.clean.utils.BaseStringUtil;
import com.ld.clean.beans.Currency;
import com.ld.clean.beans.InvestEntity;
import com.ld.clean.constants.MyConstants;
import com.ld.clean.context.ApplicationCoreContextManager;
import com.ld.clean.job.productv2.finance.entity.BrandFinanceBO;
import com.ld.clean.model.CompBrandFinancing;
import com.ld.clean.model.baseenterprisedb.CompFinanceRoundMapping;
import com.ld.clean.model.manageenterprisedb.ProductBaseinfo;
import com.ld.clean.model.tidbsearchsyncenterprise.EpProductFinancingV2Sync;
import com.ld.clean.parent.AsyncCleanParentFunction;
import com.ld.clean.pojo.bo.NewsOss;
import com.ld.clean.repository.CompBrandFinancingRepository;
import com.ld.clean.repository.manageenterprisedb.ProductBaseinfoRepository;
import com.ld.clean.repository.tidbsearchsyncenterprise.EpProductFinancingV2SyncRepository;
import com.ld.clean.test.mainbrand.dao.InvestEntityTest;
import com.ld.clean.test.mainbrand.dao.ScSeCompBrandFinancing20240513;
import com.ld.clean.test.mainbrand.dao.ScSeCompBrandFinancing20240513Repository;
import com.ld.clean.test.mainbrand.dao.finance.LpfMeASharesAndNewThirdBoardFinance240919;
import com.ld.clean.util.*;
import com.ld.clean.utils.DingDingUtil;
import org.apache.commons.lang3.StringUtils;
import tk.mybatis.mapper.entity.Condition;

import java.lang.reflect.Type;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
public class TestSyncDapCompBrandFinanceFunction extends AsyncCleanParentFunction<LpfMeASharesAndNewThirdBoardFinance240919, ScSeCompBrandFinancing20240513> {

  /*  private static ProductBaseinfoRepository productBaseinfoRepository = ApplicationCoreContextManager.getInstance(ProductBaseinfoRepository.class);
    private static CompBrandFinancingRepository compBrandFinancingRepository = ApplicationCoreContextManager.getInstance(CompBrandFinancingRepository.class);
*/
  private static ScSeCompBrandFinancing20240513Repository compBrandFinancingRepository = ApplicationCoreContextManager.getInstance(ScSeCompBrandFinancing20240513Repository.class);
    private static final EpProductFinancingV2SyncRepository epProductFinancingV2SyncRepository = ApplicationCoreContextManager.getInstance(EpProductFinancingV2SyncRepository.class);

    public TestSyncDapCompBrandFinanceFunction(int corePoolSize) {
        super(corePoolSize);
    }

    @Override
    protected ScSeCompBrandFinancing20240513 invoke(LpfMeASharesAndNewThirdBoardFinance240919 share) throws Exception {
       //无效采编录入的数据
        String symbol = share.getSymbol();
        String round = share.getRound();
        String exchange = share.getExchange();
        //查询基础表
        Condition condition = new Condition(EpProductFinancingV2Sync.class);
        condition.createCriteria().andEqualTo("companyKeyno", symbol).andEqualTo("dataStatus", 1);
        List<EpProductFinancingV2Sync> list = epProductFinancingV2SyncRepository.selectByCondition(condition);





        return null;



       /* if (StringUtils.isNotBlank(pf.getProductId())) {



            ProductBaseinfo product = (ProductBaseinfo) productBaseinfoRepository.selectByPrimaryKey(pf.getProductId());
            if (null != product) {
                String financeId = pf.getId();
                String newsOriginalUrl = pf.getNewsOriginalUrl();
                String newsTitle = pf.getNewsTitle();
                CompBrandFinancing brandFinancing = (CompBrandFinancing) compBrandFinancingRepository.selectByPrimaryKey(financeId);
                if (null != brandFinancing) {
                    // 如果新闻原始链接发生变化或标题发生变化，则内链置为空，重新转换生成内链
                    if (!StringUtils.equalsIgnoreCase(brandFinancing.getNewsOriginalUrl(), newsOriginalUrl)
                            || !StringUtils.equalsIgnoreCase(brandFinancing.getNewsTitle(), newsTitle)) {
                        brandFinancing.setNewsInternalUrl("");
                    }
                    if (pf.getDataStatus() != 1) {
                        brandFinancing.setDataStatus(pf.getDataStatus());
                        return brandFinancing;
                    }
                } else {
                    brandFinancing = new CompBrandFinancing();
                    if (pf.getDataStatus() != 1) {
                        return null;
                    }
                }
                brandFinancing.setId(financeId);
                brandFinancing.setDataStatus(pf.getDataStatus());
                brandFinancing.setBrandId(product.getId());
                brandFinancing.setBrandName(product.getName());
                brandFinancing.setFinanceDate(pf.getFinanceDate());

                brandFinancing.setPurpose(pf.getPurpose());
                brandFinancing.setEquityRatio(pf.getEquityRatio());

                String round = pf.getRound();
                if (StringUtils.isNotBlank(pf.getProcessRounds())) {
                    // 人工维护轮次优先于常规轮次
                    round = pf.getProcessRounds();
                }
                brandFinancing.setRound(BaseStringUtil.cleanStr(round));

                String standardRound = FinanceRoundUtil.getStandardRound(round);
                if (StringUtils.isNotBlank(standardRound)) {
                    brandFinancing.setStandardRound(standardRound);
                } else {
                    // 因轮次状态置为待审核
                    brandFinancing.setDataStatus(6);

                    StringBuffer sb = new StringBuffer();
                    sb.append("【融资轮次未在轮次映射表】").append("\n")
                            .append("未生效融资产品id").append(pf.getProductId()).append("\n")
                            .append("未生效融资轮次").append(round).append("\n")
                            .append("————————————————————————————").append("\n")
                            .append("请核实");
                    DingDingUtil.send(sb.toString(), new ArrayList<>(), MyConstants.DING_TALK_DUOLICATE_PRODUCT_ACCESS_TOKEN);
                }

                brandFinancing.setAdvisor(pf.getAdvisor());
                brandFinancing.setChangeMarket(pf.getChangeMarket());

                brandFinancing.setNewsTitle(newsTitle);
                if (StringUtils.isBlank(newsTitle)) {
                    brandFinancing.setNewsInternalUrl("");
                }

                brandFinancing.setNewsDate(pf.getNewsDate());
                brandFinancing.setNewsOriginalUrl(newsOriginalUrl);
                brandFinancing.setNewsLinks(pf.getNewsLinks());
                brandFinancing.setAmountHide(pf.getAmountHide());
                brandFinancing.setValuationHide(pf.getValuationHide());
                brandFinancing.setSourceType(pf.getSourceType());

                brandFinancing.setFormatRound(RoundUtil.getFormatRound(round));

                if (StringUtils.isNotBlank(newsOriginalUrl) && StringUtils.isNotBlank(newsTitle)) {
                    //brandFinancing.setNewsOssId(NewOSSUtil.getCtNewsIdByOriginalUrl(newsOriginalUrl));
                    // brandFinancing.setNewsSource(NewOSSUtil.getDomain(brandFinancing.getNewsOriginalUrl()));
                    brandFinancing.setNewsOssId("");
                    brandFinancing.setNewsSource("");
                    if (StringUtils.isNotBlank(brandFinancing.getNewsInternalUrl())) {
                        String newsId = NewOSSUtil.getNewsIdByInternalUrl(brandFinancing.getNewsInternalUrl());
                        NewsOss newsOss = NewOSSUtil.getNewsOss(newsId);
                        if (null != newsOss) {
                            brandFinancing.setNewsOssId(newsId);
                            brandFinancing.setNewsSource(NewOSSUtil.formatSource(newsOss.getSource()));
                        }
                    }
                } else {
                    brandFinancing.setNewsOssId("");
                    brandFinancing.setNewsSource("");
                    brandFinancing.setNewsInternalUrl("");
                    brandFinancing.setNewsOriginalUrl("");
                    brandFinancing.setNewsTitle("");
                }

                if (pf.getNewsOssidValid() != null && pf.getNewsOssidValid() == 1) {
                    brandFinancing.setNewsOssId("");
                    brandFinancing.setNewsSource("");
                }

                Currency currencyAmount = CurrencyUtil.getCurrenyByMoney(pf.getAmount(), pf.getAmountHide(), pf.getFinanceDate());
                brandFinancing.setAmountRmb(currencyAmount.getAmountDecimalRmb());
                brandFinancing.setAmount(currencyAmount.getAmount());

                Currency currencyValuation = CurrencyUtil.getCurrenyByMoney(pf.getValuation(), pf.getValuationHide(), pf.getFinanceDate());
                brandFinancing.setValuationRmb(currencyValuation.getAmountDecimalRmb());
                brandFinancing.setValuation(currencyValuation.getAmount());

                Set<InvestEntity> leadInvestSet = new HashSet<>();
                Set<InvestEntity> followInvestSet = new HashSet<>();
                leadInvestSet.addAll(pf.getLeadInstitutionEntity());
                leadInvestSet.addAll(pf.getLeadCompanyEntity());
                leadInvestSet.addAll(pf.getLeadPersonEntity());
                leadInvestSet.addAll(pf.getLeadAssignInvestEntity());
                followInvestSet.addAll(pf.getFollowInstitutionEntity());
                followInvestSet.addAll(pf.getFollowCompanyEntity());
                followInvestSet.addAll(pf.getFollowPersonEntity());
                followInvestSet.addAll(pf.getFollowAssignInvestEntity());

                Set<InvestEntity> removeLeadSet = new HashSet<>();
                Set<InvestEntity> removeFollowSet = new HashSet<>();
                Set<InvestEntity> newFollowSet = new HashSet<>();
                //去重逻辑
                for (InvestEntity followInvest : followInvestSet) {
                    for (InvestEntity leadInvest : leadInvestSet) {
                        if (StringUtils.isNotBlank(followInvest.getId())) {

                            if (followInvest.getId().equals(leadInvest.getId())) {
                                InvestEntity entity = new InvestEntity();

                                if (followInvest.getCategory() == leadInvest.getCategory()) {
                                    //id和category 相同
                                    if (followInvest.getCategory() == 1) {
                                        removeFollowSet.add(followInvest);
                                    } else {
                                        if (StringUtils.isNotBlank(followInvest.getKeyNo()) && StringUtils.isNotBlank(leadInvest.getKeyNo())) {
                                            String followInvestKeyNo = followInvest.getKeyNo();
                                            String leadInvestKeyNo = leadInvest.getKeyNo();
                                            if (followInvestKeyNo.equals(leadInvestKeyNo)) {
                                                removeFollowSet.add(followInvest);
                                            }
                                        }
                                    }
                                } else {
                                    if (followInvest.getCategory() == 2 && leadInvest.getCategory() == 1) {
                                        entity.setId(followInvest.getId());
                                        entity.setName(followInvest.getName());
                                        entity.setType(1);
                                        entity.setKeyNo(followInvest.getKeyNo());
                                        entity.setOriginalName(followInvest.getOriginalName());
                                        entity.setOrg(followInvest.getOrg());
                                        entity.setCategory(2);
                                        removeLeadSet.add(leadInvest);
                                        removeFollowSet.add(followInvest);
                                        newFollowSet.add(entity);
                                    } else if (followInvest.getCategory() == 1 && leadInvest.getCategory() == 2) {
                                        entity.setId(leadInvest.getId());
                                        entity.setName(leadInvest.getName());
                                        entity.setType(1);
                                        entity.setKeyNo(leadInvest.getKeyNo());
                                        entity.setOriginalName(leadInvest.getOriginalName());
                                        entity.setOrg(leadInvest.getOrg());
                                        entity.setCategory(2);
                                        removeLeadSet.add(leadInvest);
                                        removeFollowSet.add(followInvest);
                                        newFollowSet.add(entity);
                                    }
                                }
                            }
                        } else if (StringUtils.isNotBlank(followInvest.getKeyNo())) {
                            //企业-企业
                            if (followInvest.getKeyNo().equals(leadInvest.getKeyNo())) {
                                removeFollowSet.add(followInvest);
                            }
                        }
                    }
                }
                leadInvestSet.removeAll(removeLeadSet);
                followInvestSet.removeAll(removeFollowSet);
                leadInvestSet.addAll(newFollowSet);
                //单独处理领投  跟投

                Map<String, Set<InvestEntity>> resultInvestMap = leadInvestSet.stream()
                        .collect(Collectors.groupingBy(InvestEntity::getName, Collectors.toSet()));
                Map<String, Set<InvestEntity>> resultFollowMap = followInvestSet.stream()
                        .collect(Collectors.groupingBy(InvestEntity::getName, Collectors.toSet()));
                for (Map.Entry<String, Set<InvestEntity>> entry : resultInvestMap.entrySet()) {
                    Set<InvestEntity> investEntities = entry.getValue();
                    InvestEntity result;
                    if (investEntities.size() > 1) {
                        //类型不相同或者相同 拿出公司数据
                        result = investEntities.stream()
                                .filter(entity -> entity.getCategory() == 2)
                                .findFirst()
                                .orElse(null);
                        if (ObjectUtil.isEmpty(result)) {
                            result = investEntities.stream()
                                    .filter(entity -> entity.getCategory() == 1)
                                    .findFirst()
                                    .orElse(null);
                        }
                        if (ObjectUtil.isNotEmpty(result)) {
                            leadInvestSet.removeAll(investEntities);
                            leadInvestSet.add(result);
                        }

                    }
                }
                for (Map.Entry<String, Set<InvestEntity>> entry : resultFollowMap.entrySet()) {
                    Set<InvestEntity> investEntities = entry.getValue();
                    InvestEntity result;
                    if (investEntities.size() > 1) {
                        //类型不相同或者相同 拿出公司数据
                        result = investEntities.stream()
                                .filter(entity -> entity.getCategory() == 2)
                                .findFirst()
                                .orElse(null);
                        if (ObjectUtil.isEmpty(result)) {
                            result = investEntities.stream()
                                    .filter(entity -> entity.getCategory() == 1)
                                    .findFirst()
                                    .orElse(null);
                        }
                        if (ObjectUtil.isNotEmpty(result)) {
                            followInvestSet.removeAll(investEntities);
                            followInvestSet.add(result);
                        }

                    }
                }
                brandFinancing.setLeadInvestor(JSONObject.toJSONString(FunderInvestOsUtil.sortInvestment(leadInvestSet)));
                brandFinancing.setFollowInvestor(JSONObject.toJSONString(FunderInvestOsUtil.sortInvestment(followInvestSet)));


                return brandFinancing;
            }
        }*/

    }

    private Set<InvestEntityTest> handleInvest(Set<InvestEntityTest> investEntityTests){
        Map<String, Set<InvestEntityTest>> resultFollowMap = investEntityTests.stream()
                .filter(entity -> entity.getCategory() == 1 || entity.getCategory() == 2)
                .collect(Collectors.groupingBy(InvestEntityTest::getName, Collectors.toSet()));
        for (Map.Entry<String, Set<InvestEntityTest>> entry : resultFollowMap.entrySet()) {
            Set<InvestEntityTest> investEntities = entry.getValue();
            if (investEntities.size() > 1) {
                //存在情况 机构和公司  全部是机构  全部是公司
                boolean allCategoryOne = investEntities.stream()
                        .allMatch(entity -> entity.getCategory() == 1);
                boolean allCategoryTwo = investEntities.stream()
                        .allMatch(entity -> entity.getCategory() == 2);
                //都为1随机取一条
                if(allCategoryOne){
                    List<InvestEntityTest> entityList = new ArrayList<>(investEntities);
                    InvestEntityTest one = entityList.get(0);
                    investEntityTests.removeAll(investEntities);
                    investEntityTests.add(one);
                }
                //都为公司需判断keyNo 去重
                if(allCategoryTwo){
                    Map<String, Set<InvestEntityTest>> keyNoMap = investEntities.stream().collect(Collectors.groupingBy(InvestEntityTest::getKeyNo, Collectors.toSet()));
                    if(keyNoMap.size() != investEntities.size()){
                        for(Map.Entry<String, Set<InvestEntityTest>> keyNoEntry : keyNoMap.entrySet()){
                            Set<InvestEntityTest> entryValue = keyNoEntry.getValue();
                            if(entryValue.size() >1){
                                //只获取一条
                                List<InvestEntityTest> entityList = new ArrayList<>(entryValue);
                                InvestEntityTest one = entityList.get(0);
                                investEntityTests.removeAll(entryValue);
                                investEntityTests.add(one);
                            }
                        }
                    }

                }
                //1 2 都存在 则去掉1
                if(!allCategoryOne){
                    Set<InvestEntityTest> filteredSet = investEntities.stream()
                            .filter(entity -> entity.getCategory() == 1)
                            .collect(Collectors.toSet());
                    investEntityTests.removeAll(filteredSet);
                    //keyNo??

                }
            }
        }
        return investEntityTests;
    }



}