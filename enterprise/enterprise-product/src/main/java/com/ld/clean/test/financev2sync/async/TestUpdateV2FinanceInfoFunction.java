package com.ld.clean.test.financev2sync.async;


import com.ld.clean.context.ApplicationCoreContextManager;
import com.ld.clean.model.CompBrandFinancing;
import com.ld.clean.model.tidbsearchsyncenterprise.EpProductFinancingV2Sync;
import com.ld.clean.parent.AsyncCleanParentFunction;
import com.ld.clean.repository.tidbsearchsyncenterprise.EpProductFinancingV2SyncRepository;
import com.ld.clean.test.financev2sync.so.JsonRootBean;
import com.ld.clean.util.FinacingAmountUtil;
import com.ld.clean.util.FinacingValuationUtil;
import com.ld.clean.utils.CtCurrencyUtils;
import com.ld.clean.utils.DateUtil;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;


/**
 * <AUTHOR>
 * @date 2021年9月9日 17点27分
 */
public class TestUpdateV2FinanceInfoFunction extends AsyncCleanParentFunction<CompBrandFinancing, List<EpProductFinancingV2Sync>> {

   /* private static final CompBrandFinancingRepository compBrandFinancingRepository = ApplicationCoreContextManager.getInstance(CompBrandFinancingRepository.class);
    private static final CompVcpeInfoRepository compVcpeInfoRepository = ApplicationCoreContextManager.getInstance(CompVcpeInfoRepository.class);
    private static PlaceFinanceRepository placeFinanceRepository = ApplicationCoreContextManager.getInstance(PlaceFinanceRepository.class);
    private static EpProductFinancingV2SyncRepository epProductFinancingV2SyncRepository = ApplicationCoreContextManager.getInstance(EpProductFinancingV2SyncRepository.class);*/

    private static EpProductFinancingV2SyncRepository epProductFinancingV2SyncRepository = ApplicationCoreContextManager.getInstance(EpProductFinancingV2SyncRepository.class);

    public TestUpdateV2FinanceInfoFunction(int corePoolSize) {
        super(corePoolSize);
    }

    @Override
    protected List<EpProductFinancingV2Sync> invoke(CompBrandFinancing compBrandFinancing) throws Exception {


        EpProductFinancingV2Sync v2 = (EpProductFinancingV2Sync)epProductFinancingV2SyncRepository.selectByPrimaryKey(compBrandFinancing.getId());
        v2.setRate(compBrandFinancing.getEquityRatio());
        epProductFinancingV2SyncRepository.insertBatch(Collections.singletonList(v2));
        return Collections.singletonList(v2);
    }

}
