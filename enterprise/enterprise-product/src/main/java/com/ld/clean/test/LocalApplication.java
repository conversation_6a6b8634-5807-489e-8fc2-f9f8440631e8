package com.ld.clean.test;

import com.ld.clean.context.ApplicationCoreContextManager;
import com.ld.clean.datasteam.WindowCleanDataStream;
import com.ld.clean.kafka.CleanFlinkKafkaConsumer;
import com.ld.clean.kafka.KafkaHelper;
import com.ld.clean.model.searchsyncenterprise.EpProductBaseinfoSync;
import com.ld.clean.repository.searchsyncenterprise.EpProductBaseinfoSyncRepository;
import com.ld.clean.util.IntroUtil;
import com.ld.clean.utils.CheckPointUtil;
import com.ld.clean.utils.ExecutionEnvUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.api.common.functions.FlatMapFunction;
import org.apache.flink.api.java.utils.ParameterTool;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.flink.streaming.api.windowing.time.Time;
import org.apache.flink.streaming.connectors.kafka.FlinkKafkaConsumer;
import org.apache.flink.util.Collector;

import java.util.List;

/**
 * 爬虫数据字段清洗
 *
 * <AUTHOR>
 */
@Slf4j
public class LocalApplication {

    private static final EpProductBaseinfoSyncRepository epProductBaseinfoSyncRepository = ApplicationCoreContextManager.getInstance(EpProductBaseinfoSyncRepository.class);


    public static void main(String[] args) throws Exception {
        ParameterTool parameterTool = ExecutionEnvUtil.PARAMETER_TOOL;
        StreamExecutionEnvironment env = CheckPointUtil.setCheckpointConfig(ExecutionEnvUtil.prepare(parameterTool), parameterTool);
        ParameterTool dmpParams = ParameterTool.fromArgs(args);
        int spiderProcessParallelism = 2;
        env.setParallelism(spiderProcessParallelism);
        env.disableOperatorChaining();
        String topic = "base_investment_institutions_new_test";
        String group = "group_base_new_spider";
//        String topic = TopicConstants.TOPIC_BASE_COMPANY_PARTNERS_CHANGE;
//        String group = TopicConstants.GROUP_BASE_COMPANY_PARTNERS_CHANGE_LUJF_V2;

        FlinkKafkaConsumer<String> consumer = KafkaHelper.buildAuthKafkaSource(CleanFlinkKafkaConsumer.builder()
                .topic(topic)
                .groupId(group).build());

//        Map<KafkaTopicPartition, Long> specificStartupOffsets = new HashMap<>();
//        specificStartupOffsets.put(new KafkaTopicPartition(topic, 0), 4084521L);
//        consumer.setStartFromSpecificOffsets(specificStartupOffsets);
     //   consumer.setStartFromLatest();

        DataStream<String> originDataStream = env.addSource(consumer.setStartFromTimestamp(System.currentTimeMillis() -1000 * 1800));
        WindowCleanDataStream.toDataStreamList(originDataStream, Time.seconds(5L), 50).flatMap(new FlatMapFunction<List<String>, List<EpProductBaseinfoSync>>() {
            @Override
            public void flatMap(List<String> stringList, Collector<List<EpProductBaseinfoSync>> collector) throws Exception {
                List<EpProductBaseinfoSync> syncs = epProductBaseinfoSyncRepository.selectByPrimaryKeyList(stringList);

                syncs.forEach(it->{
                    if(StringUtils.isNotBlank(it.getIntro())){
                        it.setIntro(IntroUtil.getIntro(it.getIntro(),it.getCompanyName(),it.getCompanyKeyno(),1));
                    }
                });
                epProductBaseinfoSyncRepository.insertBatch(syncs);

            }
        });

      /*  DataStream<CompZzLpTypeBaseinfo> eventYjtDataStream = originDataStream.flatMap(new FlatMapFunction<String, CompZzLpTypeBaseinfo>() {
            @Override
            public void flatMap(String s, Collector<CompZzLpTypeBaseinfo> collector) throws Exception {
                try {
                    if (StringUtils.isNotBlank(s)) {
                        String[] split = s.split(",");
                        CompZzLpTypeBaseinfo compZzLpTypeBaseinfo = new CompZzLpTypeBaseinfo();
                        compZzLpTypeBaseinfo.setLpName(StringUtil.formatCtName(split[0]));
                        compZzLpTypeBaseinfo.setType(split[1]);
                        collector.collect(compZzLpTypeBaseinfo);
                    }
                } catch (Exception e) {
                }
            }
        });
        DataStream<CompZzLpTypeBaseinfo> base = AsyncCleanDataSteam.orderedWait(eventYjtDataStream, new CleanLpTypeKeynoFunction(20)).name("CleanLpTypeKeynoFunction");
        DataStream<List<CompZzLpTypeBaseinfo>> merge = AsyncCleanDataSteam.orderedWait(base, new MergeLpTypeKeynoFunction(1)).name("CleanLpTypeKeynoFunction");
//        DataStream<List<CompZzLpTypeBaseinfo>> list = WindowCleanDataStream.toDataStreamList(merge, Time.seconds(10), 500);
        AsyncCleanDataSteam.orderedWait(merge, new InsertLpTypeFunction(20)).name("InsertLpTypeFunction");
*/
        env.execute(ApplicationCoreContextManager.getAppId());
    }
}
