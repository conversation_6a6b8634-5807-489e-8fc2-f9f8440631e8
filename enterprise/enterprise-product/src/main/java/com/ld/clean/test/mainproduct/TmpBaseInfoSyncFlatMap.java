package com.ld.clean.test.mainproduct;

import com.ld.clean.context.ApplicationCoreContextManager;
import com.ld.clean.model.manageenterprisedb.CompMaEventCvs;
import com.ld.clean.model.searchsyncenterprise.EpProductBaseinfoSync;
import com.ld.clean.repository.manageenterprisedb.CompMaEventCvsRepository;
import com.ld.clean.repository.searchsyncenterprise.EpProductBaseinfoSyncRepository;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.api.common.functions.RichFlatMapFunction;
import org.apache.flink.util.Collector;

/**
 * <AUTHOR>
 */
public class TmpBaseInfoSyncFlatMap extends RichFlatMapFunction<String, EpProductBaseinfoSync> {

    private static final EpProductBaseinfoSyncRepository epProductBaseinfoSyncRepository = ApplicationCoreContextManager.getInstance(EpProductBaseinfoSyncRepository.class);
    private static final CompMaEventCvsRepository compMaEventCvsRepository = ApplicationCoreContextManager.getInstance(CompMaEventCvsRepository.class);

    public void flatMap(String s, Collector<EpProductBaseinfoSync> collector) throws Exception {
        if (StringUtils.isNotBlank(s)) {
            EpProductBaseinfoSync pf = (EpProductBaseinfoSync) epProductBaseinfoSyncRepository.selectByPrimaryKey(s);
            collector.collect(pf);

        }
    }
}
   /* @Override
    public void flatMap(String s, Collector<CompMaEventCvs> collector) throws Exception {
        if (StringUtils.isNotBlank(s)) {

            CompMaEventCvs pf = (CompMaEventCvs) compMaEventCvsRepository.selectByPrimaryKey(s);
            collector.collect(pf);

        }
    }*/





   /* @Override
    public void flatMap(String s, Collector<EpProductBaseinfoSync> collector) throws Exception {
        if (StringUtils.isNotBlank(s)) {



            EpProductBaseinfoSync pf = (EpProductBaseinfoSync) epProductBaseinfoSyncRepository.selectByPrimaryKey(s);
            collector.collect(pf);



            *//*if (null != pf) {
                collector.collect(pf);
            } else {
                ProductBaseinfo baseinfo = new ProductBaseinfo();
                baseinfo.setId(s);
                baseinfo.setDataStatus(0);
                collector.collect(baseinfo);
            }*//*
        }
    }
}*/