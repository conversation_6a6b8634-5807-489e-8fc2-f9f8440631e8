package com.ld.clean.test.mainbrand;

import com.ld.clean.datasteam.AsyncCleanDataSteam;
import com.ld.clean.datasteam.WindowCleanDataStream;
import com.ld.clean.job.productv2.finance.entity.ASharesFinanceBO;
import com.ld.clean.step.SpiderParentStep;
import com.ld.clean.step.StepConstructorParam;
import com.ld.clean.test.mainbrand.dao.ScSeCompBrandFinancing20240513;
import com.ld.clean.test.mainbrand.dao.finance.LpfMeASharesAndNewThirdBoardFinance240919;
import com.ld.clean.test.mainbrand.financetmp.ashare.TestCleanAsharesFinanceBOFunction;
import com.ld.clean.test.mainbrand.financetmp.ashare.TestCleanAsharesInvestCompanyFunction;
import com.ld.clean.test.mainbrand.financetmp.ashare.TestSyncAsharesCompBrandFinanceFunction;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.windowing.time.Time;

import java.util.List;

/**
 * 反向同步dap融资
 *
 * <AUTHOR>
 */
public class TestSyncCompBrandFinanceStep extends SpiderParentStep<LpfMeASharesAndNewThirdBoardFinance240919, ScSeCompBrandFinancing20240513> {

    private StepConstructorParam stepConstructorParam;

    public TestSyncCompBrandFinanceStep(StepConstructorParam stepConstructorParam) {
        this.stepConstructorParam = stepConstructorParam;
    }


    @Override
    protected DataStream<LpfMeASharesAndNewThirdBoardFinance240919> transformSpiderStringToObjectFlatMap(DataStream<String> dataStream) {
        DataStream<List<String>> list = WindowCleanDataStream.toDataStreamList(dataStream, Time.seconds(60), 500);
        return list.flatMap(new TestCleanFinanceFlatMap()).name("CleanCompProductFinanceFlatMap");
    }

    @Override
    protected void saveSpiderToMongoDB(DataStream<LpfMeASharesAndNewThirdBoardFinance240919> dataStream) {

    }

    @Override
    protected DataStream<ScSeCompBrandFinancing20240513> cleanSpiderFieldFlatMap(DataStream<LpfMeASharesAndNewThirdBoardFinance240919> dataStream) {
        DataStream<ASharesFinanceBO> aSharesFinanceBO = AsyncCleanDataSteam.unorderedWait(dataStream,
                new TestCleanAsharesFinanceBOFunction(stepConstructorParam.getCorePoolSize())).name("CleanAsharesFinanceBOFunction");
        if (null != aSharesFinanceBO) {
            DataStream<ASharesFinanceBO> company = AsyncCleanDataSteam.unorderedWait(aSharesFinanceBO,
                    new TestCleanAsharesInvestCompanyFunction(stepConstructorParam.getCorePoolSize())).name("CleanAsharesInvestCompanyFunction");
            return AsyncCleanDataSteam.unorderedWait(company,
                    new TestSyncAsharesCompBrandFinanceFunction(stepConstructorParam.getCorePoolSize())).name("SyncAsharesCompBrandFinanceFunction");
        } else {
            return null;
        }

        /*return AsyncCleanDataSteam.unorderedWait(dataStream, new TestSyncDapCompBrandFinanceFunction(stepConstructorParam.getCorePoolSize())).name("SyncDapCompBrandFinanceFunction");*/
    }

    @Override
    protected DataStream<ScSeCompBrandFinancing20240513> queryCompanyKeyNoFunction(DataStream<ScSeCompBrandFinancing20240513> dataStream) {
        return null;
    }

    @Override
    protected DataStream<ScSeCompBrandFinancing20240513> queryPersonKeyNoFunction(DataStream<ScSeCompBrandFinancing20240513> dataStream) {
        return null;
    }

    @Override
    protected DataStream<ScSeCompBrandFinancing20240513> mergeSpiderAndDapFunction(DataStream<ScSeCompBrandFinancing20240513> dataStream) {
        return null;
    }

    @Override
    protected void cleanStep(DataStream<ScSeCompBrandFinancing20240513> dataStream) {

    }

    @Override
    protected void saveBaseDB(DataStream<ScSeCompBrandFinancing20240513> dataStream) {
        DataStream<List<ScSeCompBrandFinancing20240513>> list = WindowCleanDataStream.toDataStreamList(dataStream, Time.seconds(5), 100);
        DataStream<List<ScSeCompBrandFinancing20240513>> result = AsyncCleanDataSteam.orderedWait(list,
                new TestInsertCompBrandFinanceFunction(stepConstructorParam.getCorePoolSize())).name("InsertCompBrandFinanceFunction");
//        result.flatMap(new FlatMapFunction<List<CompBrandFinancing>, String>() {
//            @Override
//            public void flatMap(List<CompBrandFinancing> pfList, Collector<String> collector) throws Exception {
//                pfList.forEach(compBrandFinancing -> collector.collect(JSON.toJSONString(new ProductNotifyEntity(ProductNotifyEnum.FINANCE.getCode(), compBrandFinancing.getBrandId(), compBrandFinancing.getId()))));
//            }
//        }).addSink(KafkaBuildUtil.buildProducer(TopicConstants.TOPIC_BASE_CLEAN_COMP_BRAND_FINANCE)).name("send_clean");
    }
}



