/*
package com.ld.clean.constants;

import lombok.AllArgsConstructor;
import lombok.Getter;

*/
/**
 * <AUTHOR>
 *//*


@Getter
@AllArgsConstructor
public enum ProductCntEnum {
    DEFAULT("", 1),
    ROUND("融资历程", 2),
    MEMBER("核心成员", 3),
    COMPETITIVE("相关竞品", 4),
    NEWS("相关新闻", 5),
    BANGDAN("荣誉榜单", 6);

    private String name;
    private Integer code;

    public static ProductCntEnum getByCode(Integer code) {
        for (ProductCntEnum cntEnum : ProductCntEnum.values()) {
            if (cntEnum.getCode().equals(code)) {
                return cntEnum;
            }
        }
        return DEFAULT;
    }

    public static ProductCntEnum getByName(String name) {
        for (ProductCntEnum cntEnum : ProductCntEnum.values()) {
            if (cntEnum.getName().equals(name)) {
                return cntEnum;
            }
        }
        return null;
    }
}
*/
