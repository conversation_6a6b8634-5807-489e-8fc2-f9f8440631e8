/*
package com.ld.clean.util;

import com.ld.clean.kafka.CleanFlinkKafkaConsumer;
import com.ld.clean.kafka.CleanFlinkKafkaProducer;
import com.ld.clean.kafka.KafkaHelper;
import org.apache.flink.streaming.connectors.kafka.FlinkKafkaConsumer;
import org.apache.flink.streaming.connectors.kafka.FlinkKafkaProducer;
import org.apache.kafka.clients.producer.KafkaProducer;
import org.apache.kafka.clients.producer.Producer;
import org.apache.kafka.clients.producer.ProducerRecord;

import java.util.Properties;

public class KafkaUtil {
    private static Properties props = new Properties();
    private static Producer<String, String> producer;

    static {
        */
/* 定义kakfa 服务的地址，不需要将所有broker指定上 *//*

        props.put("bootstrap.servers", "kafka.ld-hadoop.com:29092");
        */
/* 制定consumer group *//*

        props.put("group.id", "news_id");
        */
/* 是否自动确认offset *//*

        props.put("enable.auto.commit", "true");
        */
/* 自动确认offset的时间间隔 *//*

        props.put("auto.commit.interval.ms", "1000");
        props.put("session.timeout.ms", "30000");

        props.put("key.deserializer", "org.apache.kafka.common.serialization.StringDeserializer");
        props.put("value.deserializer", "org.apache.kafka.common.serialization.StringDeserializer");
        props.put("key.serializer", "org.apache.kafka.common.serialization.StringSerializer");
        props.put("value.serializer", "org.apache.kafka.common.serialization.StringSerializer");
        producer = new KafkaProducer(props);
    }

    public static FlinkKafkaConsumer<String> buildConsumer(String topic, String groupId) {
        return KafkaHelper.buildAuthKafkaSource(CleanFlinkKafkaConsumer
                .builder()
                .topic(topic)
                .groupId(groupId)
                .build());
    }

    public static FlinkKafkaProducer<String> buildProducer(String topic) {
        return KafkaHelper.buildAuthKafkaSink(CleanFlinkKafkaProducer
                .builder()
                .topic(topic)
                .build());
    }

    public static void sendMsg(String topic, String value) {
        try {
            producer = new KafkaProducer(props);
            ProducerRecord<String, String> msg = new ProducerRecord(topic, value);
            producer.send(msg).get();
        } catch (Exception e) {
        } finally {
            producer.close();
        }
    }

    public static void main(String[] args) {
        //sendMsg("dap_enterprise_product_service", JSON.toJSONString(new ProductNotifyEntity(2, "69179edd-2ff3-439e-83c3-65e42d4f49fd")));
    }
}
*/
