package com.ld.clean.beans;

import com.alibaba.fastjson.JSONArray;
import lombok.Data;
import org.springframework.data.mongodb.core.mapping.Field;

import java.util.Date;

/**
 * <AUTHOR>
 * @Description: mongo中的公司信息
 */
@Data
public class QccCompany {
    private String id;
    /**
     * 法人姓名
     */
    private String corporation = "";
    private String name;
    private String province;
    private String city;
    private String industryCode = "";
    private String subIndustryCode = "";
    private String middleIndustryCode = "";
    private String smallIndustryCode = "";
    private String imageUrl = "";
    private String webSite = "";
    private Date startTime;

    @Data
    public static class ContactInfo {
        @Field("WebSite")
        private JSONArray webSite;
        @Field("PhoneNumber")
        private String phoneNumber;
        @Field("Email")
        private String email;
    }

}
