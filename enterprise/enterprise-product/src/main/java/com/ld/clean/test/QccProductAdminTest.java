package com.ld.clean.test;

import com.alibaba.fastjson.JSON;
import com.ld.clean.beans.ProductNotifyEntity;
import com.ld.clean.kafka.KafkaHelper;
import com.ld.clean.mongo.BaseMongoEnum;
import com.ld.clean.pojo.bo.sivs.QccInstitution;
import com.ld.clean.test.entity.InvestCvsTypeTmp;
import com.ld.clean.utils.DateUtil;
import com.ld.clean.utils.MongodbUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileInputStream;
import java.io.InputStreamReader;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

public class QccProductAdminTest {
   // private static final QccProductAdminRepository qccProductAdminRepository = ApplicationCoreContextManager.getInstance(QccProductAdminRepository.class);
   // private static final EpInvestInstitutionBaseinfoSyncRepository qccProductAdminRepository = ApplicationCoreContextManager.getInstance(QccProductAdminRepository.class);


    public static void main123(String[] args) {
        Query query = new Query(Criteria.where("_id").in(Arrays.asList("aaa92f5f6658458e3822fbc80c7e7ed1","8cd05d72475db7d95ee95cde6fbc696c")));
        List<String> logos =new ArrayList<>();
        List<QccInstitution> selectByQuery = MongodbUtil.getSingleInstance().selectByQuery(BaseMongoEnum.PROD_ENTERPRISE_DB, query, QccInstitution.class);
        if (CollectionUtils.isNotEmpty(selectByQuery)) {
            selectByQuery.forEach(qccInstitution->{
                qccInstitution.setBlog(JSON.toJSONString(logos));
                qccInstitution.setWeChat(JSON.toJSONString(logos));
                MongodbUtil.getSingleInstance().upsert(BaseMongoEnum.PROD_ENTERPRISE_DB, Collections.singletonList(qccInstitution), QccInstitution.class);

            });
        //    QccInstitution qccInstitution = selectByQuery.get(0);

        }
    }

    public static void main(String[] args) throws Exception{


        String encoding = "UTF-8";
        List<String> list = new ArrayList<>();
        InputStreamReader read = null;
        try {
            File file = new File("C:\\Users\\<USER>\\Desktop\\new3.txt");
            if (file.isFile() && file.exists()) {
                read = new InputStreamReader(new FileInputStream(file), encoding);
                BufferedReader bufferedReader = new BufferedReader(read);
                String lineTxt = null;
                while ((lineTxt = bufferedReader.readLine()) != null) {
                    list.add(lineTxt);
                }

            }
        } catch (Exception e) {
            e.getStackTrace();
        } finally {
            if (read != null) {
                read.close();
            }
        }
        list.forEach(itt->{
            ProductNotifyEntity entity =new ProductNotifyEntity();

            entity.setType(1);
            entity.setProductId(itt);
            KafkaHelper.javaKafkaProducer("base_enterprise_comp_product_relationship", itt);
        });

    }
    public static void mainxxx(String[] args) throws Exception {
        System.out.println("step1");

            XSSFWorkbook book = new XSSFWorkbook("C:\\Users\\<USER>\\Downloads\\sunchao_d1000002093747131.xlsx");

            XSSFSheet sheet = book.getSheet("数据");

            int lastRowNum = sheet.getLastRowNum();


            List<InvestCvsTypeTmp> tmps = new ArrayList<>();

            for (int i = 150000; i <= 498224; i++) {
                XSSFRow row = sheet.getRow(i);
                if (row != null) {

                    InvestCvsTypeTmp typeTmp = new InvestCvsTypeTmp();
                    for (int j = 0; j <= 1; j++) {
                        Cell cell = row.getCell(j);

                        if (cell != null) {
                            cell.setCellType(Cell.CELL_TYPE_STRING);
                            String value = cell.getStringCellValue();
                            switch (j) {
                                case 0:
                                    System.out.println(value);
                                    typeTmp.setPid(value);
                                    break;
                                case 1:

                                    typeTmp.setDate(DateUtil.getDateOfStringFormatTime(value,"yyyy-MM-dd HH:mm:ss"));
                                    break;
                                default:
                                    break;

                            }

                        }

                    }

                    tmps.add(typeTmp);
                }

            }

            for(int i =0;i< tmps.size();i++){
                if(i%100 ==0) {
                    System.out.println(i);
                }
              //  qccProductAdminRepository.updateUpdateTime(tmps.get(i).getDate(),tmps.get(i).getPid());
            }

    }


}
