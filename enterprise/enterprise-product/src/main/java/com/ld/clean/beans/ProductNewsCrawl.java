package com.ld.clean.beans;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class ProductNewsCrawl {
    private String title;
    private String url;
    @JSONField(name = "publish_time")
    private String publishTime;
    @JSONField(name = "financing_id")
    private String financingId;

    @JSONField(name = "product_id")
    private String productId;
    private String type;

    public ProductNewsCrawl(String title, String url, String publishTime) {
        this.title = title;
        this.url = url;
        this.publishTime = publishTime;
    }
}
