package com.ld.clean.util;

import com.ld.clean.enums.RoundLevelEnum;
import org.apache.commons.lang.StringUtils;

import java.util.*;

/**
 * <AUTHOR>
 */
public class RoundUtil {

    public static final Map<String, String> IPO_MAP = new HashMap<>();

    public static final String GU_QUAN_RONG_ZI = "股权融资";
    public static final String ZENG_ZI_KUO_GU = "增资扩股";
    public static final String GU_QUAN_ZHUAN_RANG = "股权转让";

    public static final Set<String> set = new HashSet<>(Arrays.asList("IPO上市后", "Post-IPO", "成长资本融资", "风险融资",
            "风险投资", "股份合并", "官方披露", "略投资",
            "其它", "上市后", "私募融资", "增长资金", "债权融资", "债券融资",
            "债务融资"));

    public static final Set<String> ZHAN_LUE_RONG_ZI = new HashSet<>(Arrays.asList("未知", "待披露", "官方披露", "未披露轮次", "轮次未知", "风险融资",
            "风险投资", "战略投资", "未披露", "未知", "IPO后", "Post-IPO", "上市后", "换股", "融资轮次不明"));

    static {
        IPO_MAP.put("BJ", "北京证券交易所");
        IPO_MAP.put("SH", "上海证券交易所");
        IPO_MAP.put("SZ", "深圳证券交易所");
        IPO_MAP.put("HK", "香港交易所");
        IPO_MAP.put("OC", "新三板（公开）");
//        IPO_MAP.put("NQ", "全国中小企业股份转让系统");
        IPO_MAP.put("NQ", "新三板");
        IPO_MAP.put("NEEQ", "新三板（场内）");
        IPO_MAP.put("NASDAQ", "纳斯达克交易所");
        IPO_MAP.put("NYSE", "纽约证券交易所");
        IPO_MAP.put("KOSPI", "韩国证券交易所");
        IPO_MAP.put("TW", "台湾证券交易所");
        IPO_MAP.put("FWB", "法兰克福交易所");
        IPO_MAP.put("AMEX", "美国证券交易所");
        IPO_MAP.put("LSE", "伦敦证券交易所");
        IPO_MAP.put("BSE", "孟买证券交易所");
        IPO_MAP.put("SGX", "新加坡证券交易所");
        IPO_MAP.put("MI", "意大利证交所");
        IPO_MAP.put("TSXV", "多伦多证券交易所");
        IPO_MAP.put("ASX", "澳大利亚证券交易所");

        IPO_MAP.put("ISE", "爱尔兰证券交易所");
        IPO_MAP.put("TSX", "多伦多证券交易所");
        IPO_MAP.put("SIX", "瑞士证券交易所");
        IPO_MAP.put("NSE", "印度国家证券交易所");
//        IPO_MAP.put("OMX", "赫尔辛基证券交易所");
        IPO_MAP.put("HEL", "赫尔辛基证券交易所");
        IPO_MAP.put("TYO", "东京证券交易所");
        IPO_MAP.put("AEX", "阿姆斯特丹泛欧交易所");
        IPO_MAP.put("TSE", "多伦多股票交易所");
        IPO_MAP.put("EPA", "巴黎泛欧交易所");
    }


    public static String getRound(String round) {
        if (StringUtils.isEmpty(round)) {
            return "";
        }
        round = round.trim().replace(" ", "");
        if (round.startsWith("被收购") || round.startsWith("并购")) {
            return "并购";
        }

        if (ZHAN_LUE_RONG_ZI.contains(round)) {
            return "战略融资";
        }

        if (round.toLowerCase().startsWith("pre")) {
            return "Pre" + round.substring(3, round.length());
        }

        return round;
    }

    /**
     * 是否上市公司
     *
     * @param round
     * @return
     */
    public static boolean isIpo(String round) {
        if (!round.contains(".")) {
            return false;
        }
        Set<String> keySet = IPO_MAP.keySet();
        for (String key : keySet) {
            if (round.contains("." + key)) {
                return true;
            }
        }
        return false;
    }

    public static String getFormatRound(String round) {
        String formatRound = RoundUtil.getRound(round);
        if (RoundUtil.isIpo(formatRound)) {
            if (formatRound.contains("退") && formatRound.contains(".")) {
                // 退市
                String realRound = StringUtil.replaceChineseAndKuohao(formatRound);
                formatRound = realRound + "(已退市)";
            }
        }
        return RoundLevelEnum.getRound(formatRound);
    }

}
