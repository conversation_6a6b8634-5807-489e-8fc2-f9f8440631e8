package com.ld.clean.util;

import com.alibaba.fastjson.JSONArray;
import com.ld.clean.beans.InvestEntity;
import com.ld.clean.beans.ParticipantEntity;
import com.ld.clean.constants.InvestTypeEnum;
import com.ld.clean.context.ApplicationCoreContextManager;
import com.ld.clean.dao.model.manageenterprisedb.IpoHistoryBaseinfo;
import com.ld.clean.enums.DimensionEnum;
import com.ld.clean.model.QccCompanyOutCt;
import com.ld.clean.model.manageenterprisedb.*;
import com.ld.clean.repository.manageenterprisedb.CompAffiliatedInstitutionRepository;
import com.ld.clean.repository.manageenterprisedb.CompAffiliatedProductsRepository;
import com.ld.clean.repository.manageenterprisedb.CompInvestInstitutionAliasRepository;
import com.ld.clean.repository.manageenterprisedb.CompProductAliasRepository;
import com.ld.clean.util.ct.CtStringUtil;
import com.ld.clean.utils.CommonUtil;
import com.ld.clean.utils.CtCompanyUtils;
import com.ld.clean.utils.Log;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
public class ParticipantUtil {

    private static final Pattern pattern = Pattern.compile("\\((.*?)\\)");
    public static final List<String> NOT_INSTOITUTION =Arrays.asList(
            "67a5de9fa89738da0c6835ef457b5878",
            "d3dc838ba382b4ceb17133be54918fab",
            "150353f887435acbf6ef0a57e4a3dbca",
            "13ab292915288fa2636f26c869f66e98",
            "2299ba7afcf843964288d8dd56eb7ef3",
            "41aac14db73210c381562306a84d4ee4",
            "415e686c63e7d3ef284420ae6bb4d231",
            "d99de99a4069784cb4ed7e3aebd3a9c5",
            "2e945b99f24f789d68d85ee332131c93",
            "34a3066038aa40024f773b3ad42d8dfa",
            "7854d49ba2f35c970603fbe7b70364f9",
            "ddffcdbc82e7875126e2b9f3d087fd85",
            "7736164a2326982071b018bae3202afa",
            "ca7fd8f37778d550ade5a9166df81e9e",
            "b262c20bee48a2c302fe316b93800dce",
            "910b00a578d2042662d92e300673fb0b",
            "a0053b112a1f5187735c62d314d60067",
            "46515dcd99ea50dd0671bc6840830404",
            "3be1e43c45c1332103695ea502ff9207",
            "d46e1fcf4c07ce4a69ee07e4134bcef1",
            "9167d3136c6495ed5e73160749d32625",
            "cc1d70ad9d0ce820738dc9ffc4053a76",
            "a19bec071cf1c368ea9a4b561a806ca7",
            "38b1179514edf55ac1a7a402f96ab288",
            "b240a17d1641282bcffcc6468e077e2f",
            "1b318124e37af6d74a03501474f44ea1",
            "8cf0cda7a1cc0b2a341fd69c7b3efd91",
            "8be55a2d5c9c8945f0dac2cd37968545",
            "bf5fd78147dd0ae816e78c97786f7659",
            "0919055623ab25e5d18941d18f6cfeb4",
            "710a1e2a72678031a9396534ecec3e62",
            "d4c074c60c57087c95fd0a17f986210c",
            "b6e73a8cba92745bdfde26547b7afa74",
            "e23e7c181f77e50f6fb869fc6f68ebc4",
            "9bd6f749f52f9255c626ed6a05333afa",
            "e21c1cbfb0c9cee2e12e9a074fbd2745",
            "294fb5183d1f8be7934df6c1c2dab97c",
            "854349208a666655ce17d6a725d9728f",
            "4d289c150fc83d36f5158512246e3bfe",
            "953a62218bd09aa96111cd649a11b4f3",
            "c6b50921cc4694aaf114c1b9747fa976",
            "7a406846ab25b0af8405c1bc383dbb70",
            "c3d2fde10be1feaa5d27a7d453de16c3",
            "4176593577659e2f9783c6736af180a2",
            "d8f44f602c6149df8b17e1c01e194a87",
            "b66a7680eff99133f9c629111fd9b1fa",
            "fdfbbaafac8bf1e9328525c8fcbb8720",
            "709b30691b2f688f5340308b35643191",
            "364ad5b058688b8416437607ff2f9bfa",
            "97b9a3994ae58b8cc34242b9095f3b6c",
            "4abfc1b62138ff434169bb866a3c19cb",
            "5c6b10f56508e77228211575e68567f1",
            "73d5b781a7d74f61bd4587dadb8cbf90",
            "565e9ab63ab700020167bd69419c15c0",
            "4ab50afd6dcc95fcba76d0fe04295632",
            "9d28c480a87580e95fe80bd9670c89bf",
            "12d16adf4a9355513f9d574b76087a08",
            "5dacab03c06f42f75c3f21a2c9f98997",
            "11adb1fb65fa2e2b1cd94150eca37157",
            "35586c242944d662c8f402eb8225f188",
            "6addc887ff94c205612f27ccc7d93621",
            "f834e6cb4198b2d7d8f204c7cdb638e0",
            "ab0e2243651a3c977bdc1edf5d7096d4",
            "72067c21421784a4b032eea4eb040131",
            "1768669f3e8a1e693fdd9ac591b5b817",
            "e21510d0e3a356a400677bab62b5bc94",
            "38d0d22526b2644ca2f56d98294d4ef0",
            "a062420c37988eacbf894294073f1825",
            "7a727de9bdd9e24d273decd4677fea25",
            "33441d4922ec8ea574cefae475e1fe4b",
            "c4d1a814746f620e65811accb9e8fa73",
            "7ec74282dde60af4d8d119cee21607f2",
            "dc09b9e30905b0643f83b411c1a59641",
            "f1c5e9d6bc145fdfa2b114877c23c884",
            "d7e30ec8d6d6852ed6492006914e7c2c",
            "bf84dfa6e2921cb03f913fb167651039",
            "ed911be3c625d3912037d1c7a2bd91b9",
            "27e37d6a1fdce8632dd92f5d645b8296",
            "960b18a4c3fc9dd4f67bb76cf988cb2e",
            "231d58a9e7822762ae77e6a6171e80ee",
            "af5bdf9b9b5d63fc1abf0529e6ed3783",
            "f37e0aae411bb54be15209a276003d55",
            "88440ae05d52b3a28bf92f31f3968004",
            "54f9d58902594042457489457bc7b79f",
            "0d52e25778dbeda8cef594e4e5da8e90",
            "162b7622539f752c5f6b775660951b5f",
            "7e3e9226de619a5b1b85ec3727c33059",
            "dac32839a9f0baae954b41abee610cc0",
            "223a97f02a0335064b2e7b0019b1fd62",
            "2f160931d3cb85ceb4bd66c9c9122069",
            "3ee508b09ebf1eab70d607fc3ec68432",
            "0d2ef04e8fb6a5aa6fca9841c893c172",
            "eeacef2dc7cf16ee9ceb09e4600f4e29",
            "8110eaeb55d8f5ed7aa18842dc0ceacd",
            "84c7b3a8b9c0ea78fb4bf122a80c0866",
            "285e8872d2db4bee7aa7cd87a9e54947",
            "71dc0378fe3c4dd7d2a6029b6a051ad4",
            "69070011afae735c09d0308eb633aa60",
            "8bc43b83b2990eba8e852cc924c70264",
            "f665616bb2a2c073ba2c6a5534814f75",
            "8164ca2fe04767628ac1c6813e8a0867",
            "64a27f00970b93cac74624415060cb87",
            "e4425f08171eace71fb4a7d0600416e7",
            "3c681ce8a1e3f8351d46d3b08261107f",
            "3b8e7391f2a25a6f38a525536c1601c3",
            "7789e09082251daad18e343b1757a137",
            "f536a06e3d59cefed96b8aa91e9f9f05",
            "d341837c40756767c635efc9277ae95a",
            "f5fbd8e922fec72ca89d73985d543c68",
            "5d1d60da62735613f46eef4a158008da",
            "d865276071b5d56d178d6291cce2c1bc",
            "ad5d64641d6cf8ab8e65e94a247918b4",
            "dbf3d4f2a37460c556b7aceb0c71bc4c",
            "a31ebc0af5f1342d6a74d419f46731c5",
            "d85b130e775b4d9888f060809bbe4c50",
            "7f652fe46a4cf4d86d6ed04e2fa92809",
            "04c91f2c16682d1fd7a731e28f9eb83a",
            "11492293501b32e6a28f10cef0d8e994",
            "08b20123b418fd7b37dfa70f0f5d03b3",
            "90a60c501fe3ccbbf6e28285d4d75277",
            "02a930808b38f478921f6de0166fa622",
            "404a9f61135c5e33d002f3fd97350b8f",
            "216965b6c0c343a0866b3634853d5569",
            "74eb7169b0e4a34643f20fd3d7dce91b",
            "a59a9c2e2ec7250a16e4fff6367e953e",
            "6f4162960b7003f4794cf400faf64e8e",
            "5a37891774000f9261f7aaaa4c6ef92a",
            "be3e9d3f7d70537357c67bb3f4086846",
            "5ec12708075034242449f12041d70ec5",
            "68f7453ff238f344c3c79702557ce1c4",
            "2086a7e3d679726b40537536f52dd021",
            "73f8332c1bedd9d1cfc4bd25a8d1e481",
            "0f3a58bcc25640d5fde47df86821aa1a",
            "8128dadacb0a3f98b681237514b6a875",
            "f59842815132904a4e1501cb859e4ec2",
            "9dbe572576a1a7af589fe9b0ade8a514",
            "f8c4b35457ed7f164321b9fe315af675",
            "a2f94d8e28139ce8120147d24fe3b8f6",
            "245e6ec324dea2821782f9d3c5bca743",
            "f5c0a6928ea1fcc736712d2e5a44f099",
            "bae82e7c28ce5b0486c6ff9cd0c9183e",
            "a691dc77ce45c8190a68efc2f34fcf30",
            "3198dfd0aef271d22f7bcddd6f12f5cb",
            "e86698eadde4101913eaf1c8ea42c8ce",
            "52cc84af53166033cc4081089fbca59a",
            "2575b87c75e3eec145979684a3f35fa9",
            "64ee1a382d8e7aa1e05b1601aeba124a",
            "3337e30b0eb4b7ef2272d492bc20cca6",
            "4fa054097874de60df0d5881e2152eff",
            "6e3e2bd2997778f3c28b19a1d4540fc9",
            "5877f3ec4072806817c74fb8c310f895",
            "3c0f7c764ba71fd88a78664519ef973a",
            "4a746a82d8207b2c545c747091fa7597",
            "bf65e99397155aba443ba4c7c6d61193",
            "dfce15b8a8ca07caeb8de43542ec4bab",
            "6e03de055690c38de8a573d820b7908f",
            "ff39f26034bf6d654886fa47caeff024",
            "997440836d4c2346926d8aca03690f78",
            "39345f228df422a2ff7aa90016e8df9c",
            "17e52b896077bca8af051bc6bdb6d6a7",
            "103cf2255c43e5379d166cfeb08886dd",
            "7852cd8336a6b179912b94e2c09e657c",
            "85942ccf7c31c7a065c35f288bfec110",
            "668c7d9d4728fc9eebbe7a8202c95c26",
            "40267740f9fcf39b039955180d889ec8",
            "3c6872b5dfe7ead5043eaf7651dccd27",
            "3ed2a57dc9433ef2c4627d77fb56ae38",
            "471d5738955b3c75593993efa1c289bc",
            "06254f5dc0a1739387ca4eadc34926c3",
            "bd82e65337cec303c7b70e3f95a15a02",
            "bcf0f5a7c9f8b7c52c9c93b9befbb487",
            "423215dae3a7a243e300638262f9242d",
            "0b2978ae0ca8efbfbf5af006bd7b4298",
            "9bbb9a5df34c69244286195a1154bc36",
            "0a65ad1c65850aa5a38bf5c9b9bc8fba",
            "f51238cd02c93b89d8fbee5667d077fc",
            "c94294b357f10f01b7889fe0e1623eb5",
            "585e1b6742debf9164cf5a79d0399d9f",
            "4c6ba557dabd286e1d1c03af94c671ff",
            "e98549a4cb29369fb6dbb48ab0b6e018",
            "7842736671a89f06dee2c7049c5b9a89",
            "1c39c39d1a341ba03ae48a942c6a43ef",
            "9ce024944f251f594d2956609e71b9c3",
            "8e0e84c4c1af1947a76db2370406df7a",
            "757cafdc078bc3771b1a7d802c466a3d",
            "242620f8ce5e0e002e911a1619dbf503",
            "983a33a9a86796df362c1108e00f54a6",
            "ad059a4fe490797cc39619ff4e85a657",
            "125307a00809a4ef88ac8b01c382b4c1",
            "af4171db3fbb9b02209978b5a0481218",
            "e2e407f4c23798ff9c887412db0e7e45",
            "60a38f2596b1f6a8fd5f28604765cd5e",
            "5fa68f9f6aadb96b5f557d7a5298916d",
            "01064f1de9dfcd9d77b14d11beefefd4",
            "5c51f699fd8d88e17567b908766fca16",
            "ff266e3d83685add02e7cf5f93b4a77b",
            "d519a2b536a438401da69b5633828816",
            "72cc4c5e6cbde70aa374bf94076ad722",
            "b331d6938bc5b58153d6f6df48bb3232",
            "90143b13f084d867485e49fb28602db0",
            "ea33651c693480b4e0dc1bff6196bad7",
            "e8ee43cf4bcf5569d833f150a66ac4e8",
            "f5ebd2dd805a58ac40c8aeb53c278afe",
            "e413e11d7ff323ec44eba3552c979918",
            "c693fd730c47d7187a9701160905f4eb",
            "131db143d3a08325445252b14b14eb70",
            "450e92aa762ce31e6f6f5e4402facc73",
            "9dbda1a987521e7b2b57036db27c9642",
            "70e32a5409596393966b92875b5b9971",
            "4558dbb6f6f8bb2e16d03b85bde76e2c",
            "6167455355723da14f94980eb4179c68",
            "b49bba5842ad15dd7224a769b83cee6f",
            "f3102064ef90bf7d811c330f976366f7",
            "5b34dcb263fa7c64132c2e7833c349b8",
            "c37f9e1283cbd4a6edfd778fc8b1c652",
            "2559854e14663053f02bdfb2c3066c1d",
            "0eca1b1fa61b0e9308cf509999772079",
            "fd411fb2be3c469755d870abff6bf57e",
            "bf0a8e1dc5c86ca9c4ebb1716579ba9a",
            "dc9b06acfe2f1655256ef9f836bb5f61",
            "ced68d40cb033e9938cca60833341a8a",
            "af516cd3bea3d9e8f7d7df76dfc0c121",
            "8d7caa9cf5ceb9273f77d7f5eae9fec7",
            "f7ac0096531663e3d999aca8d955c8ba",
            "8ea0b4efa52dcaf499151a5c90c13e04",
            "5f5ca5730f5be6dfe52794a5ac50ce4e",
            "de68d312195ae298a1188c372b5c02d7",
            "4dcb2a3d770a065f57237f42d828aab1",
            "8a46e81971bb32581ce2574e521468c5",
            "278238d154c261221573e4e231e68ec6",
            "6fab6e3aa34248ec1e34a4aeedecddc8",
            "f2f8c58ad7f587658c8bcceb0c6ea7e9",
            "32523734b46f1f62ea0994ed1a521b16",
            "975bf39673b35d686bf892c8828f01a4",
            "1de7d2b90d554be9f0db1c338e80197d",
            "3c86b06e1dc5fdcc7d42e8acd0f0aebd",
            "47012a1da1fb9529dbdf297b0b66a0d4",
            "e48eceea36e716427db74aada6418df9",
            "219139a764bf436d8f2ee5636570f7e1",
            "d81c342ba8a91321e754fbfd496b8a9b",
            "b7e613a1cb13b29e4e63b9c596c1bb14",
            "0e0007e49f309441599c716dd5897fc5",
            "52efcf2b5d1643ac1cfe1a8d8445c01b",
            "9fb87ac23ffa13dd7502c1c011357742",
            "7f035cb797f521e0e4195fa01ec31806",
            "9e4f8319d8de76923a69fe8d5e811ca1",
            "3412e89ab40a451219625e204241e119",
            "08425b881bcde94a383cd258cea331be",
            "458fe1807955b8bf308c30992db5b56e",
            "9d4ae9ac88d559bb65b38889a06faeaa",
            "5a92d0309d7043ae54b4823c0a7dbc86",
            "2868ed4061266252e8a86f309aafc0ff",
            "ae1eaa32d10b6c886981755d579fb4d8",
            "9dfe21c824970d299c61fa21f609b25c",
            "bb3cc3b18ad9883ba1d4c8d9c2af8f71",
            "554e2ae1c1d6bc9618db9342bd91eb92",
            "6aed81cf880f4f9090f0fa8fc5c25d62",
            "e0866d886912e37b7bb60104edc35c39",
            "3b3e217d65134e1781397d5af12d1deb",
            "b8b2fc40d50fb88756832b6928a329f0",
            "2561dc6003e4586522b4d0192be487e3",
            "8c4a0858f77909c3b2d2beb822fe5579",
            "9b4bb52debda91270ba4ffea70144a9e",
            "fdea6d4937f0d905a6b45067f816011a",
            "a15f9896c1b92e7adc7d0232afb30816",
            "b673ed011cfb3c810010abed6f3a034b",
            "a87dc557431a32ea0ffd94c137d2a9ff",
            "2dd13006bbeafaf76613e8210020e19c",
            "38ce268d31a28f261f3e5481264b55bd",
            "72918ddb583ee47c4b3d78ce63f36c4d",
            "d237d958d5c45ed51aae55a91bbcc3da",
            "159135ceadd899892e40b4ca13cda913",
            "30fcfc66a6064084c8840053ec782f01",
            "ff0c31d2c239fbf5a34bfcbbaa764a05",
            "e588b333d4088e86fd5b7789ab18d040",
            "b9e22adc764302e7e6cc10541eb94c57",
            "3c6c81b7606c38fa4bce1de0c5583f49",
            "7e3d64345c803dd16a1441d8666be145",
            "7b460ddd44941199b910852135bc4192",
            "bdba8abe7f73cad34dc825d9b5a660f5",
            "2831d7fa945e96d6bc5846acd101634e",
            "83e58d440317e3004159c0df7afa6cb5",
            "ad7f97ea68b464c958c10bac21dacf3a",
            "66bc82ad3117fdd76c6d55d978e16dc7",
            "d0954320643b27707797dac5e8b1a9e1",
            "5c142c3bfd572b54fcf5efda828aadf8",
            "e394c4fcf4858aeb877a99486c9c9418",
            "24a1891334f602eb16c4e1f5d90b6a06",
            "235fba44e32ba4dd3a3f72db1a8a6846",
            "e27393087b9567a7692cc7d53a031522",
            "076eaca553c6e1e367ef57135c68d61e",
            "0112b119af399c8168cadb416dc6284a",
            "e7d3f8e08004c67d6b9f73ef8905b7c3",
            "7d4545a34db015b7cfe55feb306adbfa",
            "ee3bf295b9cf5a7ad4297868b069c91f",
            "c32efcb7f667f6c5def39db8eda2e6ce",
            "632f99aa42fc665104ee0fe91fa311ca",
            "e81953b0b86949f5027691d96aa2ea75",
            "59238d1c15598f332b4b05a5f7808c4d",
            "8a663a5ad9ff239de0566cfb26720c36",
            "0e3ba38d4f92efcec43041e9a8584a61",
            "544a66d5696a6e07b69dc8df98d6f825",
            "172eb1586b0089076932c43fdbaf5fab",
            "249a2d8d4ae362355cb7bce6ad8816df",
            "e73324e4498aedf04d1e444bd64d2523",
            "285c6b1945ddcaf3614f22f2a7ccd5f6",
            "d44a3a9ff0f512b10433a64782e943e5",
            "6950a55bbcec53e2cdbd188366331f9d",
            "5be396d491804b85478c7a54b3cb10b8",
            "223fdf3dc4c52431005cb7c2807300d8",
            "8276e7c837e90f27b5b0d6a457fcee46",
            "1589515970487c94597dab5c1be44085",
            "8a68563cd78a1ae9288534993baa3c8c",
            "bc53103c4bf10dbe81cca8b538255f83",
            "a05c905341e0ffcb8da5f2e4c9703ca9",
            "73e548d5a90e4442683ee4f71b7d1acb",
            "2109366d68770c5528aa56705516f364",
            "1e8d7c5fa21b291442c2a13d65de1b3a",
            "70da8abd9843831b730e3700f4010639",
            "adf6b369e33b158e459803aa392293ac",
            "0f80fba95f4ceca4f481af1d1ebb8a18",
            "abb05b6fc13831235a54e846479b5f9f",
            "44680b5f7cefd57a0e580cfc11a9308f",
            "90bac4f2e65553f240dd69645953e0f1",
            "c6b8c8d762da15fa8dbbdfb6baf9e260",
            "1a5161146e5d196a29c618b56e82ee79",
            "3339b56abe0ea815ef57734c8641c87a",
            "af23a2aa7888244b968621e35114bee9",
            "46ada9cce4b74351b6cca4d92c739e69",
            "24a7ffe3bda20532c5f116825ff5dfcf",
            "656d443862571f239599c6a0fdc2b794",
            "9a22081df9a3dc92346092250905824e",
            "dba6e93a35b0f054e2811a733f92ddc8",
            "1434f3e6425414e2473f2b1dcb525b88",
            "42e3a05710ae82d8215c38bd884d3101",
            "af8e5f26293c2fbe6e715480014c8791",
            "428365de6e004c615fe51282a0b8d9db",
            "9127231f5c05edaf1b5a8a9ed5767d47",
            "e2690453f3f62e5e9472168493356ddc",
            "33e41a4645cc3bce4d1bdd6d6f702bc7",
            "524995c223c336c4bf7b216df288ebd6",
            "136a7deb4abacf73b70360f08a8bf729",
            "ca4b33532855080dfa79cf8a925d146d",
            "3d3a26a529d4f24aee4e99bfc647b2d1",
            "ff4ea7d42c2e7432e377b910a5c232ef",
            "5d5b4c81f82b8f407c177358c2b4261f",
            "58dda09cc988de7f7c28d042631d4d8e",
            "0ca931b6d1569edc302092c20f3970de",
            "dbeee731af328fd9c9fec2def6f2290d",
            "66d0d2688211c54316a0255f696de149",
            "82e6e374ad342ba6e2e7c12dabad91af",
            "6de157c85a1134320d38101c5b71896b",
            "2368351eb189595722c1505cf053351c",
            "a936d1e45c27caee4020d7098cfd2cb7",
            "15d58433add6fb09eb77a1610b9e3e6e",
            "3e158bf46a0117f81f67757d7475a72e",
            "b89b1201684d103d3c5cebf26a16680a",
            "3ae96484e0b2bdc138a991e9265ff3be",
            "4217ec5d78c4bc4e5bd006783482441f",
            "cd758e8f59dfdf06a852adad277986ca",
            "0c498e081c7b07bb576a271e8e8ff916",
            "d3377ef2c3455b2c4217e10aa7449771",
            "d6f0605a2b0d40b28867d3a2886b9185",
            "aee5cb5fa234bd84154adcc1f104757f",
            "666d64d6c1015fe61640e500733ab761",
            "1e00996d70a49ff85e8269693709c490",
            "3f93246c26d8d3cbba8a7f42295ed663",
            "d880e783834172e5ebd1868d84463d93",
            "a94df974ceab0179c62f8c290b8f6178",
            "808726e94121ad33cd5e0c379bb3eb4f",
            "0efe8e6d31392a4d2789f95b0cde8518",
            "fd974c1e9f969ea77ac5e978bf955a57",
            "c3dc7c2ed01772f30d48d0b0490a1d6e",
            "79be62058f3d96221ea57c893b7023b9",
            "68d0c446033c6c6c1e4fd47a7bd99d20",
            "e161e5c3f302ed4a6bbb091a145d07b2",
            "b312621e9b19a8f83548812a05bd28ec",
            "95ab612968ae06a9d941bf29f9e08777",
            "197838c579c3b78927e0cd15ba4c9689",
            "25e53381a6aa4a3eb8550524eaa999cc",
            "5267f30572db714a295a4c61b9a3c0d6",
            "52161718c39f3872edb6cf043c4193d3",
            "979ed499fc59f3ff86aa283720969d0b",
            "87b1415d08be9eeb065544792ee7cbe7",
            "0755b3a0d290ad87870dd8e15c488a87",
            "27ac2ec545905eb85fd45ebd07d88d2e",
            "cdcc1abaa96861a902a9661b9cdabac8",
            "47987d2885a82e66135437de9b712c36",
            "7c3628fb0ce3f251d0e8cd84ac18f0b8",
            "140f5bf26ad0a7048eb664332671a507",
            "0733ea9936b88be05911c4d6b1835dff",
            "3ab9a9924133b76cdd88db6e7b2734da",
            "a0d6cc57074e77cd290ab7175a92f585",
            "874a2ad90189f1e3267e0dc7b2d5f9f8",
            "251c5ffd6b62cc21c446c963c76cf214",
            "71abfd41229b11e2f431750af5f7693f",
            "bcf1c39d326c5a55dc33f556f6409982",
            "251d24304014df67abfd3b9342699164",
            "b0dd5a5e668e25ef6fe6216c9e9eec4e",
            "2bd388f731f26312bfc0fe30da009595",
            "9498b2e06ad783f89ef2d4d663db09d7",
            "10b0058a3b43ffc0f0fb791884f174ca",
            "8081292bf2dfd5660349b10c74bcf7cd",
            "a793d307441615af6aaad7497a75de59",
            "012e1ca6c40d756b9158c6de5e6926aa",
            "9fd3fae710499e54c9d903d1f6bf3c52",
            "574ebc151c3252c2eb93d6504efdc5ab",
            "209ab560b8685ae44b287507f8e23f18",
            "fc6f14eb7ed4cc4fc555140eb63faab5",
            "f216f0fb9dc1d24a3f113e9ad7cb0fc8",
            "b96a545a36220dede589c3111de5466d",
            "d6482d7679cd5f78ba013af766edf325",
            "e2318a7de8c5aebba00b1d6377e31658",
            "64c06df9608ee86092914f3fd32e5625",
            "0bddb0d0c91080c4dfe96fbbe2a9133a",
            "c6da1809515ec0b0a8bbe154a65ba405",
            "b2ea5b45a03ddd39ca45b446cdf44e9e",
            "5676cb60f8ece200f5eb5cc3dec1bd3f",
            "8f3081d726e96190f9d048bef52c8a1d",
            "d975ce1388dacf7c73e1baf2dcbd6d4d",
            "26751be1181460baf78db8d5eb7aad39",
            "eaa71a9257d4a4abbcc84080094780ee",
            "c15a33772f192561d23bc82b9b00909b",
            "d47aaf6900c9a9c50a7206b49324f4ae",
            "3d8d3d1b6958a3a6393faa6f4063bbad",
            "53a3836f786e5e42720f4d3f50e4af7d",
            "99e3f798a0799f0d2d7d8d043dd4550e",
            "12c67d0e047bbfe7e5fb9d79e175a6a5",
            "ee7cd23d24e883e1d525e91c912122a0",
            "4b3c7c771def218b5deba1ffd5e630b7",
            "c7cc64ab6f6298dd238f560dd8e9418c",
            "ff2cc3b8c7caeaa068f2abbc234583f5",
            "c25c66f8ac4fc39070aaa75c4c58c91a",
            "69650a619af368c12a6ee24947ad7572",
            "e21941cd9be6279c1ccfa87bc65371dc",
            "21c1da87c1afdd4ed2836bdb521bea78",
            "0afaa9b2b82f30abf25ebec755496739",
            "790ed95799df2cb1366be83d44fa5147",
            "fba5e165f5d1952f919d916e41d1a0a0",
            "13d76003b70cdd5dd0c1555d34de5ae4",
            "66d7175518414f3157ead38d981d1aac",
            "2f4059dbdef515f7ddf25d1888ec1704",
            "34abd0c17102077f14f3afee1e767be1",
            "b892251cc1478a1ade0b882b67ac4b37",
            "38da507040f7780990a3969a01bed463",
            "2ab5effb9e6e1dfe647fd4019cbe7c46",
            "104cd7d8a426555a1ff85ab02ec4347f",
            "de3114b8f4580d79b191d3de0d5ff3f9",
            "3369ab6e9520920e91aa479deaec3df2",
            "8b89afaf8e00e0a46ea4d76ac473b1a2",
            "dec4e6a4a8b9cda4ff1ea9e9f1d9f1b1",
            "c1f128362be6981224ab749661f64802",
            "68a83eeb494a308fe5295da69428a507",
            "f69e505b08403ad2298b9f262659929a",
            "ba33e9d50937fbc89695f28db2d9f5ec",
            "cabfa51301c9bb80f4827414b5dc8222",
            "bca1cef4d30b20cbab26bb952f2118d3",
            "7203065c4407e88edb63d50120b30182",
            "d69cf75f4fbeb85f073332706606ba76",
            "4b4cbc3ab2cbb32d5d0e27bbcce55314",
            "b16cf05aa0ddf3f9bac1d0ab699a1a12",
            "6403675579f6114559c90de0014cd3d6",
            "00c79f5e4b6098653d3c9db2529d17b7",
            "d1ede539c0e7526c96e57be32eef5f59",
            "51d327388aa40ce22b7d0b1fbad7aecd",
            "1b82fc4535470c58afb5fc8b874f1cd8",
            "4c70ff78860e4de30feb5bb81fcf48c1",
            "61a7b177d826c791c1506dc4d56257ea",
            "034bda2232ffe749f1adb131b9b1bfa2",
            "090431b4819e6908ba538a773133bcc0",
            "b830cb42a80ce2f98c7ddbf69094e96d",
            "a104fd4ca28bf10917cc0a19629f5358",
            "22d3a339906edca802cfa614dba815f0",
            "6382f5d8abb8d1e911fb43055fbabb7c",
            "994d1cad9132e48c993d58b492f71fc1",
            "5cb5611ec99fa9a3bb48d9effd7960f5",
            "4911071f2d74af4e7e429c1975daff9d",
            "ef8cce248c57b7892e0b9a3a892d5817",
            "2965ce803c2c1b9c9dcf88a2d7ace08a",
            "2f0c9a2a04dde5aa934400ca078a96eb",
            "01aee16bb28cbec11a5002a702f6bbec",
            "18a535ce9e935042555186eda3fe7f67",
            "f26990c534c3b5a550f83fceb0efce05",
            "cf011ff8d8380280133bce806e0f7bb1",
            "37385a400869a0493b33053b1d5a04f9",
            "e400ad2c334dfca04e9fcfa5da509407",
            "0d9c19e0cda3d5de07f747e3c09cbe15",
            "16cc0674e20945c1ef23dcd11778c62c",
            "6d33e49c5edc96e52dd08df90af5cb7e",
            "97a9ddfe6bacedd10eed055e06aa95fb",
            "706bec0531f6d372feeffd3208878f29",
            "ad1288236b3a6b1d28115a1f92ab8bb6",
            "319e7914e64648a43279c6a603e30f7e",
            "18953934ecd14af811eb9808aef83571",
            "e641e6b16b5d17066ed2290a48d5fb0f",
            "d8963a294d65c3960f181c96b5226d40",
            "a6ed219b7192f34a767f71ed27d91e67",
            "deea1124b840309beeea7f3daaa07654",
            "1a9882be708461829f0733ea65605383",
            "80d952655be3816f9083d5abc2e65728",
            "157f786e2424ac9ee32681d0c9e18540",
            "862aa7740e21bd0e3e031271b5f9f43c",
            "23adfa7ba2f8c5a55e25c8a7cec27c6f",
            "b552ec3ce2ada8f4aa37a83e0493f9e2",
            "2e97cd7b72e3ef71ce57032942717851",
            "bf651cf69fcb3097599bd40a5218444d",
            "b2b42983eaf705d570ebe879be081849",
            "4eb94721128ab06f06e92081fd84b3ee",
            "01fd0dbe941d41130c1614960d370929",
            "355d367f527ab689b546ec7b521b43ff",
            "30453271b58e6980a82bb04b9a688f7a",
            "2e7d9989a46d771d8e179c2c4ed57490",
            "20cf587e57f5777001a739fe3ce44575",
            "5d3dbbd8535ecc38223a4509a8d8ecb0",
            "14425ce7e57ca5a2ac038d638fbded24",
            "507e3d3595c76338e62b45fcc2fcd46f",
            "2cd70f8d748d49e9ec66c5332d7c1faf",
            "8530f45f0a0289a5816fb03fe66fbba6",
            "be929a31e71b3acf2b3dd3b71c858614",
            "e12fe638d1e4f5899c88501504302fab",
            "75144d9c8780608425dd34bfaf7b96f0",
            "99e5a6ae292728f981127b71861de113",
            "e1ae57b5281ce1c722df90b88c007653",
            "928bd8f62f5c070ccc4a16a0c6b62f1c",
            "08c2c110360b74511a70b9eafd571224",
            "8b8f66308ee7c3ad5d1031a5721f0753",
            "36d838a7f08b7ac8295662b7b104440b",
            "171a5c78645785f784c7b113b3849d47",
            "3ace9cce9492fbe28d3c8fff91a5a702",
            "5ec5d0494f2b53c4677f6c4874e25088",
            "86ce38a959220913572278e7135b0a61",
            "4d01443510be0010047b09f7fde1a70e",
            "e3dfc37d031a90af5f38f4887d61a201",
            "dba0ff02313bd467ce9d52df8d6c80e6",
            "e077e1a544eec4f0307cf5c3c721d944",
            "91101fdab6af2f5b72d3e501754bb2e9",
            "d239ead2e79d759f69d638ad13a26023",
            "d630553e32ae21fb1a6df39c702d2c5c",
            "7878b14e9d762184301b06f3f609ead7",
            "c63998d0dd02e51f07b30b5103094ce8",
            "a19599d637c08bdc0e3bfbc20aaf20ea",
            "7c46f849a42c2a3323f7a35e04dcf44b",
            "7487bd3d83c2a62c6262d0096cb7315a",
            "7d502bfe49721ad1683116fc205f9336",
            "b751ea087892ebeca363034301f45c69",
            "aa475604668730af60a0a87cc92604da",
            "54882baeeab2fc6324122f015b685313",
            "7b6c4f3eb426218153d5627a8b1a6a4c",
            "8c249675aea6c3cbd91661bbae767ff1",
            "d9139941e4589d180ddd387eac026beb",
            "69db61ce36a56178711fec066e07aeb6",
            "7cbe32f0a662e3cf73e755d3bd145fea",
            "7ae813cba8b80bf571d513b9a009a1bd",
            "0c6c201863bbdd71eaccbd19dd609b8c",
            "ad87aa0f6c2df5be15e9d6770bb47d99",
            "f780690936fdcb7e65b10ca3d3e58849",
            "c1ea8dfc8fe8e86652362fd88f55d9b5",
            "93e1f4834e59f2fd5224ca71a3be306b",
            "958adb57686c2fdec5796398de5f317a",
            "a78aedaa46d159c41a16b5232596ae83",
            "666ac341af5f717fbe3de5be97715241",
            "976f32de51f0ce5099165531dffd03bc",
            "a1abb38d09139875910553a0be74e74f",
            "e13574889c6fc54847e8456d0efbef61",
            "d7f5f6b7e3e879cbea72df49fc661f3c",
            "be5acb71f959598767dd12c4732e537d",
            "10efa3aa0dfdab5b31b7e4708c334bf1",
            "a35e2f4a3fa64ee63ab18d8072b3a806",
            "1d5cc52ea5c4f35a8ceafef111934f69",
            "e4d2b6e6fdeca3e60e0f1a62fee3d9dd",
            "f915f4982f91e057fb20fd048e542c66",
            "e9076297548f926ca4f6632f0fe910b1",
            "f3d9a57230eadb4c9adb4b3c3e949751",
            "433621acc69a8fce86dd9b77e7820b67",
            "47e0ea64f1c777a6a79e223ce27723e6",
            "6a4241ea1aef6b7722e5c5f7245aa7a5",
            "7f5f4f1744db0cfa345727be75237051",
            "9278414da53390bf54cbbea452240a33",
            "7467fc45e24ec8d8b9023385c2a6066d",
            "d8b772eeda5f5c0c012c778e4e800813",
            "6e9350b6b9e51fea0f713dbf0c501b73",
            "1c9b433c5a51ee875c4765b7186dac23",
            "31806c6194db8ed21ffa9de149925c51",
            "bc0af849634a4190b3a33614672f0238",
            "387bdef7855958b4723eef980e8e5ea0",
            "bbdb10a9080a237c186cd234fef7239f",
            "d7190a742c2a7b9d0c51ddd0d28be2a5",
            "909239426cc5cda89a6aa43d6286cf4d",
            "0307babdaa17981250b6ee4e99c340bc",
            "8a4499b5701cb6f55c1714fdc9431302",
            "f27dae15fa3cc37b3ca399ea44e1a778",
            "2961e0dac0c605827722a42c0bba86f2",
            "af88153aeda9217c4829b8575210264c",
            "9202ac6cdacd7bc8c8a0bc143f599d10",
            "e11e6193361f00508ab94e588c9e109b",
            "2386819fd06dc1532fb4c1f449bc8f8b",
            "9e602df1167d328cabca2a0654e34db2",
            "5e928546c0be8a5a9521911c2229e760",
            "9315f894cfabeb8c1eca96b60fd2051b",
            "4915cf338e679aee9eed9d9436c9b681",
            "fa0ba05817b2525c3ec05466e790ea20",
            "c31e46a74fb556fd7b53f8f6b186d5e9",
            "a0c155954c42316251dc524363458778",
            "6ca83021cadb6e3e39b86792847d0c4b",
            "6bb1de38500bcc28f620c111230de552",
            "2cc7dfbcb2d9bdb5b24c6c5801d5ccdc",
            "d1e96978c6935ec01d995b1b8e4d8c33",
            "155a7e810563341e4d45ffc43353cec1",
            "c98d4d6a4c1f0235ebf0a8e473fec1d5",
            "81edafe91b3316ebcb3dc9f2daeae766",
            "07ae4cca3f90a49347ccb5c1a82ff46f",
            "38dd1cbdd14d0d07bc036052e386835a",
            "e2ad76f2326fbc6b56a45a56c59fafdb",
            "585901d295f1acaefbc3b6b65f91b406",
            "050553d40196ef109fff37cbb40aaf28",
            "f8498f3e88567576560ec570e520b0a2",
            "8142edf6a82c40093893fa3c5cff63e8",
            "0d858ba37c2b615078a3565d90947776",
            "a3bd8070b8eaf4762d822c478d14f625",
            "72f8e0c7f10489d76c74b5cf0759dd66",
            "5c5ed72ea2b0a33dff8cddd5320be808",
            "fca0789e7891cbc0583298a238316122",
            "1966821685de017ca452fe8eaa78edaf",
            "3922597442b9390a7ba1ca3883b67504",
            "4a99551e52464b34eb072d03611cf5bb",
            "c92b76c87921c97e307ec1c62edb5436",
            "710eab25b5a7013dac491a06a6224a98",
            "e7988ae8bf76b93159b9279d0e4af5ce",
            "6e04b08de7ad7537efc830f19bb18670",
            "c33a31d3370e5cc2a483121c12ad4626",
            "570f6ff5228e5ab43af45555c8710998",
            "4a51d8ff409a3abc111a7fd0effd4cce",
            "e15f8101ccdd8967b487a1630d323cbc",
            "e98758cb680b82176b4c2e76b99760ca",
            "0d1544c47150203ad7e70fdc5fd909c6",
            "19d47109e3c9e2c1423eac228aff27d1",
            "4690d47c84c9df030148626aefa32980",
            "e3670ce0c315396e4836d7024abcf3dd",
            "9d2cc28b06d1a39210dfd5ddb8681a01",
            "964513a0d5aea1edbefd60fe03748f72",
            "567924cd49fe3e21f62b20059bfc0fb1",
            "cbe7f61115d361120e68a24e3ea29423",
            "519b08137444cca2e1bc32b0b9649313",
            "ca2269a193ca0bd45e48f334b39d3b2e",
            "0ea0ff9585ab2ea0d8c68804c3461478",
            "2071a7063bb7d9f28572f21e96bb914d",
            "777a96ab44b3f292d938fa51f6d69b7d",
            "50ceacd57ad40e3f31dd8491c386eb0a",
            "b4ec76193f4185c65e404e74b46a6130",
            "1fa533d7b4d484030a70d1714efcb667",
            "ef63641dc70199352da019c6ce434ac2",
            "7600467837e78497f84ff3698818a3de",
            "e80fd816b2cc6545d5d145fb8c2d57dc",
            "4bea9e07f447fd088811cc81697a4d4e",
            "db1cb82ae1992c9a4d2eef554ca0326c",
            "6dca8ed32379365fc34a69b0513050ef",
            "539a4ba7e9fbd80f14780f0d49c15786",
            "d9248af0840e8fb2efa11158d72c329f",
            "c6d1c47900b5fcedab484fc240c6038b",
            "d649dfdfa5a2b9b1adeadf12f0104478",
            "2dad2ed20919f5c8accbc3c74963568c",
            "973c8e1cde556da7f2217ff3e60855cb",
            "2adf2573209b73e07710ca34ef56d48a",
            "6ebac25ac7d26d36a6d06ef3041950ef",
            "3f1ebbf76a2367ce0e348d892c689ccd",
            "95808691af4d5e94174ea093842542c4",
            "4a005ddf639d81de95564c9efbc99f25",
            "bc29f781916d15802b8ee8a4174cd4a8",
            "35cf62d08b57b6e92d0e140bc04c1338",
            "1e0c4affc13210078ecff0ff7a1b5218",
            "85603ed3fd116a4dfb8fd3e1666bcc0d",
            "599bb786c6addede8afe83a4bb966ebd",
            "94d385087f474ef241fc6cb1351fe9dc",
            "509d133e1aea79fe2c0b4b3ea7fc30f9",
            "3c5b7547a2e8d8aff5f81a0566d9bd5a",
            "1897c9384cc3df249997602f8af7f558",
            "a2a2c0ef7c0882f59480b1f627d5561d",
            "4c74a6dfe236c738ffc02fa3099e8e70",
            "192d33c5c607059ad56792ba8c919a67",
            "61af12ff8323e751ce1f5d6c6e0065d6",
            "496667a4e1fe41fa3a8bb4da2503bdf7",
            "7bb7055099de1ffef9409abe20525f5d",
            "bdc3a40e4516a26037b4def8d951f499",
            "ebaf0d473e5fee2cc08b0d2005ed50df",
            "c802efb97b8e2fd21b85566ec54361d2",
            "fa0dceb54f3eaf595787774fee99a064",
            "8968d6abe5e7e22654a1afc246920fc8",
            "379f74ede7ad8afa97e961269d82f786",
            "af23cd229905a52f702f30bcf91ea7da",
            "b5765ac32e1a8f42fa7ba4a7bfe8b678",
            "e4f037a7b0481cac2b28293cd99a559f",
            "53dea0aa596b621e6dae2362974728f0",
            "14bc8ac5fac2171689e9418821aa5ea0",
            "ae02293a7ee92a4a0c06a8f086b4638a",
            "7851c07082e25771a4cab1bd8bac2dfd",
            "64023b82cf676f7e5fb200bfa431a7fd",
            "ff0f68eecbe98c1f667f618a568af634",
            "4a5734a10f64076f592141a4ca0d03db",
            "26f628ed34122efd13f7ffb96fb6ec3b",
            "30c531e90b92f65fb63ac94711ea3dcc",
            "43cbaae83c14f14edb96978219f4cf63",
            "ddd13fcb035b1b1df4ac0eb479058eaf",
            "1de9a5e2ea19a724e8917330088b5e16",
            "220bc40a64f6759081b93f6f6ee3782f",
            "efcce1c8f8c7b18ffa9c63bf6a2713a7",
            "791e18f640d916175ad6419e8e9ed4f1",
            "67bd21f0917512e3a1f287c8c1fa561e",
            "14f73f5c042404f3d8e8453f11d68853",
            "a7275b23aea649b26ae1d4df7e9bac6c",
            "0ebcc77dc72360d0eb8e9504c78d38bd",
            "b1c37cde04d5d3283ef3cad0176c9d27",
            "1a8a30ee636fde1dc4b32f34e055e1ff",
            "064e3a5648fb4a7f911155bd81f87fd2",
            "3071b1b0c220704d6abed07d1569b5b1",
            "eb530d951695112cf80ff651371ceb38",
            "8842a8e539205634f0b5e3ad5a597567",
            "0d488d1db72666b71633f4297f66cb43",
            "a52352f7909248b6d6f17d1bd30495b2",
            "d41a6271c82b1d241dd8ad499d05a102",
            "304013422015403b7c671cdfdfe43e36",
            "18257da6d2689ddd66148d598c5c26ac",
            "f11cbd9831abbe0f521d62f676459889",
            "02139be04ca51124f8196329aafbbaa8",
            "c37d35c762cfabfc344fd0d0db049c08",
            "86b122d4358357d834a87ce618a55de0",
            "4dc95e49472903326631b666ecb55797",
            "a67003ff947fbf48493055d356dea8ae",
            "2e0bd7febb1d5c28e505dc1e9d16491a",
            "804650918a93b7939a3ec89ec853a3cd",
            "70e670ac677d67ea9a05938b82e6db4f",
            "667415f5c56c6edc92966a1ac6fe6a99",
            "b77472733c7da40915a17f723d9a5f32",
            "01ea80e21848d517ca100653dc42ab02",
            "2745dc25f46dabfac0e077f0a6af9298",
            "fd26c068b86b456433f4059a72e9e9b2",
            "4833ef6aee54b1724cba442a81583d78",
            "d3779a48ccf8e469f915ffdbc55f6e2e",
            "245b8874816739247996a4a27084ebca",
            "2473edab3370ba69b2950c8745117532",
            "76f7efa44de06b5e175143c335350228",
            "0a79255182239070d0f26ad745e2911f",
            "117065a8f726219e87756d33ca0aee23",
            "10c72e9e6d5852e3a68b7b0c128a4c52",
            "e4deb67de9861324ae770813b7af9b93",
            "ad22b2b03744a02d38574bff2e0f90f9",
            "61e69b9ffeab8b6e0ee85e7aa2379cba",
            "ed707c8d30b896264ff4cfc8cde0e011",
            "11aa94b848bcfcdeb362b8411ab84c58",
            "7117f8008041a5440ccd4c64a53e81e9",
            "f9a40a4780f5e1306c46f1c8daecee3b",
            "9df91eaee825f493b42f74fa4e295dc1",
            "b0ca0057cfc560d260a8f7608ff4fa24",
            "7f8ac8cbde1963c8c1c32e724dd8bbb0",
            "1d2306bb289f9a316f2b6b660af4adf7",
            "bc96125bbf8c32ddd5b2a59c988c5f44",
            "05da130dc36f4de3a6d2d9fe6dafd157",
            "ec01ec0bda13bdf91bcfd245ade69cb9",
            "055283e9465cf3c0cfce8c0f450453d2",
            "22e50a4f751378c2860b09234e4b51fd",
            "f285bd411d8c81d0328e512573bab971",
            "3352ac28c3e39d1c78ae0e7d7bfb3945",
            "a7c56cd1d5295952353bceca99a38db9",
            "5280cfcfdae1fe88078000ffebfcff98",
            "59917a3906fdf51b1b359fab990041ad",
            "922b617cfae8619e16a01cf5b5a3bf5b",
            "9a7ae1d9923fa0ee733d7af063f960b4",
            "c5fbe42866c81a3ea15109de71fbad0b",
            "66a660dc628750386a9ad031aa1130cb",
            "8f7b2d9100577f77aa8fbb4f51c0366e",
            "033a63e8de36062e58f7a96ab9e37df3",
            "fa06fdf0404bc8fa2f0e6ab430c3388f",
            "888de1e3f6fcae43c9dc2b01ae48bfd6",
            "df8a7c90965b9ec53d859f5cfc8f4562",
            "3286473a0c49f4cf9d0ab976aa7f879c",
            "7e6d9ae4ea1fc87eb1be6816f0e11d23",
            "6070d2e578e07843c2206e1a323311cb",
            "b247a28d5fe313d02ba81460fb3a1d0a",
            "c24a2750527743f9ff104e0e0ad0397e",
            "34a56f15b6bc6e82061f1b154e977502",
            "79e4f22a3d1ed1a98d8e43f0e6ec8623",
            "fcc5b727bd30570766d15fb2d4e4ccd5",
            "ae306dc92ae6dfe9049d4b2177bb932d",
            "cc9dbca43c97256addd5438e19f4592f",
            "69e83866ed86444c1bc3096a5bd34886",
            "42cc0d06ef1d9763ee036076b69d4ef1",
            "bc23fe9d1bde95e835869ab11cb61f70",
            "6a600c968f4e6a843dcaf600c1694e42",
            "c322ba54334d3a5df53d0d82159bc4a6",
            "fa975942e13f4deb23715ac0348a3a3a",
            "8e1089317d026f26b946f429055eb787",
            "a1aa3bb9f997c9d64b2a0997c57017ce",
            "a65ecf7cc78540d9a0df3c4fd664f999",
            "6805f5e2c97404f97a7a2275cd251358",
            "341647665fcfc7238bb222033097ae2e",
            "5ffdd8a20b72b028275fa2089a7dbdf8",
            "c997eeadb2366164bfe7866b4ec110f2",
            "eb360c8a611bad9146666a1b4c34b711",
            "6201090a4cf6e501f9f0d7a1c75c584d",
            "96d32c3bb5f30c7775a9ef743f303694",
            "be1cccf322d8fa6f28ac856d9022b811",
            "89d52b69c7f7317b23873870ef59ed06",
            "d828b6103eee8e2399e1b7c9694f313b",
            "e2065cb56f5533494522c46a72f1dfb0",
            "6d5155000c7d6e63cd10641aa52405d6",
            "67843bc0e7e7b09cc369beabf05e9d30",
            "b55d83ccddafa95f0dddb6e63d593cc9",
            "de962aea086d4cf4a600e89b48ecd742",
            "feb5fba0cb20b5eaf1e50cbb90469bdc",
            "6186f3c4cf7ab31f87354cd57ba29d65",
            "c83f28896b96ecd8cb883814468e4a8e",
            "3898997ebb08c6affa158f767476437d",
            "9b4192ee086949047cfc64d86d895c2e",
            "ccd140d15f7473c1efd9906903c1bf93",
            "6b7f3bdadccbfffd35e7a77318f58e58",
            "df31a79f21d8e6eabbf7a3bee2d53b7e",
            "15ae4f385ecd244498d30e09fd9fcbe1",
            "51743be866c8dde0aa164046810132b8",
            "245d145c0b2b7fc0a7c215e5851d7086",
            "5ecec9891d5945cfb7b85fb1cbd53f35",
            "def9f6a41cb71c6007fafde85c70161c",
            "17aadbe67d7b2c0f7abe7c43481658d4",
            "49982c92be73039f4b8e50e187a50809",
            "739033123722906a86c2e387db18652f",
            "5bc1ba285ca01627339bed60dbc2d82b",
            "ad8330b929b40b16a1a899afa83f7b16",
            "94191ced222678151cfdc494b0f6d73c",
            "782708bd1c76fa10903afb651d80109f",
            "634b92e752518a1b9cc9814f7dbf8cd3",
            "8208dd730fb0a5670e2817252c6de728",
            "bb12fafff11da2d29b89fee75f3419c8",
            "b56521d28ba6dfeaf371ba1e439b3c85",
            "5ecc55cd504f6fc8dc317e21b5f853cb",
            "828f728321bf291985e89ec1ee2fca73",
            "998da83ce73172cdefe02813080f9638",
            "5b072bdad20c1e901ebd765fbac0b3d4",
            "110bef66bf27ac659a673cefcf422d7c",
            "d8e9d162af6a8988dc88b7b4c3b87960",
            "4d4bb8e87dc0b08183a06d2c11e8eb65",
            "c4079f87c25d61c9ef9f56b7784d231c",
            "2efb92095a8210873e37469511a24586",
            "7d5d08ad4d46e5eb812e642dac9537ec",
            "2c56da50be6dc5eaa4ffef81871aeb93",
            "37cd37bbbb72d0696db94d11f1ce1d7d",
            "13ff88e3d4fb3d45832ee77439ffd47d",
            "f93a58a90687d1dfe7c8b7c11a27eb4a",
            "d9a8c56824cfbe66f28f85edbbe83e09",
            "3e63b5d036052c93548f6e2fef8b1944",
            "56f99353427b0746dc3d51edfc785db8",
            "937f4dd243d2866be47cbfc95c01548f",
            "30b4a92517ace5825f5944c8a794ad3e",
            "53c81273af055b161e83ebe717546ec2",
            "7d7b04e989115e193107af57ad662dd2",
            "b8f3f4b4c8f1aa6d250938ee1ab11b26",
            "c7423300e9079128ba3bbfb857b6e0e8",
            "11b0c235292bcf3d4f0e1ecc56596f34",
            "0394ea68951e3299bcdfa75a097d7c11",
            "605b4ea331332bc9640df27b3b706edf",
            "e3a72c791a69f87b05ea7742e04430ed",
            "f899ec5955befe6e3b83581c61f012cc",
            "0192adec1a16b0565dc3f70b5d235a40",
            "fb2e2f549c667723e7a29f7fafa77e26",
            "0f701e22c252a24d9449e829b0c21666",
            "eb62379f79b5d6073108ada4b3fdd9e2",
            "54d0ad877584296abb129e2e4f60ee67",
            "16a40772226cc46e575d4944a43fc516",
            "95227017a174dce4f57aee36c3da0483",
            "c374bd2c53b772bfca08701131a54e07",
            "9f52baaed0f3fc7e126de9391b60fae3",
            "1b36d70be55dfdb6352b18d230bec70c",
            "e64df973463227bd885f326e571e0065",
            "744878fbdd26871c594f57ca61733e09",
            "5325d60434ba765bc51133c9735e8152",
            "d3e90db28329af684586b3cb948bccd4",
            "dd058670b8f2cbeea688604fe06dc647",
            "ef64065f4e2e0d6a2ffbde41406b049a",
            "cc5014a350a71553dac753f04774fed2",
            "e41607369fb4bd52999066adacb2e9c8",
            "cb4cbd9e689ceccfa3ab546bdc5e11a0",
            "824a3978b4da3f49d95065d399ecdb9c",
            "e2d8381557885b9b7f8cb36dc1fa35f0",
            "2dce41d1b98c08791f7c9dfa1ff610fe",
            "9ffa28d75c74948059e5c1c527d3fc48",
            "bda989b1330ba4f420924a5be0ebcb61",
            "4ed83314fe6d32ef792235a60effc851",
            "9f2cb637e8226c0f3a12a93b598e6629",
            "b45c02c886332aeb4ecd61b57369b5ee",
            "99607461cdb9c26e2bd5f31b12dcf27a",
            "2230f8b6ddf0173bf64bff7451c4552b",
            "ecb287ff763c169694f682af52c1f309",
            "5d7bc19edd34d2ffe410dad0536c7fb1",
            "4c27660c7a49ac070107b21959aa1daf",
            "c00d6e3ce5a66ab98c80b33ddab0ae40",
            "c92a10324374fac681719d63979d00fe",
            "1a78fbe690617fd627be3fd84e7728d3",
            "4055ecc5d6d4e6f3f25267b292b5f441",
            "3b4416c66379e37fb949f3f1698240b6",
            "f77f6be80cc879614f1069bea92f70f6",
            "9ab71af6dd3c03bc46f8fc7e33af7556",
            "529a2e97b961c45c9eabc73693bc8dd9",
            "55c11616b1aeb14d5166f02b07e17eb7",
            "01b60607f66509598b62e80b5aca4a42",
            "c4aa656b2aafda49bbb6b79b2d84fe4f",
            "972c38dfbeb32131135065a4925e7f6a",
            "a2b0415789ad25e36a4f6cfe2306511a",
            "56139bfd9268ad4686c1fb9eae262327",
            "865934fd8ded47d48ac7ffb21e7608a0",
            "b79b5da0d125ca2d17d039a8991d8122",
            "424216f40403be220c82bc2eb2de3d32",
            "c05220ea9050fed3b51e4f30353967ad",
            "81688f9a586ab725a6ae9e0af361ca5a",
            "11331795a93df7f15e5e553e9c36f329",
            "71994cbed2837c2389f5f2a45e08e02f",
            "613e4b45be59f778a80853ebed1031e7",
            "e2d083a5fd066b082d93042169313e21",
            "b7bced9d2f572535f1188bc4cd4ba4a3",
            "aa7478f6c8032b7d8cddfd8c6478291b",
            "722fd8c97825bdea860322e28ac6dcbd",
            "732dc452e39d99b6831747b7b9a6d560",
            "7fd804295ef7f6a2822bf4c61f9dc4a8",
            "77b45b4cfcd299117157b0845f724f20",
            "b5e91b83fe1bd73f4955fde97aaf35b4",
            "2d0122e6c17cdb89f8eed4d75b5f5eef",
            "55c9b2f18ba572204b385f63b442dcac",
            "a48251d4c143adf4d4b657d5d82ee0ee",
            "c21e6509b9b37cbbcf04a9f7b81efba6",
            "ab04a1078d097cb5296426ca8f844a0b",
            "580a20b10244bc9c3e2e7b05858a3803",
            "860222b8c6e4f14fa3f2b9d397336091",
            "95972715b86371930ec2536bbbe4100d",
            "dbb55fdeaa47c1658ef50641ea0ed407",
            "e9a18f28b9d4746bfbb06f416233810e",
            "d0aa7a3768a83341474a7fa601af1a3c",
            "0bed94520495a2ea92b476d2725d3f21",
            "74c654f9d970ac0b5f741f36af9efb23",
            "15797f4206e7c7b25c73035c2fdde725",
            "977e32ff6fa7cf5de793572e0734bdc2",
            "f6a673f09493afcd8b129a0bcf1cd5bc",
            "5f95083908c5810febb519fd844b7271",
            "373d71f842ca1c1bff5a1d8b1da9f1b2",
            "72acff72400205c3cf210c5ef62e53b0",
            "e5746a85024275218071f2c19780b292",
            "9c7dadb6f7c1eadc8bd2a7923d51193b",
            "1531beb762df4029513ebf9295e0d34f",
            "33dad8c4bab30ae86afa47be7340d072",
            "70210cd7158547148574abe01f84724a",
            "37f8dea22b2424ff5f7cfd0821dafa71",
            "49d1104125590ed50e93fb9edaa4e312",
            "473b0fb9b0f15f3565da15cf50d5f8ab",
            "1d4a10cab222cd808f873ac9bca20e4d",
            "4fbd11aeadd719c8cf9c6d501b854755",
            "882303f7aec5d67ebbefa827b2349fce",
            "c46f057dcfeb142945bf6d0168444f05",
            "73b75d1fa4f1ec7b5028a7f79ceead33",
            "ca1107d15499257ea9656e3fe4b05e14",
            "c3106312520ac69dd49da8635f40b222",
            "72f7b281fa037a722fa03064d5d28621",
            "1bf3db693e8c5b2606a1fb55a7cc7889",
            "53a2a74a17882ab5b689b827d2392b75",
            "0f668288da61599586da12893a204dc6",
            "9930c7f34f9b8d189d3001bfc34a4a95",
            "a92c57d0745286f7b326560b0b15f6b0",
            "286982e550e8440e5617cc0b2734192a",
            "e90ed7dea9b52e3082f5318a113024d9",
            "bdeb208b17eecdbe14697a54dd697bbe",
            "9215df3469ba7ffa2e6a6f2c209d19f7",
            "cef08af3091ab048fb5d435c636761f2",
            "9beefc2c869777cdbf569d6bfd2a3941",
            "8b818a26d29d3a3e591c506b95ca21f2",
            "b49a6cc43f3fb69eeadecb9b8be88924",
            "30270b7a8f7d456b15e65e299b5bf199",
            "d2a452edff079ca6980edcf54cc49945",
            "d59f4df255f0f6c9bef4d80e5c86aa69",
            "0da0b8b06e9536d42f38dfc6e0199dd8",
            "1a904391b774a1b21c3292b3880211a5",
            "5bfe83dd817882aea816d95f1a26662c",
            "b4a528955b84f584974e92d025a75d1f",
            "9a7e4d07ef8a46f82d97b848585ac16f",
            "c8e52f8a8397d8eb6aa25ee70f8ff242",
            "a4529111e15383ce19f60f2b7af485d1",
            "45c88ce5b56eaef4a92799bee582973f",
            "3030262bb840651cc64a9f6532e71b10",
            "9bab2897d91c481b2ed956053ad61169",
            "b074030ff7b7e8e6e540421f1a6b52cc",
            "42df59af01e92da78b4b264baf972f8d",
            "c324a7232473e0661fac78b66bcf87a2",
            "d681e20d6096f79b3de56e5998e40222",
            "eeaa74307ce68b8d90b4b51f816f541b",
            "d614339d89e2437150d88b737a702ac6",
            "f9c807a8a4791079d1271d8b69a44fe9",
            "3e1fc912c628b104feae5b6285a67343",
            "3f37c010783748f8e8577f732d74054c",
            "c9f158580bd652c5c83f6b2302604eb8",
            "4f7b884f2445ef08da9bbc77b028722c",
            "9098dfebc7d592d495092db450bc1a44",
            "401fd95bf6799fe9dad285c30f898cac",
            "486bc20870daef007b7114f29d45e0ae",
            "837f37cc2b223ed4753abeac3b41bae8",
            "17e7296efe3b34b181613b81b3e72ddc",
            "981acb558042a7922b4e84a932d1f53e",
            "98510e7abac2bb60e95cd6947c93acd1",
            "15f555692efbd3f524d15e4f1618f225",
            "de8e1f5db2ee476dd15bcc7b0fa857d3",
            "28e6ec5f1de9e8bdfe2621e8a37ddae2",
            "97d0145823aeb8ed80617be62e08bdcc",
            "a39831e0a74dd51a4157f216f3bbacd9",
            "1c9c7524526817770526fa185625a5c2",
            "be414c84abab7df6a7627270adf955e2",
            "5b5502c22d760d06c00553914bb0e08a",
            "e873fdc8abdf50ee02f1bcd854f35ad3",
            "d0f96bc8fa7d4ab1c1532ce71bf6d651",
            "924689279cdd86131696ea8b08a62238",
            "87e74857d4ebdfd478c1aebb0521f96c",
            "c7410c88f7db9413f3e2768f1786433f",
            "5e0e5a60a9804c7d966742753873e3f4",
            "373217248d0b26f831106119bc7f0dfe",
            "15d94a671bce53a568899385491993bf",
            "7f67577d0820859e03a7ab640ab2096c",
            "ba891559add2fe3651181848a4999493",
            "243facb29564e7b448834a7c9d901201",
            "46ff3cba762a335a33f219e81f3ec1cf",
            "543ce2427c4ea8716111b1b2670ecd1f",
            "5d3d1e378802207ba7943fe5fa2aa0e4",
            "220bfbc05591c2e0bb5afdafcb39c478",
            "8839912ddd6f88acc25fa7c8d5882cac",
            "57e52bb817a25aef293eae2164f5a7bc",
            "b9e40dc17c3ae621dce81b33ec9567fd",
            "fc58816e64fb42595516071652d813dc",
            "cfa6ce248821ba971db98349795df7c9",
            "70b627d043534b7ee6ad1510e56f4e4f",
            "d7ca5f91077df64c880ae01502b5cdcc",
            "92251e8665e19be62c86ff039528e16e",
            "000fa36b364ad0ceb8d8ffdbeb35de4f",
            "345c10f06276918a4c6b0f410fa96793",
            "f7ef90c2756759f461417084b6983074",
            "5a6958b76854849f591a1b6893809e6f",
            "1195597734c4ac0250dd23b1e163e555",
            "f902866a20efc1aa4c8090f495d8db92",
            "ef7380f49a12338f2f926c9cadc6bf7a",
            "4e2c668e3aea6e98a0af774d73404b7f",
            "24e23677d6722931c4fe84d781e8e32b",
            "c5e615abe31f19799c9d3a92bf38ce26",
            "ce1a44b643d3f634f7c20de4382fdc82",
            "b178bb7087d3849422046ebb15c7eafa",
            "8b630a4b6a4e29a8ea741899e55cfaa2",
            "7c90515e9d7a3ab02bcc96e96ffcbfbe",
            "73394a40d1d7697973c0699e69dae7da",
            "874dc8c9cd9006d9aaca0cb5ac550812",
            "45e64dd30c38dee7251b1c41bd70cd02",
            "977a0455c33bed46bb75a876c54f1271",
            "af98591bbfa97f725240d5353560ca60",
            "00a2aa5c43a94f625ebf713cb5bfb091",
            "84ccfcce45fdda705091a9de5392462e",
            "aff331fda99542df6f57108c846a9772",
            "d4ff76af57c1ebcc7eca3807b9a431a6",
            "26d76d7dd692e6728db5df09ad3f10d2",
            "e6c941ae4ee2d6c175735845db49858e",
            "8539dc46e0b9d7ddba37bc2aa3a93f8f",
            "8b4832d21046bf84e7963c8a410176cd",
            "1f02095cad26b661db5ad8b5620fbca8",
            "27d95d35dd97211de1386fa9c312fd05",
            "e19b597cb9d91e83a5516f61d172b9a7",
            "6c3623e117cfcb8c0de73061d9732ae4",
            "7860cc14eb38e1139fffc19c4687c132",
            "442053db63fa708cef3f23aac5eafe80",
            "786e9ac2a91abc685e169b38d7e19df4",
            "ca0ffe5fe1b4bc283b603c3ba521984a",
            "fb598cb494b6e98253995d800fab908d",
            "079d11eb24cadc4807b9cd1099164201",
            "41884c79b3339f66e8b7e121f785d641",
            "a75b5ddfe7a4096d36126bf474e2073b",
            "8ac9ed23aba06fec4d4adc79dca58301",
            "b41541dc6c37dc2250e1bfb56d192cee",
            "d7fcc6c0227d133e5ca084b40d90f10b",
            "4bdcc4c7f65f20a8e986f9fc61711100",
            "b27152e13b5ed9b61478a1384d7b9bf2",
            "6358cf387cecd4838c4927abf5592443",
            "3917201ce7bb82777c41c10f9d5c7d01",
            "a0417302ec4ffe17a9a455df3a31a30e",
            "db18edb52bdf8d384e10c5ffb7b59a4d",
            "4dff080a35dde124f1c3357c904221ba",
            "e689ea1023656910d6015e0db565af57",
            "cdaa8f1b5b6426c416e6b41ba0172692",
            "363a02c73da67ccf92e06957b78480f0",
            "db04e3f6d3a99b8d966f458b5ebc70f8",
            "dab9862ed07a17d8b935ad16b29c630a",
            "0012c29be9db47c8cbc50bfa2262db5e",
            "b6428d428d5255d719051b1061adacf2",
            "32ce3337fc6684b636381084d6932695",
            "cb4c8bde59d45a5dffe9af5f820893e8",
            "6b24fbae4718f1ad74ce842ff4cfe844",
            "db28a7b71e5e195d5f1d064c6e15832a",
            "297156731251423c59098604dc924730",
            "a8ce2d6f9cfb3a1fbd5a53e7d2e7c5dc",
            "96b95423b2f46cf8a3a4b36f86d94e4e",
            "b826ef3592779ad4e963da8b842a1ade",
            "fa6f123e606564fd3db2cb6eb6d30af7",
            "e5815151957be36ad2085b7a1a02c5cc",
            "95580e8d4ca89174b081259cd185ee37",
            "d6356bfe7b2c2e1d34376db9350dd971",
            "42ea16295f421414196c510c6e17e57a",
            "88dd34ae15833e7f183f0d4a71bf63b3",
            "ea70bf53abd4bee38c698ca7476009e9",
            "7f618ed9c7b531139e1c570f8e1ea0bd",
            "8b5578656a9a3c888090fb81c2869c63",
            "d808da82f9b06b8c7cf8af2827f00b65",
            "55553bbd5c523aa6ab3ec19784dbb994",
            "7985d71628953ea5ebfd3550152da161",
            "0561f407d320127dc15805ce7799f477",
            "02b17759254860053f37e8ace41d3321",
            "5eb13cb69b6e20dd7a42030f5936a9dc",
            "8abb3d363a2030485383c43306e690fa",
            "57fdcf1ed87734d95600e3d2eb704f5d",
            "d9f62dfbdf9fd18011cd26aab072821c",
            "4fe24c69a70b6fe826f650725911b775",
            "875ac8aec9f582cde7e04987d800499d",
            "d7a5ab098724d619aa9026c895e6f7cb",
            "2d6f787aa177215c3ac1e15c824e7891",
            "2f1886f43e2b5b53dbe8cb488607f680",
            "063657998c5763c61604c64a629e8f10",
            "da4ceb5294c1b5551e62b98af87fd710",
            "385a7b4bf124af1c9fb2050ccf954b71",
            "ea195b994a7a3569f93d93ecfb4ccc53",
            "778083970d60aaebb2b7ca999e658f47",
            "1bf6834d84288fab22900f30ee947973",
            "ee5e9175bb2566d132e6296508242934",
            "1b61dfc1674456959b77ff289a6e0160",
            "86d0026a550b7fe25a4e5a631f3854af",
            "705795e6f3ba074223607c806b553745",
            "f7c2a7269b0072a1da3bfb77eddeabaf",
            "f7f3350dd5bebf82e3328c86555c6a77",
            "7048362a3d5c97e3eb005839ef9c9f39",
            "3b5a55ac0ee17c35dfd7987a5e2863b2",
            "1fc87e47c0e943e0fb0c36bafbfa8510",
            "cb917895b0df4a10b6eb212bdad30845",
            "0e380735316ce024f48a0bd9b480b0e5",
            "86ddabec0fae285894d0068c24e54140",
            "d0757d8a6c8f0b30dff4cf92571ea333",
            "8daf0b452ded75566eb62edad1c233d9",
            "9abe5ac77ed8ba6ce5ad54dbef897bd3",
            "84e3fdb767f2e9f9a4a51765c2d0be1c",
            "9763e8b9b9f36aea5d6211198a4c4805",
            "039eddaccc4faaadd9a18a4e6915c0f4",
            "b6d3a375bbba4a5eac834dc08ab9a2ff",
            "46994a3cd8d943d03b44b8fc9792d435",
            "90fd4f88f588ae64038134f1eeaa023f",
            "dc7d25e5aded24e41d9485dad98bedb6",
            "6b2a91552bca604612ce68abe0ec03ff",
            "217b4541c73ac1dcf030c1b96a5de141",
            "9bc3fad18c700d04d68a27a547892384",
            "7570284063f2de77ff3145e5f321f2c5",
            "0d5e0d7a42cf81c3e1b28d064b0a18b4",
            "3ac6acc35628babbfef5ed05dac14f13",
            "4bf9567c247eace1729f477243404f8d",
            "bd818ddbdf2e89105f5a3ba6f3baee79",
            "3b0e7e7bc308ae8308058065ada8f2f1",
            "38c79368b6c95bb02604848f81689f7e",
            "0559ac4fe3a97f957a7d53d849e2a465",
            "78ef75537381ece04f3f80e9dba17544",
            "9d637158fbd711f2fa170bb14be98e89",
            "2c1291031cf620e993216c9b2939f95b",
            "0957aacb6e589cbcccf44209c357d856",
            "450c1562367411c591cf05cb2cdd65a4",
            "afa806680e3179a764da5dc370cf9ee9",
            "ee47b7b3576d85c8f7bf019c5d5f56aa",
            "3609f0cd67805f15756230d3c29b8ad4",
            "5040a571a1e3e1cf1864d82124483d47",
            "a272ad39b92dbf24aac514e421915201",
            "0609e44df0b5a40af7dc0ba6a5298145",
            "30de0034629320acfba465f1366ea149",
            "0aa9a864c9c1a2e19d743337fd54cfe2",
            "3dda95f34121ddacdaf497f34f830da1",
            "8e4f5e9144270142686183c3768e5721",
            "4e82d99325dcec23472abd15bf992274",
            "ffa507c0741084cde2c4c42eda5d3556",
            "dc0210a8e141a6a32b510349bf4820e9",
            "ae26bb04cc2d9a2f20563f5899291982",
            "0244ea88f8dcff88958930c52873a0b3",
            "bfd83e32f4d30f43c74f082963009d36",
            "681fca98726803dde6d79c01c2cf17e3",
            "a14ad54c10440cfe67211afa33637817",
            "ce865f84c9a69fc2b9ab9029f04e2b91",
            "bf5afff6a3fa6673f1771ae7962b42e4",
            "9988954db7e948f2625d4a6b05d7e941",
            "2cd6f5f37739899f04bc82b76d62a5e2",
            "669ea75f1504b326e37d132fec021ee8",
            "fb7b2e341aff60ebe0cf3ff96252c2ae",
            "3eea52c0af49d9ee6d12ca2bf4e558d5",
            "27603e171e287dac395b21e617371405",
            "c4a0de10881851c20e27d2205649d49d",
            "ca60b418faea3026b699b240b772f19b",
            "1c4440e8b6bed5e9b2ee94b58781d3da",
            "4ebe7c58bc215459085a6bb82be271c0",
            "82e412aeda3d74ec7296a20b030af815",
            "30ceb23162d428928067ff4d8e4692a9",
            "4ad2668f262af7953d3e31ecac5bc678",
            "ecb9cdda091856f2750663630c010b69",
            "76291d0fb3d3d6557a3caac779038ae4",
            "86a1b8c4fd7f42dbda33bcd62c5af463",
            "d1d9e083ab130ace0942bbf7250292c7",
            "ca83438640bf51e13ae6010e9eff4559",
            "6be7a3c3aa34da30da10427a7f1341b0",
            "6f5aa93727e658472608415abe7df416",
            "a4c07618e910823c1d3d95a2e696e75f",
            "0242ef26c6337793b7efae855add5788",
            "4283c54c8f2c581850e9d7a911308acc",
            "5e3eeac9a0c1dbd64c3c96d19897f2ab",
            "9fdfa0807792cce46c1b310d18ec4157",
            "ccddb19d94622d5ff1ee354d99d24d20",
            "732d11ddaaf1dc1180cf10f527e0db5a",
            "6b8fb5c57ba603f9d05f0a2b0dfcc850",
            "f64c7269cc72b945db7ff996dbabc091",
            "6b72dd59deff143b3737dffbd604da24",
            "51e790c237b7337bcbb6c12d06f4c3a0",
            "9633bf03e2f71f0d65b5f8d7853c3386",
            "16e3edce388ec9d945fe57b0952a9a8e",
            "54632a880c348fe70349723e4e24cfe3",
            "be93254234cdf6d9d10c773680baee31",
            "45125a74bec16dc22aece307fc34daf8",
            "5597de66a16afcf882ad99acf95f5240",
            "9aebfc06f0b674f43b77ea8728a51202",
            "938fc62f95a3fddf3eacfb8b94aa2a51",
            "a993e4d1d4273433570c872e1e7202fb",
            "11e4741c245a59ff81fc3bb579623aa5",
            "23f2888f614fe724d7a081bb89360997",
            "abc7403e31d2ea47c69a7151608d13f0",
            "41cd3edc6db758d9938bc0240087d56d",
            "f0113621c2176c376535c10385468f7d",
            "e280d6f55e8961a41ff252f4c4482e2e",
            "5034a5d62f91942d2a7aeaf527dfe111",
            "24608de526e9b37fa9c4ceaa748852f7",
            "b798810a80d302262bde090160623ef1",
            "26033e119c351a4cfea378963f21a3b9",
            "d05558fc3d4a7a6e30656feabd04f54d",
            "ce3b5506fc4151c97d646dc6fce8b1f5",
            "4278b0c6995b3641b998cca4adcb5510",
            "03a35f11b141400753cd02d84a2510d2",
            "ad73dd993655f6ebb4e1d4ba0b97e514",
            "f775ec264c01adf8189da19ec86676fe",
            "8a02c246fa04dc743413543bd8c9b329",
            "c9a4d044f11b1f058eb7d27c08b0f647",
            "624e8c2d1e7ccd12d4b42298eecd8234",
            "963859b79a359c0a74ef4210cfceb5d6",
            "a7bb607d0add0f3ed44ed92da154f79b",
            "70a1ff6de57f2ff86c7f2542313a93eb",
            "275ac327e657ccb68c137890d15f1fcc",
            "3a0936863acfd659925e2e6008d86750",
            "68197667db0818801a9bb210793be9c9",
            "748566616d43bcdd955aaff231c8baf6",
            "7bb0bb6558539fe33da1fa03c8f4f717",
            "5e89812b1d9a8b10464d6aa90bb514ac",
            "0b674478a9ad60b79d47bb4fdf7f6d1c",
            "440f05931953117e2972c6bb64fb3780",
            "46e25cb6cad862cf47a4d9db0037f397",
            "0de869e8de9dc7f79bca57e6c2f73359",
            "b8b5650843c584dea2e70ececf54d936",
            "5228a873464ea524571839e8fb9f9fca",
            "62b49a07a2030ab0b5ac536b15a1fa26",
            "e206926bfa7f09f2f95034e570aea982",
            "1f06f570f8720aa7959b43e581041cf9",
            "5eb00940f40b6e07fed6248425401e51",
            "ebedfbe2aaf14bc4a21ec1cb5d062414",
            "7d68e71480e5aae55e91f63a606dad32",
            "81465aed6e2521aa196151173d777a1e",
            "c1472804f035800cf97a5c244bcc3fb8",
            "50becfb7388671efb3a46ae4cd8e7476",
            "6e104cc2b602b2cbcb17ab9a3566fb51",
            "b19cadf175314c5125252871ec5b5370",
            "9d5f3a5f1c38faf1a6c5b3a8471ced5e",
            "79b0e6d5b338102c4f572e646e3cd37c",
            "68bc1f0cb836613f86d9c2d789aca190",
            "83d4e377294dd23fd548c5e304e06cf7",
            "ce2b18dcdc9e4f0e0f166ee3f5b7d8ca",
            "dad6a91d05a6f30211eaee6f1ab345fc",
            "806f6f9ec20b59c079c344cb5b3d753b",
            "c6dd57681abf5bcf67b70f5cbe61f835",
            "899d212d64f105b3edf6c92b6f05f2f7",
            "eabeed77c370a6a4258a6373af2d137d",
            "5e33a2bedd4743d532714d0a7fb5a622",
            "73c2ff59e7ac9af9597793eaf6b3b890",
            "f65df2f6a5a363ea10f7b0e4cca62613",
            "f6c4f1e8cdf15c2d238993be2676b53b",
            "cbea0546ef2260e70cc5b721201d3811",
            "d400fe064cffd6a23313872cb6b48cb9",
            "602c7b3fe8de08395b4a6d51e5635c2b",
            "095a9dba4997c44fa99165207be503c0",
            "6562a2c4889bc3e203ab869723a40f7e",
            "89526830b871d40d5628a6c294a4be6b",
            "31aa648281b327fca7492cd2e2e605c6",
            "4b400fcdf7d1ca627609098fbe88edad",
            "5b5bbd3170c560829391c3db7265ee9b",
            "4bfddca77ae8f1cb7eebacb53f97e33a",
            "b87015d12ebdcebfc79343d08cc61a7f",
            "761a0c714184cab2456d17bdfbb8d550",
            "f0eda84da0addca2c901c6205e8b1fa9",
            "772d316613da8c54672c54d3fb5aaa58",
            "5165bb7092845d883f5d8fd52d1f32b5",
            "579c87fbe1adadc6d76bf58e3fbead50",
            "39b0e2827bd30c2d9e18ef0f2aa5bfcf",
            "e87b870bc1c0e4b6cfd5424bab3b4d2a",
            "25ea7919b01217140b538ffdbfa8292a",
            "90060cdc89af5da15c1362d96de3d27c",
            "dce6b2328034e7d13a0d5204778bdbcc",
            "faffea4591461ca6592169ff7142734b",
            "3dc004bd7759a450cf73ba584b17c1dd",
            "9c476b4d3e82cac2065eb0c47ca27e26",
            "a4fa479123a50c7146ab332ab30f3bd2",
            "373c385bcbcaecdb13f73a49ac0716db",
            "e96476b30c86f8e63fa748252fdd9edf",
            "deb1335864dbe20ca2f9b94ad01c2321",
            "e4108ddd4a8a0b4303e87639081f5fe5",
            "4cf06252cc21d496e754ad7185d0617d",
            "652e50c9ffdc8a735970ca8045dffe4a",
            "bc2ba6bff2342259d8fb150b02dc1565",
            "14fed78ae1668c2a7719859cd2cb5680",
            "780bc6caf343bb06a4372c0821012624",
            "ead65985a306a4bf10dd7ef4aa2d03e8",
            "404349601eb751c3265b601b0ad09fbc",
            "5ab8f5ea26541c7a1e1a05503faa40c8",
            "a901a119aabd7ca83c52aa1c62d5667d",
            "09868ce16c276492f43fcfea298ddf63",
            "1edb6b9ccc627f396101f9142489c501",
            "78a2be025ba3d5a97c4da484ae38bc47",
            "2ce71be8df76b931613bbd4d71d5331c",
            "6cd90289749092cc17f5a6c0cc57b9eb",
            "d3140ef0330cbbe8f69060fb76af20ae",
            "efed20fb8d5ce92e67a6db55a48ea24d",
            "c73cd86915025bddc163f03b408ad741",
            "4a4a9668773d267a456c628d177aeafd",
            "a7ee9277005bf253a792ee381c20ad7b",
            "a9476e9cbdcf818c2e63b559d093a4c1",
            "ec70dc9290cdfb369f6e1dceb8ad7646",
            "f670ef5d2d6bdf8f29450a970494dd64",
            "58eaa69d86c0bb41c0f334b95b6c8cf2",
            "25fdedc9ee0000b836b32b7e7fc39a47",
            "a464ba668b3092e05b7ac75edc538650",
            "2ab2ee2d6374525563f11ddd813bed97",
            "7ce153eb935ea47a7f386ebde811dd5b",
            "11ce9e3a983196ef3ee0a096e134c95d",
            "1b274efbad3eebaf86610c29f406f685",
            "0c4795a5517cb1908b23b40b5d03b443",
            "6b41376b2c96daa3a6995e8e80f5a90f",
            "1288625a4bdcf1109ff5adca3bd33753",
            "f4793f177ab4614180f9e8e82afc4c1c",
            "4610d3ad662beeee604b93f5212d968c",
            "62336a5fa9536b5486663f1975859c81",
            "2ece78b66addd22b8552f3b218337fe0",
            "68a6b13af773d20f2e4ef172c82399e0",
            "b81dc9a8683da246537d137e949ced00",
            "376d34e02125175753a0e2e1c10f8d1e",
            "6baadc89159617043965f9e1889224e7",
            "5d2b707b6b4c9e8ab2c71a77814a4e7c",
            "498bce62bd2bda584246701fa0166482",
            "09923a2cb6e1990169d68e0ea3fbd2ee",
            "693ae67c39b1ff6df487d9c5213bb5ff",
            "8f30dd4ebab7f2425d0abe9a4baf8e62",
            "b56b7f623b480958bd2ed5094c0575a2",
            "c3e62a47c57fe4c458f14db3e935879f",
            "e0cbe3d9bc3143fce85c0aadea38da0d",
            "858a6acb4f59085e56d2ce1db761795c",
            "067ac283d6b2d422f436e88a8be801de",
            "324d6370574676dc632b66048978e51b",
            "c2b415c99a344c87f4a7aa0d52cf231f",
            "804741413d7fe0e515b19a7ffc7b3027",
            "1c146c5b6c3c401e401a3d0064963d57",
            "c11e7bdf90f2d5f9731104d7fbeed535",
            "553e468dda18463e06eea7df5fcc1663",
            "998ea854284ac631d0468db7f4b6a3b4",
            "1201b9605c7a2344214bfe162444dee1",
            "07efac834d3f6e4e16b56998403a9c0a",
            "0913e2e6049a59306f85c213277a5253",
            "c5f74d70816146950dade7447c3bb19b",
            "bd61d49ef02a43cafa0d93151fde0141",
            "35170604c0c79bca269230cad2a4a1b4",
            "e74c0d42b4433905293aab661fcf8ddb",
            "1398ec337718a0ea6f7d4bc9d133d2cd",
            "f439fd056bb9f78964473a222070e5b1",
            "5f09c18aa03d2b166d99a176c080fcb0",
            "d8ef2baa49541373b1f0ad17136f50b4",
            "bf981432da270dbb8850e1ab8c0e23ef",
            "8d2583b317ce1bd4962cec8605c5676a",
            "4b6e01342c861443cc723ae8578374d6",
            "3b0e25997ec9fc2ff41914cd1d416b08",
            "57947ed4d4130c7ff0a057c8654dd1a3",
            "c53e912b5f1f9f356348061f065828fa",
            "191f8f858acda435ae0daf994e2a72c2",
            "5b15de19acf484007e0b12fbe4b32dcd",
            "5747e790dc02f17095a88daa3bdefb53",
            "8aed00a8f05f1bdbdeaeb49e43a6f5d0",
            "da30ae19dad4ae2fe559f5ee20183be1",
            "2c463dfdde588f3bfc60d53118c10d6b",
            "74e13f9cb4d85bc8173944b81ae352e6",
            "e0ab403cfa9bcad8735e867489ef50ce",
            "aac8edbc29a560cac666aee5b8da1e97",
            "ce8ea6a2d7101720263df11d3f2b1edb",
            "fd7e84422dec2435f70f8f6596414d46",
            "6c1a9168318f6cb8f20a70b3c4246823",
            "32fd8f28cc525c2441ae91c75130f69a",
            "eddeb82df22554fa67c641e3f8a25566",
            "5bf77ea710807e74364b116b97482c35",
            "1d662c1ef17eea350576fb2fb560f699",
            "903e0bd908e82a41740bc1ff06336f90",
            "fe635afcd091063cddd63113802a1d4c",
            "9d15f1bfbb6f67319c4c750de939692c",
            "024f306da5357bb0945010cb023197eb",
            "d8167a955c6fe515a674d8e2dae1ac5e",
            "035eb76e8e1199879473db82502d31dc",
            "31c88b31e501b9375639c1fd4d8b9b30",
            "13ed5639159d334043114483e20798eb",
            "f7e2b2b75b04175610e5a00c1e221ebb",
            "1d37638fd00e68caf046788ad8b0f3cd",
            "88fe754390312fbe4403df0d5bfcd684",
            "dc16622ddc767e6bc1200fe5df2fbdfb",
            "8562267361782a2afff35c511754950b",
            "51e782e149cc9bf47a3663420c69afb0",
            "0f07b6849aa7069383835c8a08af522b",
            "cefbf611a6c272b539c614781b18c672",
            "209dc878267a1292ff2a1b0bdfbbc52e",
            "6968240043185f65f22097299b865f6b",
            "32a6fdcfcc8e552b39077e16cfa60bb8",
            "c215ef1234b2901f65028ae649d9ab10",
            "8352b337d49786ef84e9c4e17db37d3c",
            "8b73f6e0233d36b602d3380b92c24767",
            "c4543cb2c16f20d3ca96fae20b76d80c",
            "e28a07aa422cbd5287605d56eb93bdf2",
            "96007274ca23403c50423e084541f6ad",
            "e6f7794f338e60d879957ac9c078b8c8",
            "844e64dd47a930b206d17c880b1b6aa7",
            "bdbfe110aa92e31fc63118cc925d0f53",
            "c12ac138cabba9b19f78a1a857fd739f",
            "fbe932a22e0f1e370ef66521e1d830d6",
            "8195c323068b9e712546bb0b0f53f1a7",
            "b1f0533fe802ea5705349ef2534d731f",
            "7686cf3d01b0da82092aaf31e64a2d16",
            "c7b4c8931a9d26f903ba7ccc3bd15a46",
            "ae7f6ef676f7f295e804910c6f4a59b5",
            "115c51eb37365df2d4f4e2482b964822",
            "7ddb044742a796dbde827d549332d068",
            "60b76206ede5e61ad6b76640334e42e1",
            "9a085f3f881635bb1aba998c19fd0437",
            "dbfc84f90425e6e242583008badae50f",
            "1ba0846587744383fa518b7e642ff632",
            "a0b1ed833b7ba6b8ee18719be00484ea",
            "cad15ea50b2c5f48b8affea97ce5ffb9",
            "59b0242901e35827bf9cd5cf4e02520a",
            "befd6964decdf48c881a74f489200436",
            "c7410e5d6aa6b2f78ea7d9267b7908c2",
            "298f8102b1b587b763a523c48aae2ddc",
            "22f4672d7c1794680dc28c40b616509c",
            "7e364af51232953a478ebdb80369b5e7",
            "5c3c6dca46f9c469e9a393cbb4ff6705",
            "f0e9efc970d06fb296931317485f2eea",
            "6462101070b518295f543ac29600a422",
            "bad512975c43a6fcfc99387270800ea3",
            "208e2e328d9f46f5e897b6b06fb98a99",
            "fba52734fa4fc4cc4dc0770a3ad87af7",
            "dd62b793bccf62e093daf8b25b3a0d3f",
            "da0cc3d85e4b073bad11a48a05303ae4",
            "542ee6960a9da6c2c9ba5555160e3a4d",
            "17b146257aa921ec01c87e27bd784bbf",
            "d2bb0d1b75c96ad873f38e9d4292528d",
            "d10d425653580997d2d2a94e9eb7678e",
            "d99562d86cb39e635544169e26df5fc2",
            "bf15e9bbff22c7719020f9df4badc20a",
            "db60b95decdeed944b4cd8685417cfdc",
            "170683878ac363fc32a644d5bbb34b0a",
            "781334cb77b31e8712c47c596f64be26",
            "dc47280b595ea347ed0cbca21a3f8c0f",
            "9ef14ff237f2b0ff89af4b076d46dec6",
            "f29e862f7307c4eb635b0bb98fdbaf60",
            "6fa2dc1f425ab5a60c8bfb107c241a71",
            "b0ba3ef0fbb84634cbda64006c598618",
            "df5a406715cf39286ef722b14c9bac22",
            "18a2107298ac0eb088a8991e494548c6",
            "5bb15b81a1d2304ecd701e776a4af5e9",
            "614b6908debeb1dcacffbcd171f16273",
            "32f1cef9abedbe6a3ed8a710d9cb6e36",
            "aead837c5a558c9689f8bb22fad2979f",
            "566a9968b43628588e76be5a85a0f9e8",
            "d8ffe0099bb7e2c98f8e6e5c37baffa2",
            "87b3c27db40753fbc540536efc8f654a"
    );
    private static final CompInvestInstitutionAliasRepository compInvestInstitutionAliasRepository = ApplicationCoreContextManager.getInstance(CompInvestInstitutionAliasRepository.class);
    private static final CompAffiliatedInstitutionRepository compAffiliatedInstitutionRepository = ApplicationCoreContextManager.getInstance(CompAffiliatedInstitutionRepository.class);
    private static final CompProductAliasRepository compProductAliasRepository = ApplicationCoreContextManager.getInstance(CompProductAliasRepository.class);
    private static final CompAffiliatedProductsRepository compAffiliatedProductsRepository = ApplicationCoreContextManager.getInstance(CompAffiliatedProductsRepository.class);
    private static final List<String> FILELDS = Arrays.asList("_id", "Name");
    private static final Set<String> FINACING_INVEST_NAME_ENUMS = new HashSet<>(Arrays.asList("-",
            "投资机构未知",
            "未披露",
            "未知",
            "投资方未知",
            "投资人未知",
            "未透露",
            "投资者未知",
            "投资方未透露",
            "暂未透露"
    ));

    // 给投资机构排序
    public static List<ParticipantEntity> sortParticipantList(List<ParticipantEntity> participantList) {
        List<ParticipantEntity> list = new ArrayList<>();
        //有机构id的机构 1
        List<ParticipantEntity> relateInstitution = new ArrayList<>();
        //可关联到机构且有keyno的企业 2
        List<ParticipantEntity> compRelateInstitution = new ArrayList<>();
        //关联不到机构但是有keyno的企业 3
        List<ParticipantEntity> relateComp = new ArrayList<>();
        //无机构id的机构 4
        List<ParticipantEntity> unRelateInstitution = new ArrayList<>();
        //无keyno的企业  5
        List<ParticipantEntity> unRelateComp = new ArrayList<>();
        //自然人 6
        List<ParticipantEntity> person = new ArrayList<>();
        //其他 7
        List<ParticipantEntity> other = new ArrayList<>();

        //List<ParticipantEntity> product = new ArrayList<>();//该类别待废弃

        for (ParticipantEntity investEntity : participantList) {
            String investName = getFinacingInvestName(investEntity.getName());
            if (StringUtils.equalsIgnoreCase(investName, "未披露")) {
                continue;
            }
            // 包含特定字符的 机构名称无需繁简体转换
//            StringCommonUtil.isInvestHasSpecialName(investEntity.getName()) ?
//                    investEntity.getName() : ZhConverterUtil.toSimple(investEntity.getName());
            investEntity.setName(investName);
            if (investEntity.getCategory() == InvestTypeEnum.INSTITUTION.getCode()) {
                if (StringUtils.isEmpty(investEntity.getKeyNo())) {
                    unRelateInstitution.add(investEntity);
                } else {
                    relateInstitution.add(investEntity);
                }
            } else if (investEntity.getCategory() == InvestTypeEnum.COMPANY.getCode()) {
                if (StringUtils.isEmpty(investEntity.getKeyNo())) {
                    unRelateComp.add(investEntity);
                } else {
                    List<ParticipantEntity.RelationInfo> relationInfo = investEntity.getRelationInfo();
                    if (CollectionUtils.isNotEmpty(relationInfo) && StringUtils.isNotBlank(relationInfo.get(0).getKeyNo())) {
                        //新增机构id 相同放一起
                        investEntity.setInstitutionId(relationInfo.get(0).getKeyNo());
                        compRelateInstitution.add(investEntity);
                    } else {
                        relateComp.add(investEntity);
                    }
                }
            } else if (investEntity.getCategory() == InvestTypeEnum.PERSON.getCode()) {
                person.add(investEntity);
            } else if (investEntity.getCategory() == InvestTypeEnum.OTHER.getCode()) {
                other.add(investEntity);
            }/*else if (investEntity.getCategory() == InvestTypeEnum.PRODUCT.getCode()) {
                product.add(investEntity);
            }*/
        }
        list.addAll(relateInstitution);
        //新增逻辑相同机构相邻展示
        list.addAll(sortByInstitutionId(compRelateInstitution));
        list.addAll(relateComp);
        list.addAll(unRelateInstitution);
        list.addAll(unRelateComp);
        list.addAll(person);
        list.addAll(other);
        //list.addAll(product);
        return list;
    }





    public static String getFinacingInvestName(String investName) {
        if (StringUtils.isBlank(investName)) {
            return "未披露";
        }
        if (FINACING_INVEST_NAME_ENUMS.contains(investName)) {
            return "未披露";
        }
        return investName;
    }

    /**
     * 机构id相同的存放在一起
     */
    public static List<ParticipantEntity> sortByInstitutionId(List<ParticipantEntity> compRelateInstitution) {
        if (CollectionUtils.isNotEmpty(compRelateInstitution)) {
            Map<String, List<ParticipantEntity>> groupedByInsId = compRelateInstitution.stream()
                    .collect(Collectors.groupingBy(ParticipantEntity::getInstitutionId));
            // 机构id相同挨着
            List<ParticipantEntity> sortedList = groupedByInsId.entrySet().stream()
                    .flatMap(entry -> entry.getValue().stream())
                    .collect(Collectors.toList());
            return sortedList;
        }
        return new ArrayList<>();
    }

    /*
     获取非CVC 机构  name  keyno org 等字段需要提前设置好
    */
    public static List<InvestEntity> getParticipantEntityWithNotCVCByNameEntity(List<InvestEntity> entities){
        List<String> insNames =entities.stream().filter(vv->StringUtils.isBlank(vv.getId()) && StringUtils.isNotBlank(vv.getName())).map(InvestEntity::getName).collect(Collectors.toList());
        List<CompInvestInstitutionAlias> investInstitutionAliases = compInvestInstitutionAliasRepository.getInvestByNameList(insNames);
        entities.stream().filter(vvv->vvv.getCategory()==1&& StringUtils.isBlank(vvv.getId())).forEach(vvv->{
            Set<String> names = getFormatNames(vvv.getName());
            CompInvestInstitutionAlias op = investInstitutionAliases.stream().filter(it -> names.contains(it.getKeywords())).findFirst().orElse(null);

            if(op!=null &&!NOT_INSTOITUTION.contains(op.getInstitutionCode())){
           // if (op != null && (StringUtils.isBlank(op.getInstitutionType()) || (StringUtils.isNotBlank(op.getInstitutionType()) && !"CVC".equals(op.getInstitutionType())))) {
                vvv.setName(op.getInstitutionName());
                vvv.setId(op.getInstitutionCode());
                vvv.setInType(op.getInstitutionType());
                vvv.setVcpeType("5");



            }
        });

        List<String> insKeynos =entities.stream().filter(vv->StringUtils.isBlank(vv.getId()) && StringUtils.isNotBlank(vv.getKeyNo())).map(InvestEntity::getKeyNo).distinct().collect(Collectors.toList());
        List<QccCompanyOutCt> companyOutCts = CtCompanyUtils.getCompDetailsByKeyNo(insKeynos, FILELDS);

        entities.stream().filter(vvv->vvv.getCategory()==2).forEach(vvv->{
            QccCompanyOutCt op=companyOutCts.stream().filter(uu->uu.getKeyNo().equals(vvv.getKeyNo())).findFirst().orElse(null);
            if(op!=null){
                vvv.setOriginalName(op.getName());
                vvv.setName(op.getName());
            }
        });
        List<CompAffiliatedInstitution> investInstitutionAffiKeynos = compAffiliatedInstitutionRepository.getValidAffByCompKeyNoList(insKeynos).stream().filter(vv -> vv.getShowType() > 0).collect(Collectors.toList());

        entities.stream().filter(vvv->vvv.getCategory()==2 &&StringUtils.isBlank(vvv.getId()) &&StringUtils.isNotBlank(vvv.getKeyNo())).forEach(vvv->{
            CompAffiliatedInstitution op=investInstitutionAffiKeynos.stream().filter(uu->uu.getCompKeyno().equals(vvv.getKeyNo())).findFirst().orElse(null);
           // if (op != null && (StringUtils.isBlank(op.getInstitutionType()) || (StringUtils.isNotBlank(op.getInstitutionType()) && !"CVC".equals(op.getInstitutionType())))) {
            if(op!=null &&!NOT_INSTOITUTION.contains(op.getInstitutionId())){
                vvv.setName(op.getInstitutionName());
                    vvv.setId(op.getInstitutionId());
                    vvv.setInType(op.getInstitutionType());
                    vvv.setVcpeType("5");
                    vvv.setVcpeId(op.getVcpeId());

            }
        });

        if(entities.stream().anyMatch(vvv->vvv.getCategory()==2 &&StringUtils.isBlank(vvv.getKeyNo()) &&StringUtils.isBlank(vvv.getId()))){
            List<CompAffiliatedInstitution> investInstitutionAffiNames = compAffiliatedInstitutionRepository.getShowTypeAffListByCompNameList(insNames);
            entities.stream().filter(vvv->vvv.getCategory()==2 &&StringUtils.isBlank(vvv.getId()) &&StringUtils.isBlank(vvv.getKeyNo())).forEach(vvv->{
                Set<String> names = getFormatNames(vvv.getName());
                CompInvestInstitutionAlias op = investInstitutionAliases.stream().filter(it -> names.contains(it.getKeywords())).findFirst().orElse(null);
              //  if (op != null && (StringUtils.isBlank(op.getInstitutionType()) || (StringUtils.isNotBlank(op.getInstitutionType()) && !"CVC".equals(op.getInstitutionType())))) {
                if(op!=null &&!NOT_INSTOITUTION.contains(op.getInstitutionCode())){
                    vvv.setName(op.getInstitutionName());
                        vvv.setId(op.getInstitutionCode());
                        vvv.setInType(op.getInstitutionType());
                        vvv.setVcpeType("5");
                        vvv.setCategory(1);

                } else {
                    CompAffiliatedInstitution op1 = investInstitutionAffiNames.stream().filter(it -> names.contains(it.getCompName())).findFirst().orElse(null);
                   // if (op1 != null && (StringUtils.isBlank(op1.getInstitutionType()) || (StringUtils.isNotBlank(op1.getInstitutionType()) && !"CVC".equals(op1.getInstitutionType())))) {
                   if(op1!=null &&!NOT_INSTOITUTION.contains(op1.getInstitutionId())){
                        vvv.setName(op1.getInstitutionName());
                        vvv.setId(op1.getInstitutionId());
                        vvv.setInType(op1.getInstitutionType());
                        vvv.setVcpeType("5");
                        vvv.setVcpeId(op1.getVcpeId());

                    }
                }
            });

        }
        //还有公司名称好像调用了 CommonUtil.getCompanyNameByName()
        entities.stream().filter(it->it.getCategory()==2).forEach(vvv->{ vvv.setName(CommonUtil.getCompanyNameByName( vvv.getName())); });

        return entities;

    }


    /*
 获取CVC 产品机构  name  keyno org 等字段需要提前设置好
*/
    public static List<InvestEntity> getParticipantEntityWithCVCByNameEntity(List<InvestEntity> entities){
        List<String> insNames =entities.stream().filter(vv->StringUtils.isBlank(vv.getId()) && StringUtils.isNotBlank(vv.getName())).map(InvestEntity::getName).collect(Collectors.toList());
        List<CompProductAlias> compProductAliases = compProductAliasRepository.getProductByNameList(insNames);
        entities.stream().filter(vvv->vvv.getCategory()==1&& StringUtils.isBlank(vvv.getId())).forEach(vvv->{
            Set<String> names = getFormatNames(vvv.getName());
            List<CompProductAlias> opList = compProductAliases.stream().filter(it -> names.contains(it.getKeyword())).collect(Collectors.toList());

          //  if (opList.size() ==1) {
            if (opList.stream().map(CompProductAlias::getProductId).distinct().count() ==1) {
                vvv.setName(opList.get(0).getProductName());
                vvv.setId(opList.get(0).getProductId());
                vvv.setInType(opList.get(0).getProductType());
                vvv.setVcpeType("4");
            }
        });

        List<String> insKeynos =entities.stream().filter(vv->StringUtils.isBlank(vv.getId()) && StringUtils.isNotBlank(vv.getKeyNo())).map(InvestEntity::getKeyNo).distinct().collect(Collectors.toList());
        List<QccCompanyOutCt> companyOutCts = CtCompanyUtils.getCompDetailsByKeyNo(insKeynos, FILELDS);

        entities.stream().filter(vvv->vvv.getCategory()==2).forEach(vvv->{
            QccCompanyOutCt op=companyOutCts.stream().filter(uu->uu.getKeyNo().equals(vvv.getKeyNo())).findFirst().orElse(null);
            if(op!=null){
                vvv.setOriginalName(op.getName());
                vvv.setName(op.getName());
            }
        });
        List<CompAffiliatedProducts> afftitedProdAffiKeynos = compAffiliatedProductsRepository.getProductCompByKeyNo(insKeynos);
        entities.stream().filter(vvv->vvv.getCategory()==2 &&StringUtils.isBlank(vvv.getId()) &&StringUtils.isNotBlank(vvv.getKeyNo())).forEach(vvv->{
            CompAffiliatedProducts op=afftitedProdAffiKeynos.stream().filter(uu->uu.getCompKeyno().equals(vvv.getKeyNo())).findFirst().orElse(null);
            if(op!=null){
                vvv.setName(op.getProductName());
                vvv.setId(op.getProductId());
                vvv.setInType(op.getProductType());
                vvv.setVcpeType("4");

            }
        });

        if(entities.stream().anyMatch(vvv->vvv.getCategory()==2 &&StringUtils.isBlank(vvv.getKeyNo()) &&StringUtils.isBlank(vvv.getId()))){
            List<CompAffiliatedProducts> productsAffiNames = compAffiliatedProductsRepository.getProductCompNameList(insNames);
            entities.stream().filter(vvv->vvv.getCategory()==2 &&StringUtils.isBlank(vvv.getId()) &&StringUtils.isBlank(vvv.getKeyNo())).forEach(vvv->{
                Set<String> names = getFormatNames(vvv.getName());
                List<CompProductAlias> opList = compProductAliases.stream().filter(it -> names.contains(it.getKeyword())).collect(Collectors.toList());
                //  if (opList.size() ==1) {
                if (opList.stream().map(CompProductAlias::getProductId).distinct().count() ==1) {
                    vvv.setName(opList.get(0).getProductName());
                    vvv.setId(opList.get(0).getProductId());
                    vvv.setInType(opList.get(0).getProductType());
                    vvv.setVcpeType("4");
                    vvv.setCategory(1);

                } else {
                    CompAffiliatedProducts op1 = productsAffiNames.stream().filter(it -> names.contains(it.getCompName())).findFirst().orElse(null);
                    if (op1 != null) {
                        vvv.setName(op1.getProductName());
                        vvv.setId(op1.getProductId());
                        vvv.setInType(op1.getProductType());
                        vvv.setVcpeType("4");


                    }
                }
            });

        }
        //还有公司名称好像调用了 CommonUtil.getCompanyNameByName()
        entities.stream().filter(it->it.getCategory()==2).forEach(vvv->{ vvv.setName(CommonUtil.getCompanyNameByName( vvv.getName())); });

        return entities;

    }




    /**
     * 投资方关联机构或者CVC产品 keyno啥的
     * return Set<InvestEntity> List<ParticipantEntity>
     */
    public static List<InvestEntity> getParticipantEntityByNameEntity(CompProductFinancing brandFinanceBO) {

         List<InvestEntity> result = new ArrayList<>();
        //领投机构

        List<InvestEntity> investLeadEntities = ProductUtils.getInvestNames(brandFinanceBO.getLeadInstitution()).stream().filter(StringUtils::isNotBlank).map(vv -> {
            InvestEntity investEntity = new InvestEntity();
            investEntity.setCategory(InvestTypeEnum.INSTITUTION.getCode());
            investEntity.setOriginalName(vv);
            investEntity.setName(vv);
            investEntity.setType(1);
            return investEntity;
        }).collect(Collectors.toList());

        //跟投机构
        List<InvestEntity> investFollowEntities = ProductUtils.getInvestNames(brandFinanceBO.getFollowInstitution()).stream().filter(StringUtils::isNotBlank).map(vv -> {
            InvestEntity investEntity = new InvestEntity();
            investEntity.setCategory(InvestTypeEnum.INSTITUTION.getCode());
            investEntity.setOriginalName(vv);
            investEntity.setName(vv);
            investEntity.setType(2);
            return investEntity;
        }).collect(Collectors.toList());


        //领头公司
        List<InvestEntity> companyLeadEntities = ProductUtils.getInvestNames(brandFinanceBO.getLeadCompany()).stream().filter(StringUtils::isNotBlank).map(vv -> {
            InvestEntity investEntity = new InvestEntity();
            investEntity.setCategory(InvestTypeEnum.COMPANY.getCode());
            investEntity.setOriginalName(CommonUtil.getCompanyNameByName(vv));
            //investEntity.setOriginalName(vv);
            investEntity.setName(vv);
            investEntity.setKeyNo(CtCompanyUtils.getKeyNoByName(vv, "", "", null, true, "", DimensionEnum.PD_PRODUCT));
            investEntity.setOrg(CommonUtil.getOrgByKeyNo(investEntity.getKeyNo()));
            investEntity.setType(1);
            return investEntity;
        }).collect(Collectors.toList());


        //跟头公司
        List<InvestEntity> companyFollowEntities = ProductUtils.getInvestNames(brandFinanceBO.getFollowCompany()).stream().filter(StringUtils::isNotBlank).map(vv -> {
            InvestEntity investEntity = new InvestEntity();
            investEntity.setCategory(InvestTypeEnum.COMPANY.getCode());
            investEntity.setOriginalName(CommonUtil.getCompanyNameByName(vv));
            //investEntity.setOriginalName(vv);
            investEntity.setName(vv);
            investEntity.setKeyNo(CtCompanyUtils.getKeyNoByName(vv, "", "", null, true, "", DimensionEnum.PD_PRODUCT));
            investEntity.setOrg(CommonUtil.getOrgByKeyNo(investEntity.getKeyNo()));
            investEntity.setType(2);
            return investEntity;
        }).collect(Collectors.toList());

        companyFollowEntities.addAll(ProductUtils.getInvestNames(brandFinanceBO.getStockholderInvestor()).stream().filter(StringUtils::isNotBlank).map(vv -> {
            InvestEntity investEntity = new InvestEntity();
            investEntity.setCategory(InvestTypeEnum.COMPANY.getCode());
            investEntity.setOriginalName(CommonUtil.getCompanyNameByName(vv));
            //investEntity.setOriginalName(vv);
            investEntity.setName(vv);
            investEntity.setKeyNo(CtCompanyUtils.getKeyNoByName(vv, "", "", null, true, "", DimensionEnum.PD_PRODUCT));
            investEntity.setOrg(CommonUtil.getOrgByKeyNo(investEntity.getKeyNo()));
            investEntity.setType(2);
            return investEntity;
        }).collect(Collectors.toList()));



        //领头个人
        String leadPerson = ProductUtils.getInvestNames(brandFinanceBO.getLeadPerson()).stream().collect(Collectors.joining(" , "));
        InvestEntity leadPersonEntity = new InvestEntity();
        leadPersonEntity.setCategory(InvestTypeEnum.PERSON.getCode());
        leadPersonEntity.setType(1);
        leadPersonEntity.setName(leadPerson);
        if (StringUtils.isNotBlank(leadPerson)) {
            if (leadPerson.equalsIgnoreCase("个人投资者")) {
                leadPersonEntity.setName(leadPerson);
            } else {
                leadPersonEntity.setName("个人投资者（" + leadPerson + "）");
            }
        }

        //跟头个人
        String followPerson = ProductUtils.getInvestNames(brandFinanceBO.getFollowPerson()).stream().collect(Collectors.joining(" , "));
        InvestEntity followPersonEntity = new InvestEntity();
        followPersonEntity.setCategory(InvestTypeEnum.PERSON.getCode());
        followPersonEntity.setType(2);
        followPersonEntity.setName(followPerson);
        if (StringUtils.isNotBlank(followPerson)) {
            if (followPerson.equalsIgnoreCase("个人投资者")) {
                followPersonEntity.setName(followPerson);
            } else {
                followPersonEntity.setName("个人投资者（" + followPerson + "）");
            }
        }

        // 处理指定领投方
        List<InvestEntity> leadAssignInvestEntityList = getAssignInvest(brandFinanceBO.getAssignLeader(), 1);
        // 处理指定更投方
        List<InvestEntity> followAssignInvestEntityList = getAssignInvest(brandFinanceBO.getAssignFollwer(), 2);

        List<String> inIds = leadAssignInvestEntityList.stream().filter(vvv->vvv.getCategory() ==1 && StringUtils.isNotBlank(vvv.getId())).map(InvestEntity::getId).collect(Collectors.toList());
        inIds.addAll(followAssignInvestEntityList.stream().filter(vvv->vvv.getCategory() ==1 && StringUtils.isNotBlank(vvv.getId())).map(InvestEntity::getId).collect(Collectors.toList()));
        List<CompInvestInstitutionAlias> existAlias =compInvestInstitutionAliasRepository.getInvestByInstitutionCodeList(inIds);
        //指定领头跟投方 补充机构类型和机构
        leadAssignInvestEntityList.forEach(vvv -> {
            if (vvv.getCategory().equals(InvestTypeEnum.COMPANY.getCode())) {
                vvv.setOriginalName(CommonUtil.getCompanyNameByName(vvv.getName()));
                companyLeadEntities.add(vvv);
            } else if (vvv.getCategory().equals(InvestTypeEnum.INSTITUTION.getCode())) {
                CompInvestInstitutionAlias op =existAlias.stream().filter(it->it.getInstitutionCode().equals(vvv.getId())).findFirst().orElse(null);
                if(op !=null){
                    vvv.setVcpeType("5");
                    vvv.setInType(op.getInstitutionType());
                }
                investLeadEntities.add(vvv);
            }
        });

        followAssignInvestEntityList.forEach(vvv -> {
            if (vvv.getCategory().equals(InvestTypeEnum.COMPANY.getCode())) {
                vvv.setOriginalName(CommonUtil.getCompanyNameByName(vvv.getName()));
                companyFollowEntities.add(vvv);
            } else if (vvv.getCategory().equals(InvestTypeEnum.INSTITUTION.getCode())) {
                CompInvestInstitutionAlias op =existAlias.stream().filter(it->it.getInstitutionCode().equals(vvv.getId())).findFirst().orElse(null);
                if(op !=null){
                    vvv.setVcpeType("5");
                    vvv.setInType(op.getInstitutionType());
                }
                investFollowEntities.add(vvv);
            }
        });





        //其他跟投方
        companyFollowEntities.addAll(getOtherInvest(brandFinanceBO.getOtherInvestor(), 2));

        result.addAll(investLeadEntities);
        result.addAll(investFollowEntities);
        result.addAll(companyLeadEntities);
        result.addAll(companyFollowEntities);
        if (StringUtils.isNotBlank(leadPerson)) {
            result.add(leadPersonEntity);
        }
        if (StringUtils.isNotBlank(followPerson)) {
            result.add(followPersonEntity);
        }

        return getParticipantEntityWithCVCByNameEntity(getParticipantEntityWithNotCVCByNameEntity(result));
    }

    private static List<InvestEntity> getAssignInvest(String investment, int type) {
        if (StringUtils.isNotBlank(investment)) {
            try {
                List<InvestEntity> investEntities = JSONArray.parseArray(investment, InvestEntity.class);
                investEntities.forEach(investEntity -> {
                    if (investEntity.getCategory().equals(InvestTypeEnum.INSTITUTION.getCode())) {
                        String id = StringUtils.isBlank(investEntity.getId()) ? investEntity.getKeyNo() : investEntity.getId();
                        investEntity.setId(id);
                        investEntity.setKeyNo("");
                    } else if (investEntity.getCategory().equals(InvestTypeEnum.COMPANY.getCode())) {
                        String keyno = StringUtils.isBlank(investEntity.getId()) ? investEntity.getKeyNo() : investEntity.getId();
                        investEntity.setId("");
                        investEntity.setKeyNo(keyno);
                        investEntity.setOrg(0);
                    }
                    investEntity.setType(type);
                });
                return investEntities;
            } catch (Exception e) {
                Log.getInst().error("录入格式异常", "Error : {},Data : {}", e, investment);
            }
        }
        return Collections.emptyList();
    }

    private static List<InvestEntity> getOtherInvest(String investment, int type) {
        List<InvestEntity> list = new ArrayList<>();
        if (StringUtils.isNotBlank(investment)) {
            Set<String> otherInvestorNames = ProductUtils.getInvestNames(investment);
            otherInvestorNames.forEach(name -> {
                InvestEntity investEntity = new InvestEntity();
                investEntity.setName(CommonUtil.getCompanyNameByName(name));
                investEntity.setId("");
                investEntity.setKeyNo("");
                investEntity.setOrg(-1);
                investEntity.setCategory(InvestTypeEnum.OTHER.getCode());
                investEntity.setType(type);
                list.add(investEntity);
            });
        }
        return list;
    }


    private static Set<String> getFormatNames(String name) {
        Set<String> names = new HashSet<>();
        // 兼容机构当前别名处理规则
        if (StringUtils.isNotBlank(name)) {
            names.add(name);
            names.add(CtStringUtil.handleCompanyName(name));
            // 创投名称统一处理
            names.add(CtStringUtil.formatCtName(name));
            // 投资方名称繁简体处理
            names.add(CtStringUtil.getKeywordByInvestName(name));
        }
        return names;

    }

    public static Integer changeHolderOrgTypeByKeywords(Integer type, String holderName) {

        if (StringUtils.isNotBlank(holderName)) {


            if (type == null || type == 0|| type == 1) {
                if (holderName.contains("资管计划") || holderName.contains("年金计划") || holderName.contains("信托计划") || holderName.contains("信托产品") || holderName.contains("私募产品")) {
                    return 3;
                }

                if (holderName.contains("-") ||holderName.contains("－")) {
                    if(holderName.split("-|－")[1].contains("证券投资基金")){
                        return 3;
                    }
                }
                Matcher matcher = pattern.matcher(holderName.replace("）",")").replace("（","("));

                while (matcher.find()) {
                    if(matcher.group(1).contains("证券投资基金")){
                        return 3;
                    };
                }
            }
        }
        return type;

    }

    /**
     *
     * @param type
     * @param holderName
     * @param holderKeyno
     * @param historyList 已根据holderName匹配
     * @return
     */
    public static Integer changeHolderOrgTypeByKeywordsV2(Integer type, String holderName,String holderKeyno,List<IpoHistoryBaseinfo> historyList) {


        //1 匹配到直接获取直抬 不是1234则进入下一步
        if(CollectionUtils.isNotEmpty(historyList)){
            Integer holderOrgType = historyList.get(0).getHolderOrgType();
            if (holderOrgType == 1 || holderOrgType == 2 || holderOrgType == 3 || holderOrgType == 4) {
                return holderOrgType;
            }
        }
        //2(1) 规则复用 识别资管   只存在0 了
        if (StringUtils.isNotBlank(holderName)) {

            if (type == null || type == 0 || type == 1) {
                if(StringUtils.isNotBlank(holderKeyno) ){
                    if(holderKeyno.startsWith("f05")){
                        return 3;
                    }
                }
                //结尾判断
                if(holderName.contains("混合型证券投资基金") || holderName.contains("混合型发起式证券投资基金") || holderName.endsWith("私募基金")){
                    return 3;
                }

                if (holderName.contains("资管计划") || holderName.contains("年金计划") || holderName.contains("信托计划") || holderName.contains("信托产品") || holderName.contains("私募产品")|| holderName.contains("资产管理计划")) {
                    return 3;
                }
                if (holderName.contains("-") ||holderName.contains("－")) {
                    if(holderName.split("-|－")[1].contains("证券投资基金")){
                        return 3;
                    }
                }
                Matcher matcher = pattern.matcher(holderName.replace("）",")").replace("（","("));
                while (matcher.find()) {
                    if(matcher.group(1).contains("证券投资基金")){
                        return 3;
                    };
                }
            }
        }
        //2(2) 识别自然人
        if (StringUtils.isNotBlank(holderKeyno)) {
            // 优先根据keyno判断
            return holderKeyno.startsWith("p") ? 2 : 1;
        }
        if(holderName.length() < 4 && StringUtils.isBlank(holderKeyno)){
            return 2;
        }
        //企业识别 中文
        if(holderName.endsWith("公司") || holderName.endsWith("企业")){
            return 1;
        }
        boolean hasChinese = Pattern.compile("[\u4e00-\u9fa5]").matcher(holderName).find();
        if (!hasChinese) {
            // 2. 判断是否包含指定的公司后缀（不区分大小写）
            String regex = "(?i)\\b("
                    + "CO|LTD|Inc|Corp"                 // 原缩写
                    + "|Limited|Company|Incorporation|Corporation"  // 新增全称
                    + ")(?:\\.,?\\s?(?:Lt?d?\\.?|Co\\.?|Inc\\.?|Corp\\.?))?\\b";
            boolean hasSuffix = Pattern.compile(regex).matcher(holderName).find();
            if(hasSuffix){
                return 1;
            }
        }
        //其他
        return 5;

    }




    public static void main(String[] args) {
        String holder ="上海文多资产管理中心（有限合伙）-文多逆向私募证券投资基金";
        System.out.println(changeHolderOrgTypeByKeywords(1,holder));
    }

}