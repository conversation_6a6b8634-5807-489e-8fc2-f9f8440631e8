package com.ld.clean.constants;

import com.ld.clean.enums.BusinessEnumInterface;

/**
 * <AUTHOR>
 */

public enum CompanyProductTagTypeEnum implements BusinessEnumInterface {
    /**
     * 融资标签
     **/
    FINANCE(3, "融资标签"),
    /**
     * 产品标签
     **/
    PRODUCT_TAG(4, "产品标签");

    private Integer type;
    private String name;

    private CompanyProductTagTypeEnum(Integer type, String name) {
        this.type = type;
        this.name = name;
    }

    public static CompanyProductTagTypeEnum getCompanyTagTypeEnum(int type) {
        CompanyProductTagTypeEnum[] var1 = values();
        int var2 = var1.length;

        for (int var3 = 0; var3 < var2; ++var3) {
            CompanyProductTagTypeEnum companyTagTypeEnum = var1[var3];
            if (companyTagTypeEnum.getType().equals(type)) {
                return companyTagTypeEnum;
            }
        }
        return null;
    }

    @Override
    public Integer getType() {
        return this.type;
    }

    @Override
    public String getName() {
        return this.name;
    }
}
