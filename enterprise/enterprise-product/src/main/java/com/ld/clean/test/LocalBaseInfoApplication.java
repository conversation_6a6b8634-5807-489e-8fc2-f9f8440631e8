package com.ld.clean.test;

import com.alibaba.fastjson.JSONObject;
import com.ld.clean.context.ApplicationCoreContextManager;
import com.ld.clean.dao.model.manageenterprisedb.CompProductMergerEventsQk;
import com.ld.clean.datasteam.AsyncCleanDataSteam;
import com.ld.clean.datasteam.WindowCleanDataStream;
import com.ld.clean.job.product.baseinfo.sink.InsertProductBaseInfoTidbFunction;
import com.ld.clean.job.product.investmapping.process.CleanCompProductFinanceFlatMap;
import com.ld.clean.job.productv2.memberbusiness.epproductmemberadmin.so.JsonRootBean;
import com.ld.clean.kafka.CleanFlinkKafkaConsumer;
import com.ld.clean.kafka.KafkaHelper;
import com.ld.clean.model.ASharesAndNewThirdBoardFinance;
import com.ld.clean.model.CompBrandFinancing;
import com.ld.clean.model.baseenterprisedb.CompProductAdminRelationship;
import com.ld.clean.model.manageenterprisedb.*;
import com.ld.clean.model.searchsyncenterprise.EpProductBaseinfoSync;
import com.ld.clean.model.tidbsearchsyncenterprise.EpProductFinancingV2Sync;
import com.ld.clean.pojo.mo.ct.MongoProductV2;
import com.ld.clean.test.mainbrand.dao.ScSeCompBrandFinancing20240513;
import com.ld.clean.test.mainbrand.dao.finance.LpfSeEpProductFinancingV2Sync241011;
import com.ld.clean.test.mainbrand.dao.finance.ScMeASharesAndNewThirdBoardFinance20250428;
import com.ld.clean.test.mainbrand.dao.mergerexit.LpfMeIpoExitBaseinfo250208;
import com.ld.clean.test.mainbrand.productTmp.process.InsertProductBaseInfoSyncTestTidbFunction;
import com.ld.clean.test.mainproduct.*;
import com.ld.clean.utils.CheckPointUtil;
import com.ld.clean.utils.ExecutionEnvUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.api.common.functions.FlatMapFunction;
import org.apache.flink.api.java.utils.ParameterTool;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.datastream.SingleOutputStreamOperator;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.flink.streaming.connectors.kafka.FlinkKafkaConsumer;
import org.apache.flink.util.Collector;

import java.util.List;


/**
 * 爬虫数据字段清洗
 *
 * <AUTHOR>
 */

@Slf4j
public class LocalBaseInfoApplication {

    public static void main(String[] args) throws Exception {
        ParameterTool parameterTool = ExecutionEnvUtil.PARAMETER_TOOL;
        StreamExecutionEnvironment env = CheckPointUtil.setCheckpointConfig(ExecutionEnvUtil.prepare(parameterTool), parameterTool);
        ParameterTool dmpParams = ParameterTool.fromArgs(args);
        int spiderProcessParallelism = 4;
        env.setParallelism(spiderProcessParallelism);
        env.disableOperatorChaining();

        String topic = "base_tmp_liupf";
        String group = "group_base_product_baseinfo_data_test";


        FlinkKafkaConsumer<String> consumer = KafkaHelper.buildAuthKafkaSource(CleanFlinkKafkaConsumer.builder()
                .topic(topic)
                .groupId(group).build());

        consumer.setStartFromLatest();

        DataStream<String> originDataStream = env.addSource(consumer.setCommitOffsetsOnCheckpoints(true));


        /*DataStream<CompMaEventCvs> baseinfoDataStream = originDataStream.flatMap(new TmpBaseInfoSyncFlatMap()).name("TmpBaseInfoSyncFlatMap");
        DataStream<CompMaEventCvs> productInfo = AsyncCleanDataSteam.orderedWait(baseinfoDataStream, new TmpCleanBaseinfoSyncFunction(20)).name("TmpCleanBaseinfoSyncFunction");*/

       /* DataStream<EpProductBaseinfoSync> baseinfoDataStream = originDataStream.flatMap(new TmpBaseInfoSyncFlatMap()).name("TmpBaseInfoSyncFlatMap");
        DataStream<EpProductBaseinfoSync> productInfo = AsyncCleanDataSteam.orderedWait(baseinfoDataStream, new TmpCleanBaseinfoSyncFunction(20)).name("TmpCleanBaseinfoSyncFunction");
*/



        DataStream<DapProductMergerEvents> baseinfoDataStream = originDataStream.flatMap(new TmpBaseInfoFlatMap()).name("TmpBaseInfoFlatMap");
        //DataStream<CompBrandFinancing> v2Sync241011DataStream = AsyncCleanDataSteam.orderedWait(baseinfoDataStream, new TmpCleanV2Function(20)).name("TmpCleanBaseinfoSyncFunction");
        //DataStream<CompProductMergerEvents> baseinfoDataStream = originDataStream.flatMap(new TmpBaseInfoFlatMap()).name("TmpBaseInfoFlatMap");
        //DataStream<LpfSeEpProductFinancingV2Sync241011> v2Sync241011DataStream = AsyncCleanDataSteam.orderedWait(baseinfoDataStream, new TmpCleanV2Function(20)).name("TmpCleanBaseinfoSyncFunction");
        DataStream<DapProductMergerEvents> productInfo = AsyncCleanDataSteam.orderedWait(baseinfoDataStream, new TmpCleanBaseinfoSyncFunction(10)).name("TmpCleanBaseProductToRuiShouFunction");
       //并购退出
         //DataStream<List<LpfMeIpoExitBaseinfo250208>> productInfo = AsyncCleanDataSteam.orderedWait(baseinfoDataStream, new TmpExitSyncFunction(20)).name("TmpCleanBaseProductToRuiShouFunction");


       /* DataStream<List<InvestCompanyHolderDiff>> list = WindowCleanDataStream.toDataStreamList(productInfo);
        DataStream<List<InvestCompanyHolderDiff>> insertBaseList = AsyncCleanDataSteam.orderedWait(list,
                new InsertProductBaseInfoSyncTestTidbFunction(10)).name("InsertProductBaseInfoTidbFunction");*/



        /*DataStream<JsonRootBean> jsonRootBeanDataStream = originDataStream.flatMap(new FlatMapFunction<String, JsonRootBean>() {
            @Override
            public void flatMap(String s, Collector<JsonRootBean> collector) throws Exception {
                collector.collect(JSONObject.parseObject(s, JsonRootBean.class));
            }
        });
        SingleOutputStreamOperator<ProductBaseinfo> productBase = AsyncCleanDataSteam.orderedWait(jsonRootBeanDataStream, new ProductBaseInfoCleanFeildAsync(1)).flatMap(new FlatMapFunction<List<ProductBaseinfo>, ProductBaseinfo>() {
            @Override
            public void flatMap(List<ProductBaseinfo> events, Collector<ProductBaseinfo> collector) throws Exception {
                events.forEach(collector::collect);
            }

        });*/
        /*DataStream<List<DapIpoHistory>> list = WindowCleanDataStream.toDataStreamList(productInfo);
        DataStream<List<DapIpoHistory>> insertBaseList = AsyncCleanDataSteam.orderedWait(list,
                new InsertProductBaseInfoSyncTestTidbFunction(20)).name("InsertProductBaseInfoTidbFunction");
*/
       /* DataStream<List<EpProductFinancingV2Sync>> list = WindowCleanDataStream.toDataStreamList(productInfo);
        DataStream<List<EpProductFinancingV2Sync>> insertBaseList = AsyncCleanDataSteam.orderedWait(list,
                new InsertProductBaseInfoSyncTestTidbFunction(20)).name("InsertProductBaseInfoTidbFunction");*/
       /* //更新基础表
        //更新mongo
        /*AsyncCleanDataSteam.orderedWait(productInfo,
                new TmpSyncBrandMongoFinanceInfoFunction(20)).name("TmpSyncBrandMongoFinanceInfoFunction");*/

        /*AsyncCleanDataSteam.orderedWait(baseinfoDataStream,
                new TmpSyncBrandMongoFinanceInfoFunction(20)).name("TmpSyncBrandMongoFinanceInfoFunction");*/
        env.execute(ApplicationCoreContextManager.getAppId());
    }
}
