package com.ld.clean.constants;

import lombok.Getter;

/**
 * 企业阶段
 *
 * <AUTHOR>
 */
@Getter
public enum CompanyStageEnum {
    LISTED("已上市", 1),
    TO_BE_LISTED("待上市", 2),
    IPO_DECLARATION("IPO申报阶段", 3),
    LISTING_GUIDANCE("上市辅导阶段", 4),
    NOT_LISTED("尚未启动辅导", 5);

    private String stage;
    private int code;

    CompanyStageEnum(String stage, int code) {
        this.stage = stage;
        this.code = code;
    }

    public static String getStageNameByCode(Integer code) {
        for (CompanyStageEnum stage : CompanyStageEnum.values()) {
            if (stage.code == code) {
                return stage.stage;
            }
        }
        return "尚未启动辅导";
    }

    public static Integer getCodeByStageName(String name) {
        for (CompanyStageEnum stage : CompanyStageEnum.values()) {
            if (stage.stage.equals(name)) {
                return stage.code;
            }
        }
        return 5;
    }

}

