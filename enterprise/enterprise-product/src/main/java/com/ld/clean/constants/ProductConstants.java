package com.ld.clean.constants;

/**
 * 常量类
 *
 * <AUTHOR>
 * @date 2021年3月8日
 */
public class ProductConstants {

    /**
     * 线程数
     */
    public static final String CORE_POOL_SIZE = "core.pool.size";
    public static final int CORE_POOL_SIZE_DEFAULT = 20;

    /**
     * topic与消费者组  投融资相关队列
     */
    public static final String SPIDER_ENTERPRISE_PRODUCT_FINANCING_TOPIC = "spider.enterprise.product.financing.topic";
    public static final String SPIDER_ENTERPRISE_PRODUCT_FINANCING_GROUP = "spider.enterprise.product.financing.groupid";

    public static final String SPIDER_PRODUCT_FINANCING_PROCESS_PARALLELISM = "spider.process.parallelism";

    /**
     * time out 时间
     */
    public static final int TIME_OUT = 1000;

    public static final String UPDATE = "UPDATE";
    public static final String INSERT = "INSERT";
    public static final String DELETE = "DELETE";

    public static final Integer CLEAN_PRODUCT_TYPE_BASE = 0;
    public static final Integer CLEAN_PRODUCT_TYPE_ALL = 1;
    public static final Integer CLEAN_PRODUCT_TYPE_FINANCE = 2;
    public static final Integer CLEAN_PRODUCT_TYPE_MEMBER = 3;
    public static final Integer CLEAN_PRODUCT_TYPE_COMPETITOR = 4;
    public static final Integer CLEAN_PRODUCT_TYPE_NEWS = 5;

}
