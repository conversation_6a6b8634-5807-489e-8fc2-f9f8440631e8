package com.ld.clean.beans;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Data
public class ParticipantEntity {

    /**
     * 1-投资方
     * 2-出让方
     */

    @JSONField(name = "ParticipantType")
    private Integer participantType;

    /**
     * 1：领投
     * 2：跟投
     */
    @JSONField(name = "Type")
    private Integer type;

    /**
     * 1：投资机构，2：公司，0：个人 3:产品
     */
    @JSONField(name = "Category")
    private Integer category;

    @JSONField(name = "Name")
    private String name;

    @JSONField(name = "KeyNo")
    private String keyNo;

    @JSONField(name = "Org")
    private Integer org;

    // 原始投资方名称
    @JSONField(name = "RelationInfo")
    private List<RelationInfo> relationInfo = new ArrayList<>();

    // 投资方投资金额明细，融资事件该字段为空
    @JSONField(name = "Amount")
    private String amount = "";

    // 投资方投资比例明细，融资事件该字段为空
    @JSONField(name = "Rate")
    private String rate = "";

    /*
     * 0 -
     * 1 私募基金
     * 2 私募基金管理人
     * 3 企业
     * */
    @JSONField(name = "Identity")
    private Integer identity;

    @JSONField(serialize = false)
    private Double transRate;


    @JSONField(serialize = false)
    private BigDecimal amountRmb;

    @JSONField(serialize = false)
    private String institutionId;

    public ParticipantEntity() {
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof ParticipantEntity)) {
            return false;
        }
        ParticipantEntity invest = (ParticipantEntity) o;
        return Objects.equals(getName(), invest.getName()) && Objects.equals(getKeyNo(), invest.getKeyNo());
    }

    @Override
    public int hashCode() {
        return Objects.hash(getName());
    }

    @Data
    public static class RelationInfo {
        @JSONField(name = "Name")
        private String name;

        @JSONField(name = "KeyNo")
        private String keyNo;

        @JSONField(name = "Org")
        private Integer org;
    }
}
