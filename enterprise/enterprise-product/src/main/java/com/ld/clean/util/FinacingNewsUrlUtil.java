/*
package com.ld.clean.util;

import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;

*/
/**
 * <AUTHOR>
 *//*

public class FinacingNewsUrlUtil {

    public static final Set<String> FINACING_NEWS_URL_ENUMS = new HashSet<>(Arrays.asList("-",
            "null"));

    public static String getNewsUrl(String newsUrl) {
        if (StringUtils.isBlank(newsUrl)) {
            return "未披露";
        }
        if (FINACING_NEWS_URL_ENUMS.contains(newsUrl)) {
            return "未披露";
        }
        return newsUrl;
    }
}
*/
