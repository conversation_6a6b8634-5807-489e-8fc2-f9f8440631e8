package com.ld.clean.util;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.ld.clean.constants.ExchangeAllEnums;
import com.ld.clean.context.ApplicationCoreContextManager;
import com.ld.clean.core.model.ecidata.CompanyExtendTagsV2;
import com.ld.clean.dao.model.searchsyncenterprise.CompanyListingInfo;
import com.ld.clean.job.productv2.baseinfo.entity.RelationCompEntity;
import com.ld.clean.job.productv2.baseinfo.entity.RelationInfo;
import com.ld.clean.model.QccCompanyOutCt;
import com.ld.clean.model.manageglobalsysdb.SysSecCode;
import com.ld.clean.model.tidbsearchsyncenterprise.EpProductFinancingV2Sync;
import com.ld.clean.repository.manageglobalsysdb.SysSecCodeRepository;
import com.ld.clean.utils.DateUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;
import tk.mybatis.mapper.entity.Condition;

import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
public class FinancialSymbolUtils {

    private static final SysSecCodeRepository sysSecCodeRepository = ApplicationCoreContextManager.getInstance(SysSecCodeRepository.class);
    private static final HashMap<String,String> SYMBOL_SUFF_MAP = new HashMap<String,String>(){{put("101",".SH");put("102",".SZ");put("107",".BJ");}};
    private static final HashMap<String,String> LIS_SECTION_MAP = new HashMap<String,String>(){{put("101","沪主板");put("102","深主板");put("107","北交所");put("3","创业板");put("4","科创板");}};
    private static final HashMap<Integer,Integer> FINAN_TAG_SORT_MAP = new HashMap<Integer,Integer>(){{put(2,1);put(9,2);put(6,3);put(401,4);put(10,5);put(7,6);put(11,7);put(25,8);put(1,9);put(8,10);}};
    private static final HashMap<String,String> KEY_FORMAT_MAP = new HashMap<String,String>(){{put("s","ShortName");put("t","Type");put("d","DataExtend");put("n","Name");put("d2","DataExtend2");}};
    private static final HashMap<Integer,Integer> SEC_TYPE_SORTED_MAP = new HashMap<Integer,Integer>(){{put(1,1);put(8,2);put(7,3);}};

    /**
     * @param symbol
     * @return
     */
    public static SysSecCode getSysCodeInfoBySymbolAndSecType(String symbol, int secType) {
        if (StringUtils.isBlank(symbol)) {
            return null;
        }
        Condition condition = new Condition(SysSecCode.class);
        condition.createCriteria().andCondition("symbol = '" + symbol + "' and data_status=1 and sec_type in (1,8)");
        List<SysSecCode> list = sysSecCodeRepository.selectByCondition(condition);
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }
        SysSecCode sysSecCode = list.stream().filter(p -> p.getListStatus().equals(1) || p.getListStatus().equals(0)).findFirst().orElse(null);
        if (sysSecCode == null) {
            sysSecCode = list.stream().filter(p -> p.getListStatus().equals(3)).findFirst().orElse(null);
        }

        return sysSecCode;

    }



    /**
     *  返回工商金融标签
     * @param qccCompany
     * @return
     */

    public static String getFinancialTagsByCompKeyno(QccCompanyOutCt qccCompany, CompanyListingInfo companyListingInfo) {
            if( qccCompany !=null){
                if(qccCompany.getTags()!=null&&!qccCompany.getTags().isEmpty()){
                    String tags =JSON.toJSONString( qccCompany.getTags().stream().filter(itt->FINAN_TAG_SORT_MAP.keySet().contains(itt.getType())).sorted(Comparator.comparing(vvv->{ return FINAN_TAG_SORT_MAP.getOrDefault(vvv.getType(),999); })).collect(Collectors.toList()));
                    return JSON.toJSONString( JSON.parseObject(tags, new TypeReference<List<LinkedHashMap<String, Object>>>() {}).stream().map(map -> {
                        Map<String, Object> updatedMap = new LinkedHashMap<>();
                        map.forEach((key, value) -> { updatedMap.put(Character.toUpperCase(key.charAt(0)) + key.substring(1), value); });
                        return updatedMap;
                    }).collect(Collectors.toList()));
                }
            }
            //美股
            if(companyListingInfo !=null){
                //例子：71bbc48b8b1e480e882cf4117301b286
                if(StringUtils.isBlank(companyListingInfo.getTagsInfo())){
                    return "[]";
                }
                return JSON.toJSONString( JSON.parseObject(companyListingInfo.getTagsInfo(), new TypeReference<List<LinkedHashMap<String, Object>>>() {}).stream().map(map -> {
                    Map<String, Object> updatedMap = new LinkedHashMap<>();
                    if("7".equals(map.getOrDefault("t","").toString()) ||"11".equals(map.getOrDefault("t","").toString()) ||"25".equals(map.getOrDefault("t","").toString()) ){
                        map.forEach((key, value) -> {
                            if(KEY_FORMAT_MAP.containsKey(key)){
                                //OCEA.OTCBB /FLDD.OTCBB 特殊处理 后续金融会进行无效
                                if(value.equals("OCEA.OTCBB")){
                                    updatedMap.put(KEY_FORMAT_MAP.get(key),"OCEA.O");
                                }else if(value.equals("FLDD.OTCBB")){
                                    updatedMap.put(KEY_FORMAT_MAP.get(key),"FLDD.O");
                                }else {
                                    updatedMap.put(KEY_FORMAT_MAP.get(key),value);
                                }

                            }
                        });
                    }

                    return updatedMap;
                }).filter(vv->!vv.isEmpty()).collect(Collectors.toList()));
            }
            return "[]";
    }

    /**
     *  通过事件获取relation 金融标签
     * @param sortList
     * @return
     */

    public static List<RelationInfo> getFinancialTagsByV2Sync(List<EpProductFinancingV2Sync> sortList) {
        List<RelationInfo> relationInfos = new ArrayList<>();
        List<EpProductFinancingV2Sync> v2Syncs = sortList.stream().filter(item -> ("IPO".equals(item.getRound()) || "退市".equals(item.getRound()) || "挂牌".equals(item.getRound()) || "摘牌".equals(item.getRound()))).collect(Collectors.toList());
        if (!v2Syncs.isEmpty()) {

            v2Syncs.stream().filter(vv->StringUtils.isNotBlank(vv.getStockCode())).collect(Collectors.groupingBy(EpProductFinancingV2Sync::getStockCode)).forEach((k,v)->{
                RelationInfo relationInfo = new RelationInfo();
                EpProductFinancingV2Sync financing =v.get(0);

                EpProductFinancingV2Sync v2SyncIpo = v.stream().filter(it -> "IPO".equals(it.getRound()) || "挂牌".equals(it.getRound())).findFirst().orElse(null);
                relationInfo.setListDate("");
                relationInfo.setIsDelist(0);
                if(v2SyncIpo!=null) {
                    String listDate = DateUtil.getStringFormatTimeOfTimestamp(v2SyncIpo.getFinanceDate() * 1000, DateUtil.YMD);
                    relationInfo.setListDate(listDate);
                    relationInfo.setIsDelist(0);
                }


                EpProductFinancingV2Sync v2SyncExit = v.stream().filter(it -> "退市".equals(it.getRound()) || "摘牌".equals(it.getRound())).findFirst().orElse(null);
                if(v2SyncExit !=null) {
                    String deListDate = DateUtil.getStringFormatTimeOfTimestamp(v2SyncExit.getFinanceDate() * 1000, DateUtil.YMD);
                    relationInfo.setDelistDate(deListDate);
                    relationInfo.setIsDelist(1);
                }
                ExchangeAllEnums enums = ExchangeAllEnums.getByDesc(financing.getStockExchange());
                if (ObjectUtil.isNotEmpty(enums)) {
                    relationInfo.setStockExchange(enums.getCode());
                    String exchangeName = "";
                    if (relationInfo.getStockExchange() == 303 || relationInfo.getStockExchange() == 302 || relationInfo.getStockExchange() == 301) {
                        exchangeName = "美股";
                    }else{
                        exchangeName = enums.getDesc();
                    }
                    relationInfo.setFtags(String.format("%s%s %s",StringUtils.isNotBlank(financing.getShortStockExchange())?financing.getShortStockExchange():exchangeName,relationInfo.getIsDelist()==1?"退市":"", financing.getStockCode()));

                }else if(k.endsWith(".NQ")){
                    relationInfo.setStockExchange(103);
                    relationInfo.setFtags(String.format("%s%s %s",StringUtils.isNotBlank(financing.getShortStockExchange())?financing.getShortStockExchange():"新三板",relationInfo.getIsDelist()==1?"摘牌":"", financing.getStockCode()));

                }
                relationInfo.setStockCode(k);
              /*  if (StringUtils.isNotBlank(relationInfo.getStockCode()) && ObjectUtil.isNotEmpty(relationInfo.getStockExchange())) {
                    String exchangeName = "";
                    if (relationInfo.getStockExchange() == 303 || relationInfo.getStockExchange() == 302 || relationInfo.getStockExchange() == 301) {
                        exchangeName = "美股";
                    } else {
                        exchangeName = enums.getDesc();
                    }
                    relationInfo.setFtags(String.format("%s %s",StringUtils.isNotBlank(financing.getShortStockExchange())?financing.getShortStockExchange():exchangeName, financing.getStockCode()));
                }*/
                relationInfo.setSecType(financing.getSecType());
                relationInfo.setListSection(financing.getListSection());
                relationInfo.setRelatedSec(financing.getRelatedSec());




                //退市超两年过滤 T+1 离线数据补充  把两年前的数据筛选出来
                //退市超两年过滤 T+1 离线数据补充  把两年前的数据筛选出来
                //退市超两年过滤 T+1 离线数据补充  把两年前的数据筛选出来


                if( relationInfo.getIsDelist() ==1 &&DateUtil.getTimestampOfStringDate(relationInfo.getDelistDate(),DateUtil.YMD)>=LocalDateTime.now().minusYears(2).toInstant(ZoneOffset.ofHours(8)).toEpochMilli()){
                    relationInfos.add(relationInfo);

                }else  if( relationInfo.getIsDelist() ==0){
                    relationInfos.add(relationInfo);
                }
                //需要标签？
                //relationInfo.setFtags(handleTags(secCode, relationInfo.getIsDelist()));
               // relationInfos.add(relationInfo);
            });

        }
        return relationInfos;
    }


    /**
     *  通过工商金融标签获取relation 金融标签
     * @param compFinancialTags
     * @return
     */
    public static  List<RelationInfo> getFinancialTagsByQccCompany(String compFinancialTags) {
        List<RelationInfo> relationInfos = new ArrayList<>();
        if(StringUtils.isNotBlank(compFinancialTags)){
            List<CompanyExtendTagsV2> tagsV2s = JSON.parseArray(compFinancialTags, CompanyExtendTagsV2.class);
            if (!tagsV2s.isEmpty()) {

                for (CompanyExtendTagsV2 tags : tagsV2s) {
                    RelationInfo relationInfo = new RelationInfo();
                    if(StringUtils.isNotBlank(tags.getDataextend2())) {
                        JSONObject object = JSON.parseObject(tags.getDataextend2());
                        String ld = object.getString("LD");
                        if (StringUtils.isNotBlank(ld)) {
                            relationInfo.setListDate(ld.replace("-", ""));
                            relationInfo.setIsDelist(0);
                        }
                        String dd = object.getString("DD");
                        if (StringUtils.isNotBlank(dd)) {
                            relationInfo.setDelistDate(dd.replace("-", ""));
                            relationInfo.setIsDelist(1);
                        }
                        relationInfo.setRelatedSec(object.getString("Id"));
                    }
                    switch (tags.getType()){
                        case 1:
                        case 8:
                            relationInfo.setSecType(3);
                            relationInfo.setStockExchange(103);
                            relationInfo.setListSection(0);
                            relationInfo.setStockCode(tags.getDataExtend() + ".NQ");
                            relationInfo.setFtags(String.format("%s%s %s", "新三板", relationInfo.getIsDelist() == 1 ? "摘牌" : "", relationInfo.getStockCode()));


                            break;
                        case 2:
                        case 9:
                            relationInfo.setSecType(1);
                            if(StringUtils.isNotBlank(tags.getDataextend2())){
                                JSONObject object =JSON.parseObject(tags.getDataextend2());
                                String lm =object.getString("LM");
                                if("101".equals(lm)){
                                    relationInfo.setListSection(1);
                                    relationInfo.setStockExchange(101);

                                }else if("102".equals(lm)){
                                    relationInfo.setListSection(1);
                                    relationInfo.setStockExchange(102);
                                }else if("107".equals(lm)){
                                    relationInfo.setListSection(1);
                                    relationInfo.setStockExchange(107);
                                }else if("3".equals(lm)){
                                    relationInfo.setListSection(3);
                                    relationInfo.setStockExchange(102);
                                }else if("4".equals(lm)){
                                    relationInfo.setListSection(4);
                                    relationInfo.setStockExchange(101);
                                }
                                relationInfo.setStockCode(tags.getDataExtend()+SYMBOL_SUFF_MAP.getOrDefault(""+relationInfo.getStockExchange(),""));
                                relationInfo.setFtags(String.format("%s%s %s",LIS_SECTION_MAP.getOrDefault(lm,""),relationInfo.getIsDelist()==1?"退市":"",relationInfo.getStockCode()));

                            }
                            break;
                        case 6:
                        case 10:
                        case 401:
                            relationInfo.setSecType(8);
                            relationInfo.setStockExchange(201);
                            relationInfo.setListSection(0);
                            relationInfo.setStockCode(tags.getDataExtend());
                            relationInfo.setFtags(String.format("%s%s %s", "港交所", relationInfo.getIsDelist() == 1 ? "退市" : "", relationInfo.getStockCode()));

                            break;
                        case 7:
                        case 11:
                        case 25:
                            relationInfo.setSecType(7);
                            relationInfo.setListSection(0);
                            relationInfo.setStockCode(tags.getDataExtend().replace("已退市","").trim());
                            if(relationInfo.getStockCode().endsWith(".O")){
                                relationInfo.setStockExchange(301);
                            }else  if(relationInfo.getStockCode().endsWith(".N")){
                                relationInfo.setStockExchange(302);
                            }else if(relationInfo.getStockCode().endsWith(".A")){
                                relationInfo.setStockExchange(303);
                            }else {
                                //后续添加告警 是否有不为此三种结尾  目前设置特殊值
                                relationInfo.setStockExchange(9999);
                            }
                            relationInfo.setFtags(String.format("%s%s %s", "美股", relationInfo.getIsDelist() == 1 ? "退市" : "", relationInfo.getStockCode()));

                            break;

                        default:
                            break;

                    }
                    //需要标签？
                    //relationInfo.setFtags(handleTags(secCode, relationInfo.getIsDelist()));
                    relationInfos.add(relationInfo);
                }
               // return JSONObject.toJSONString(relationInfos);
            }


        }

        return relationInfos;
    }


    /*private static RelationInfo getRelationInfoByTagsType(){

    }*/



    /**
     *  通过事件获取relation 金融标签  合并去重掉重复的stockcode
     * @param sortList
     * @return
     */

    public static String getMergeAllFinancialTags(List<EpProductFinancingV2Sync> sortList,String compFinancailTags) {
        List<RelationInfo>  relationInfos = getFinancialTagsByQccCompany(compFinancailTags);
        List<RelationInfo>  relationInfos1 = getFinancialTagsByV2Sync(sortList);
        relationInfos1.forEach(vv->{
            if(relationInfos.stream().noneMatch(itt->itt.getStockCode().equals(vv.getStockCode()))){
                relationInfos.add(vv);
            }
        });
        return JSON.toJSONString(relationInfos);
    }


    /**
     *  項目標簽互斥規則
     *
     *  IPO 事件更具交易市場排序區時間最大的。。防止有退了再上的
     *  IPO 在多地上市需要展示退市
     * IPO 單地區退市 無視相當於不存在
     * 三板和IPO互斥 有IPO的三板無視相當於不存在
     * 只有三班挂牌的展示挂牌  只有IPO的展示IPO
     *新三板挂牌，新三板摘牌，IPO，IPO（退市）  都退市了無視相當於不存在
     * @param companyRelationInfo
     * @return
     */

    public static String getFinancialTagsMutualExclusion(String companyRelationInfo,String  relationInfo) {
        List<RelationInfo>  relationInfos =new ArrayList<>();
        if(StringUtils.isNotBlank(companyRelationInfo)) {
            relationInfos.addAll(JSON.parseArray(companyRelationInfo, RelationInfo.class));
        }

        if (StringUtils.isNotBlank(relationInfo)) {
            List<RelationCompEntity> contactList = JSONObject.parseArray(relationInfo, RelationCompEntity.class);
            for (RelationCompEntity entity : contactList) {
                if (StringUtils.isNotBlank(entity.getRelationInfo())) {
                    relationInfos.addAll(JSONObject.parseArray(entity.getRelationInfo(), RelationInfo.class));
                }
            }
        }
        //根据市场去重取事件最新最新
        List<RelationInfo>  relationInfosNew =new ArrayList<>();
        List<String> fTags =new ArrayList<>();
        //try {
            relationInfos.stream().collect(Collectors.groupingBy(RelationInfo::getStockExchange)).forEach((k, v) -> {
                relationInfosNew.add(v.stream().max(Comparator.comparing(RelationInfo::getListDate)).get());
            });
        /*}catch (Exception e){
            System.out.println(relationInfos);
            e.printStackTrace();
        }*/


        if(relationInfosNew.stream().filter(vv->vv.getIsDelist() ==0).count()==1 && relationInfosNew.stream().filter(vv->vv.getIsDelist() ==0 &&vv.getSecType()==3).count()==1){
            //上市标签中只有新三板的话展示新三板
            fTags.add(relationInfosNew.stream().filter(vv->vv.getIsDelist() ==0 &&vv.getSecType()==3).findFirst().get().getFtags());
        }else{
            if(relationInfosNew.stream().anyMatch(vv->vv.getIsDelist() ==0)){
                fTags.addAll(relationInfosNew.stream().filter(vv->vv.getSecType()!=3).sorted(
                        Comparator.comparing(
                                (RelationInfo so) -> SEC_TYPE_SORTED_MAP.getOrDefault(so.getSecType(), 9999)
                        ).thenComparing( (RelationInfo so) -> Math.max(StringUtils.isNotBlank(so.getDelistDate())?Integer.valueOf(so.getDelistDate()):0,StringUtils.isNotBlank(so.getListDate())?Integer.valueOf(so.getListDate()):0))
                ).map(RelationInfo::getFtags).distinct().collect(Collectors.toList()));
            }
        }

        return JSON.toJSONString(fTags);
    }

}
