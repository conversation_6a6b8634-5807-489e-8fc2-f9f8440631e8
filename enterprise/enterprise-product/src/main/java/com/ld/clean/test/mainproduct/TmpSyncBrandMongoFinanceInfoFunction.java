package com.ld.clean.test.mainproduct;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.ld.clean.beans.Currency;
import com.ld.clean.constants.CompanyStageEnum;
import com.ld.clean.context.ApplicationCoreContextManager;
import com.ld.clean.dto.company.CompanyCommonOut;
import com.ld.clean.dto.company.OriginalNameOut;
import com.ld.clean.dto.company.QccCompanyOut;
import com.ld.clean.job.product.baseinfo.entity.TagEntity;
import com.ld.clean.model.QccCompanyOutCt;
import com.ld.clean.model.manageenterprisedb.CompVcpeLabelBaseinfo;
import com.ld.clean.model.manageenterprisedb.ProductBaseinfo;
import com.ld.clean.model.searchsyncenterprise.EpProductBaseinfoSync;
import com.ld.clean.model.tidbsearchsyncenterprise.EpProductFinancingV2Sync;
import com.ld.clean.mongo.BaseMongoEnum;
import com.ld.clean.mongo.QccDataMongoTemplate;
import com.ld.clean.parent.AsyncCleanParentFunction;
import com.ld.clean.pojo.mo.ct.MongoProductV2;
import com.ld.clean.model.OriginalNameOveasea;
import com.ld.clean.repository.manageenterprisedb.CompVcpeLabelBaseinfoRepository;
import com.ld.clean.repository.tidbsearchsyncenterprise.EpProductFinancingV2SyncRepository;
import com.ld.clean.utils.CompanyDetailsUtil;
import com.ld.clean.utils.CtCompanyUtils;
import com.ld.clean.utils.MongodbUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import tk.mybatis.mapper.entity.Condition;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 */
@Slf4j
public class TmpSyncBrandMongoFinanceInfoFunction extends AsyncCleanParentFunction<ProductBaseinfo, MongoProductV2> {

    private static QccDataMongoTemplate qccDataMongoTemplate = ApplicationCoreContextManager.getInstance(QccDataMongoTemplate.class);
    private static final CompVcpeLabelBaseinfoRepository compVcpeLabelBaseinfoRepository = ApplicationCoreContextManager.getInstance(CompVcpeLabelBaseinfoRepository.class);
    private static final EpProductFinancingV2SyncRepository epProductFinancingV2SyncRepository = ApplicationCoreContextManager.getInstance(EpProductFinancingV2SyncRepository.class);

    public TmpSyncBrandMongoFinanceInfoFunction(int corePoolSize) {
        super(corePoolSize);
    }

    @Override
    protected MongoProductV2 invoke(ProductBaseinfo productBaseinfo) throws Exception {
        if (1 == 1) {
            /*Condition condition = new Condition(EpProductFinancingV2Sync.class);
            condition.createCriteria().andEqualTo("productId", productBaseinfo.getId()).andEqualTo("dataStatus", 1);*/
           /* List<EpProductFinancingV2Sync> financingList = epProductFinancingV2SyncRepository.selectByCondition(condition);
            if(CollectionUtils.isEmpty(financingList)){
                return null;
            }*/
            MongoProductV2 mongoProductV2 = MongodbUtil.getSingleInstance().selectById(BaseMongoEnum.PROD_ENTERPRISE_DB, productBaseinfo.getId(), MongoProductV2.class);
            /*if (null != mongoProductV2 ) {
                if("未上市".equals(mongoProductV2.getStage())){
                    mongoProductV2.setStage("尚未启动辅导");
                    System.out.println(1);
                    MongodbUtil.getSingleInstance().upsert(BaseMongoEnum.PROD_ENTERPRISE_DB, Collections.singletonList(mongoProductV2), MongoProductV2.class);
                }
                if("IPO申报期".equals(mongoProductV2.getStage())){
                    mongoProductV2.setStage("IPO申报阶段");
                    System.out.println(2);
                    MongodbUtil.getSingleInstance().upsert(BaseMongoEnum.PROD_ENTERPRISE_DB, Collections.singletonList(mongoProductV2), MongoProductV2.class);
                }
                if("上市辅导期".equals(mongoProductV2.getStage())){
                    mongoProductV2.setStage("上市辅导阶段");
                    System.out.println(3);
                    MongodbUtil.getSingleInstance().upsert(BaseMongoEnum.PROD_ENTERPRISE_DB, Collections.singletonList(mongoProductV2), MongoProductV2.class);
                }
            }*/


            /*MongoProductV2 mongoProductV2 = MongodbUtil.getSingleInstance().selectById(BaseMongoEnum.PROD_ENTERPRISE_DB, productBaseinfo.getId(), MongoProductV2.class);
            if (null != mongoProductV2 ) {

                int stageCode = ObjectUtil.isEmpty(productBaseinfo.getCompStageGs())
                        ? productBaseinfo.getCompStageCt()
                        : productBaseinfo.getCompStageGs();
                String stage = CompanyStageEnum.getStageNameByCode(stageCode);
                if(stageCode == 3 || stageCode == 4 || stageCode == 5){
                    mongoProductV2.setStage(stage);
                    MongodbUtil.getSingleInstance().upsert(BaseMongoEnum.PROD_ENTERPRISE_DB, Collections.singletonList(mongoProductV2), MongoProductV2.class);

                }*/


            //mongoProductV2.setFinanceAmounts(productBaseinfo.getFinanceAmounts());
            QccCompanyOut company = null;
                if(StringUtils.isNotBlank(productBaseinfo.getCompanyId())){
                    List<String> fields = Arrays.asList("_id", "CommonList");
                    company = CtCompanyUtils.getCompDetailsByKeyNo(Collections.singletonList(productBaseinfo.getCompanyId()), fields).stream().findFirst().orElse(null);
                    /*List<QccCompanyOut> companyOutList = CompanyDetailsUtil.getCompanyList(Collections.singletonList(productBaseinfo.getCompanyId()), false, fields);
                    if(CollectionUtils.isNotEmpty(companyOutList)){
                        company = companyOutList.get(0);
                    }*/
                }
            if(ObjectUtil.isNotEmpty(company) && ObjectUtil.isNotEmpty(mongoProductV2)) {
                CompanyCommonOut companyCommonOut = company.getCommonList().stream().filter(f -> f.getKey() == 79).findFirst().orElse(null);
                if (ObjectUtil.isNotEmpty(companyCommonOut)) {
                    //企查查行业
                    mongoProductV2.setQccIndustry(companyCommonOut.getValue());
                    MongodbUtil.getSingleInstance().upsert(BaseMongoEnum.PROD_ENTERPRISE_DB, Collections.singletonList(mongoProductV2), MongoProductV2.class);
                }
            }

                /*String portraitTags = null;
                if(ObjectUtil.isNotEmpty(company)){
                    CompanyCommonOut companyCommonOut = company.getCommonList().stream().filter(f -> f.getKey() == 79).findFirst().orElse(null);
                    if(ObjectUtil.isNotEmpty(companyCommonOut)){
                        //企查查行业
                        mongoProductV2.setQccIndustry(companyCommonOut.getValue());
                    }
                    //曾用名
                    List<OriginalNameOut> s = company.getOriginalName();
                    mongoProductV2.setOriginalNameList(s);

                    *//*if(ObjectUtil.isNotEmpty(company.getContactInfo())){
                        //官网
                        List<WebSiteOut> webs = company.getContactInfo().getWebSite();
                        if(CollectionUtils.isNotEmpty(webs)){
                            List<String> urls = webs.stream().map(WebSiteOut::getUrl).filter(url ->url != null && !url.isEmpty()).collect(Collectors.toList());
                            mongoProductV2.setWebsites(JSONObject.toJSONString(urls));
                        }
                        String phoneNum = company.getContactInfo().getPhoneNumner();
                        String email = company.getContactInfo().getEmail();
                        if(ObjectUtil.isEmpty(phoneNum)){
                            mongoProductV2.setPhoneNumber(JSONObject.toJSONString(new ArrayList<>()));
                        }else {
                            mongoProductV2.setPhoneNumber(JSONObject.toJSONString(Collections.singletonList(phoneNum)));
                        }
                        if(ObjectUtil.isEmpty(email)){
                            mongoProductV2.setEmails(JSONObject.toJSONString(new ArrayList<>()));
                        }else {
                            mongoProductV2.setEmails(JSONObject.toJSONString(Collections.singletonList(email)));

                        }
                    }*//*
//画像标签
                    *//*Condition labels =new Condition(CompVcpeLabelBaseinfo.class);
                    labels.createCriteria().andEqualTo("compKeyno",productBaseinfo.getCompanyKeyno()).andEqualTo("vcpeType",4)
                            .andEqualTo("vcpeId",productBaseinfo.getId())
                            .andEqualTo("dataStatus",1);
                    List<CompVcpeLabelBaseinfo> tags =compVcpeLabelBaseinfoRepository.selectByCondition(labels);
                    //目前只获取四种标签
                    if(CollectionUtils.isNotEmpty(tags)){
                         portraitTags = sortTags(tags);
                    }*//*
                }*/


                /*List<TagEntityForMongo> lists = new ArrayList<>();
                //1.获取融资
                TagEntityForMongo roundEntity = new TagEntityForMongo();
                roundEntity.setType(1);
                roundEntity.setName(mongoProductV2.getLastRound());
                lists.add(roundEntity);
                //2.画像
                if(StringUtils.isNotBlank(portraitTags)){
                    List<TagEntity> cList = JSONArray.parseArray(portraitTags, TagEntity.class);
                    for(TagEntity entity :cList){
                        TagEntityForMongo portEntity = new TagEntityForMongo();
                        portEntity.setType(2);
                        portEntity.setName(entity.getInfo());
                        lists.add(portEntity);
                    }
                }
                //3.新兴行业
                if(StringUtils.isNotBlank(productBaseinfo.getIndustryTagsFirst())){
                    List<String> names = new ArrayList<>(Arrays.asList(productBaseinfo.getIndustryTagsFirst().split(",")));
                    for(String name : names){
                        TagEntityForMongo tagEntity = new TagEntityForMongo();
                        tagEntity.setType(3);
                        tagEntity.setName(name);
                        lists.add(tagEntity);
                    }
                }
                mongoProductV2.setTagsInfo(JSONObject.toJSONString(lists));


                mongoProductV2.setFinanceTimes(productBaseinfo.getFinanceTimes());
                String stage = CompanyStageEnum.getStageNameByCode(productBaseinfo.getStageCode());
                mongoProductV2.setStage(stage);*/

                //融资总金额
                //update.set("FinanceAmounts", productBaseinfo.getFinanceAmounts());

                //mongoProductV2.setFinanceAmounts(handleAmounts(financingList));
                //MongodbUtil.getSingleInstance().upsert(BaseMongoEnum.PROD_ENTERPRISE_DB, Collections.singletonList(mongoProductV2), MongoProductV2.class);



                //MongodbUtil.getSingleInstance().updateFieldsById(BaseMongoEnum.PROD_ENTERPRISE_DB, productBaseinfo.getId(), update, MongoProductV2.class);

                // 更新老mongo，待废弃
//                Query query = new Query(Criteria.where("_id").is(productBaseinfo.getId()));
//                qccDataMongoTemplate.getMongoTemplate().updateFirst(query, update, MongoProduct.class);
//                return mongoProductV2;
            //}
        }
        return null;
    }

    private String sortTags(List<CompVcpeLabelBaseinfo> tags) {
        List<TagEntity> entityList = new ArrayList<>();
        List<Integer> orderList = Stream.of(309, 310, 102, 103)
                .collect(Collectors.toList());
        for (CompVcpeLabelBaseinfo tag : tags) {
            Integer type = tag.getLabelStype();
            if (type != null && (type==309 || type==310 || type==102 || type==103)) {
                TagEntity entity = new TagEntity();
                entity.setInfo(tag.getLabelContent());
                entity.setType(type);
                entityList.add(entity);
            }
        }

        entityList.sort(Comparator.comparingInt(entity -> {
            int type = entity.getType();
            return orderList.indexOf(type);
        }));
        return JSONObject.toJSONString(entityList);
    }

    private String handleAmounts(List<EpProductFinancingV2Sync> financeList) {
        if (financeList.size() == 1 && !"未披露".equals(financeList.get(0).getAmount())) {
            return financeList.get(0).getAmount();
        }
        boolean flag = areAmountsValid(financeList);
        if (!flag) {
            return "";
        }
        //多条融资事件
        List<String> amounts = financeList.stream().map(EpProductFinancingV2Sync::getAmount).collect(Collectors.toList());
        //获取所有金额单位
        Set<String> units = amounts.stream().map(Currency::getUnitFromAmountSimple).collect(Collectors.toSet());
        if (units.size() == 1) {
            //币种相同
            String unit = units.iterator().next();
            List<BigDecimal> moneyList = new ArrayList<>();
            for (String amount : amounts) {
                String money = amount.replaceAll("[a-zA-Z$￥?？,，。\\-\u4e00-\u9fa5\u200b\ufeff]", "").replaceAll("\\s*", "").trim();
                // 转换为数字
                BigDecimal decimal = null;
                try {
                    decimal = new BigDecimal(money);
                } catch (Exception e) {
                    log.info("字符串金额转换为数值异常：money={}", amount);
                }
                if (amount.contains("百万")) {
                    decimal = decimal.multiply(new BigDecimal(1000000));
                    moneyList.add(decimal);
                } else if (amount.contains("千万")) {
                    decimal = decimal.multiply(new BigDecimal(10000000));
                    moneyList.add(decimal);
                } else if (amount.contains("亿")) {
                    decimal = decimal.multiply(new BigDecimal(100000000));
                    moneyList.add(decimal);
                } else if (amount.contains("万")) {
                    decimal = decimal.multiply(new BigDecimal(10000));
                    moneyList.add(decimal);
                }else {
                    moneyList.add(decimal);
                }
            }
            if (CollectionUtils.isEmpty(moneyList)) {
                return "";
            }
            BigDecimal sum = moneyList.stream()
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            String finalAmount = formatBigDecimal(sum);
            if (finalAmount.contains(".00")) {
                finalAmount = finalAmount.replace(".00", "");
            }
            return finalAmount + unit;

        } else {
            //币种不同使用rmb 使用rmb
            List<BigDecimal> amountRmb = financeList.stream().map(EpProductFinancingV2Sync::getAmountRmb).collect(Collectors.toList());
            BigDecimal sum = amountRmb.stream()
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            String finalAmount = formatBigDecimal(sum);
            return finalAmount + "人民币";
        }
    }

    private static final BigDecimal ONE_HUNDRED_MILLION = new BigDecimal("100000000");
    private static final BigDecimal TEN_THOUSAND = new BigDecimal("10000");

    private String formatBigDecimal(BigDecimal sum) {
        // 检查 sum 是否大于等于一亿
        if (sum.compareTo(ONE_HUNDRED_MILLION) >= 0) {
            BigDecimal result = sum.divide(ONE_HUNDRED_MILLION, 2, RoundingMode.HALF_UP);
            return formatResult(result) + "亿";
        } else if (sum.compareTo(TEN_THOUSAND) >= 0) {
            BigDecimal result = sum.divide(TEN_THOUSAND, 2, RoundingMode.HALF_UP);
            return formatResult(result) + "万";
        } else {
            return formatResult(sum);
        }
    }

    // 格式化结果，根据是否有小数决定显示
    private String formatResult(BigDecimal value) {
        // 如果值是整数
        if (value.stripTrailingZeros().scale() <= 0) {
            return value.toPlainString(); // 直接返回整数部分
        } else {
            // 保留两位小数
            return value.setScale(2, RoundingMode.HALF_UP).toPlainString();
        }
    }

    private boolean areAmountsValid(List<EpProductFinancingV2Sync> financeList) {
        return financeList.stream()
                .allMatch(item -> {
                    String amount = item.getAmount();
                    return amount != null && !amount.trim().isEmpty() &&
                            !amount.contains("未披露") &&
                            !amount.contains("约") &&
                            !amount.contains("超") &&
                            !amount.contains("数");
                });
    }
}