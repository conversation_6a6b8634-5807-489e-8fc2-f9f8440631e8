package com.ld.clean.util;


import cn.hutool.core.collection.CollectionUtil;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.ld.clean.context.ApplicationCoreContextManager;
import com.ld.clean.dto.company.CompanyTagOut;
import com.ld.clean.job.ipoexitbaseinfo.bo.CompanyTags;
import com.ld.clean.model.QccCompanyOutCt;
import com.ld.clean.model.qccdata.CompanyListingInfo;
import com.ld.clean.repository.qccdata.CompanyListingInfoRepository;
import com.ld.clean.utils.CtCompanyUtils;
import com.ld.clean.utils.Log;
import org.apache.commons.lang.exception.ExceptionUtils;
import org.springframework.beans.BeanUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @description：上市标签工具
 * @author： liupf
 * @create： 2025/2/27 20:46
 */
public class BeListedUtil {

    private static final CompanyListingInfoRepository companyListingInfoRepository = ApplicationCoreContextManager.getInstance(CompanyListingInfoRepository.class);

    private static final List<String> fields = Arrays.asList("_id", "Tags");
    //目前获取类型和排序
    private static final List<Integer> types = Arrays.asList(2, 9, 6, 401, 10, 7, 11, 25, 1, 8, 207, 209);


    public static Map<String, List<CompanyTags>> getTags(List<String> ids) {
        //美股上市
        List<String> lIds = ids.stream().filter(id -> id.startsWith("l")).collect(Collectors.toList());
        //大陆海外
        List<String> otherIds = ids.stream().filter(id -> !id.startsWith("l")).collect(Collectors.toList());
        Map<String, List<CompanyTags>> tagMap = new HashMap<>();
        //排序映射
        Map<Integer, Integer> typeOrderMap =
                types.stream().collect(Collectors.toMap(type -> type, types::indexOf));
        //分类?
        if (CollectionUtil.isNotEmpty(otherIds)) {
            List<QccCompanyOutCt> qccCompanyList = CtCompanyUtils.getCompDetailsByKeyNo(otherIds, fields);
            for (QccCompanyOutCt ct : qccCompanyList) {
                List<CompanyTags> tagsList = new ArrayList<>();
                List<CompanyTagOut> tags = ct.getTags();
                // 获取需要的 tag 并根据 typeOrder 排序
                List<CompanyTagOut> filteredAndSortedTags = tags.stream()
                        .filter(tag -> typeOrderMap.containsKey(tag.getType()))
                        .sorted(Comparator.comparingInt(tag -> typeOrderMap.get(tag.getType())))
                        .collect(Collectors.toList());

                if (CollectionUtil.isNotEmpty(filteredAndSortedTags)) {
                    for (CompanyTagOut tag : filteredAndSortedTags) {
                        //直接获取对象 不是大驼峰
                        CompanyTags companyTagOut = new CompanyTags();
                        BeanUtils.copyProperties(tag, companyTagOut);
                        tagsList.add(companyTagOut);
                    }
                    tagMap.put(ct.getKeyNo(), tagsList);
                }
            }
        }
        //查询金融
        if (CollectionUtil.isNotEmpty(lIds)) {
            List<CompanyListingInfo> infoList = companyListingInfoRepository.selectByPrimaryKeyList(lIds);
            for (CompanyListingInfo info : infoList) {
                String tagsJson = info.getTagsInfo();
                ObjectMapper objectMapper = new ObjectMapper();
                List<CompanyTags> companyTagOutList = new ArrayList<>();
                try {
                    JsonNode arrayNode = objectMapper.readTree(tagsJson);
                    for (JsonNode node : arrayNode) {
                        CompanyTags companyTagOut = new CompanyTags();
                        companyTagOut.setShortName(node.get("s").asText());
                        companyTagOut.setType(node.get("t").asInt());
                        companyTagOut.setName(node.get("n").asText());
                        companyTagOut.setDataExtend(node.get("d").asText());
                        companyTagOut.setDataExtend2(node.get("d2").asText());
                        companyTagOutList.add(companyTagOut);
                    }
                    if (CollectionUtil.isNotEmpty(companyTagOutList)) {
                        // 获取需要的 tag 并根据 typeOrder 排序
                        List<CompanyTags> filteredAndSortedTags = companyTagOutList.stream()
                                .filter(tag -> typeOrderMap.containsKey(tag.getType()))
                                .sorted(Comparator.comparingInt(tag -> typeOrderMap.get(tag.getType())))
                                .collect(Collectors.toList());
                        if (CollectionUtil.isNotEmpty(filteredAndSortedTags)) {
                            tagMap.put(info.getId(), filteredAndSortedTags);
                        }
                    }
                } catch (JsonProcessingException e) {
                    Log.getInst().error(ApplicationCoreContextManager.getAppName(), "金融标签转换异常error:{},数据:{}", ExceptionUtils.getStackTrace(e), tagsJson);
                }
            }
        }
        return tagMap;
    }

}
