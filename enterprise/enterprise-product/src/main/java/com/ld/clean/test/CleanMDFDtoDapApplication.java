package com.ld.clean.test;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.base.clean.utils.financial.FinancialApiUtil;
import com.ld.clean.context.ApplicationCoreContextManager;
import com.ld.clean.dto.company.PromoterOut;
import com.ld.clean.dto.company.QccCompanyOut;
import com.ld.clean.enums.CleanStatusEnum;
import com.ld.clean.enums.DimensionEnum;
import com.ld.clean.job.product.monitorcompanychange.entity.NameKeyNoTagsEntity;
import com.ld.clean.mapper.manageenterprisedb.CompVcpeInfoRepository;
import com.ld.clean.model.manageenterprisedb.CompBrandFinancingMid;
import com.ld.clean.model.manageenterprisedb.CompVcpeInfo;
import com.ld.clean.model.manageenterprisedb.DapBusinessProductFinance;
import com.ld.clean.repository.manageenterprisedb.CompBrandFinancingMidRepository;
import com.ld.clean.repository.manageenterprisedb.ProductBaseinfoRepository;
import com.ld.clean.test.entity.ForMdFdEntity;
import com.ld.clean.utils.CompanyDetailsUtil;
import com.ld.clean.utils.DateUtil;
import com.ld.clean.utils.MD5Util;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import tk.mybatis.mapper.entity.Condition;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 清洗外部补充md fd
 *
 * <AUTHOR>
 */
@Slf4j
public class CleanMDFDtoDapApplication {
    private static final ProductBaseinfoRepository productBaseinfoRepository =ApplicationCoreContextManager.getInstance(ProductBaseinfoRepository.class);
    private static final CompVcpeInfoRepository compVcpeInfoRepository =ApplicationCoreContextManager.getInstance(CompVcpeInfoRepository.class);

    private static final CompBrandFinancingMidRepository compBrand = ApplicationCoreContextManager.getInstance(CompBrandFinancingMidRepository.class);
    public static void main(String[] args) throws Exception {
        List<String> compNos = Arrays.asList(
                "01b67e63618212650c3af62746cfea58",
                "0467bff26fcc583cd9abf082bd1856b7",
                "04d81731ea456bfb49172ab05b8f39dc",
                "0677a60627471f6b266009b23ad391c7",
                "0778173bb157c53de4376b19c09d74cc",
                "0cf833068f8c1e6e5235c9f97c446a12",
                "0d4e5e771b59d6e4f1646546c8d111e9",
                "0e85fa9c7a9584c9544e94e92161a761",
                "1047731b5389b561ad5db14a768d5db3",
                "109c33732172f814df889d85810afae6",
                "144af7c541f6c1314da8c915770a24a5",
                "1702e7fe9b9f56f8304b39a8ef755b12",
                "173754dc49c0595287b6cd5399da0306",
                "1798f3c92de17df509aa1a1ebe86abe3",
                "1a619571e3265aa466f2df5f8a7a01b3",
                "1b311bbdb9cade415505377c833eaadb",
                "1e90bdd5542634153a674ce38efac04d",
                "21a30762a325a737f4d01ee761c698b8",
                "2333df24a619db559efa55c02ef17ea5",
                "235cdb7b60fb375bbc3db42af66d6441",
                "237ae400ec67ebceb703d1d980108639",
                "24de0d0bb08971aaa4183cf7b3bfced9",
                "25242cc1fd3f52fefbd0466ebe49d6a7",
                "2534872d441d3a094f9b688a4411eeff",
                "25ab4fd35f7d11bfff46dc453e8a728d",
                "2857a909982ef3db3371a7f7f804b8b0",
                "29160b7a4c9a279379eedfc10ed77d28",
                "2a2d93265a766b6771614065039eb6ba",
                "2c46576524c84e8d6c63d2119365186f",
                "2c74b143531c156a9a709dde932bb38c",
                "2d0133c7e5d3faf07a457459d68739dd",
                "2d31e6ec40abf5db4e0e0fa684fd942e",
                "2d7ac503110deb83d6f731360e9d1a84",
                "2ee15837722e0d67b871b8f587b4fc91",
                "309a13dbfce6c06f4df4466fad0f243d",
                "31802334c4a7ef77b84f2102ca8a859e",
                "344fdae2b8fbefb30fa5816794d5145f",
                "345c0f8ca50597654a8553094bccafec",
                "348ed1f639bbc8daae09372c38337399",
                "383ea2b43ff49ac62e83b1e2a2b96901",
                "38ddd6ce08ad99da8112c1ca8cdcd58f",
                "3bd900e8e21dc4b8000787fb2d2f1b63",
                "3bf39baf33f22f3ca9d2b5ffc00edbf2",
                "3c72a33d0fb7f7f9ca7d5dba69740e1f",
                "4151857dc24fba08d94eb71b6bfd607e",
                "418c1f1a1cad77e6602b54294fccfc72",
                "44398e4b54f21c76f4e8c8209b3e70c5",
                "464d5fb34fd0d0f3f01548ba5fd8e486",
                "47b09aa3b2e5441584df20ac50f32b7b",
                "484d9ae2d19c8bcc17b5ebd059bbd53b",
                "4875010d6781944cdab5dc1ffb518ddb",
                "49022f9ac32c3ef68199da1d976d1bc3",
                "494fee529334aa8229f41c2545aac582",
                "4ab61535be8b6574c61ab4e584552994",
                "52c9febfbea35715346c4e7c6cae97f9",
                "54aa93433595de64b2d44e52de077dfd",
                "54bfa057be4a4b3f3d11e9188f94cda4",
                "54e72251b901cf2be75be7789440f907",
                "55b2073e3c743be2332053684dc1a36b",
                "55c720cbd2a51d8135606632190026bf",
                "560a1e7f06b00fd63afce5b034ba2a21",
                "5ead733c9600069e25098136ecc9731d",
                "63a4ee784fa63db73cc49c595be4ab75",
                "6631c5fc3bdbbc8fdc469b5efdff2974",
                "684d899f89819173fb0e42a977319943",
                "697b518d2112e0f729cfaee3349349ae",
                "69b79927bf283a077b7e82e8be08fdf5",
                "6bc272ab48cb6ffbf802876f961ea461",
                "6f7ea580f320ab47a28388b07c310012",
                "711d7dfe6f27c49089ab1ac2afe9b708",
                "717435964bdcdd85c8defd6b1c777c8e",
                "739cedf19dfaacb9f97bd9320f6e0bcc",
                "75afd9c77a6b17978b62217d4d29cc26",
                "79c9bbb7cf7b3a204d1c3ce17fcd443e",
                "7c8fa1f24b2575e4440cc83e11c33d51",
                "7d4e91c3697d2c969b4699ce77bef2da",
                "7e4f1d4a96a6b54fd1250f8f79291ba0",
                "7f6bf9e993ed2c455c8ed4fa87840bdf",
                "7f90b0e4777b71dd44d989e7ffa0b222",
                "843a962614745b0bbbb1640b814e8966",
                "8558394f94b2c40759c0f252c1aa8c29",
                "85616e99d8fbc107ad3d4d50ac85ff51",
                "858aa887d750503cfee26305d52fe063",
                "8705964676953bb4111fe846a63450e4",
                "8721bd10eff55289d2de84fc6600d663",
                "87233b374bf6c8fba16e7695b4320537",
                "875575c6aae8a1fb3f1aac23419d0b99",
                "88f9693e69ce533ffd05d8635b85e940",
                "8a230830a58cbed6d773b389d1df6daa",
                "8d1953cf76b4faee120602379862b3b0",
                "8d3264fc09a141c529551910ac62bc6a",
                "8ee8d9698f2ae34e0e70bc148c0fdff3",
                "8fa0a20fe8e6c3b06a977e26b41efa97",
                "90b85c407e66d07a2c52ec51657338d9",
                "922eec9b84a1327bf7da2ef1d54e6882",
                "92ad82359f0c0166acebedb877316e67",
                "93ab6a0ad566e76567b1b62eea427022",
                "93b6235a54f24ec5357f90c5bfc3d800",
                "964e720e37dc13d39e4a79be403c25c4",
                "967a75aab511c052cc4f2f8b455aac60",
                "96e60a238c373e72b3be8407b02298a2",
                "98e0405207c051700ba6892e9f9e91be",
                "99d3cbd1f4d6a816f16367b36849e801",
                "9d2dbaa8a7cfbc4ead4bab1fd83415e7",
                "9d46e33587c5ecf803ce1658afda2317",
                "a3ac983dc12b3b80ebf61175bae21e32",
                "a80924d8316af14fa9abc520b1e73cb3",
                "a86fc5c48ba410a36032d46fc32142e3",
                "a8866099a4640d5a479b2939c9f4ca84",
                "a8c45b75ccfd6367d5ee08e81886ac5f",
                "ab1608c6d907c1c92224942be35a4605",
                "aefbc9724a0ea35f62bd84682ba17f9e",
                "b0e289ceb44cc8983ffc898ae78715b5",
                "b406ac1494c35f0540e241e976e19693",
                "b8601300b58623a41636adfc201f63d1",
                "bca72346b886e72de315ff8aa0b46df9",
                "bcd12c3de375c38492a4b50ea2fcbf05",
                "be51c4fddd33fd88988c4ea31412b166",
                "bef84b595fefe7eff5ae09ea3cb47556",
                "bf54c19b4848add9525c3893ffaeeb16",
                "c14052f0d702f713e0b5a6c40f8f7a69",
                "c34c74787001c91d9f679e02bd05c4d7",
                "c4d5718e370c26db6d892d0333302f96",
                "cab3d82488bd8516ade33103366d1ca2",
                "cb57282c7e2d0bf327d04847470e3a3b",
                "ccc492a1f56bd8ae0ca0f0f8b06935cc",
                "ce5293acf39f169ea0b6d4a17110ce08",
                "ce5bdd709775fbcd53f0e6d8266ab16c",
                "cf3e25b89cc6dad3f8e9896350dc9a19",
                "cf8357951d52f30d4d2830085bf75157",
                "cf9cd4659ba7df3c32702b559d6a4937",
                "d2cd6971685b51d242076f8ada2bf446",
                "d436acbbbf3f17376d3b34986f8da39a",
                "d5aa318204fe0a6f8d76ced100365011",
                "d6b60d74292fe2b0c9e740e1e2b0dbaf",
                "d6e20e2c130f6a469fb18dd8abee1ddd",
                "d96aba5a64576aef8ec7607255f4d1ad",
                "da0c31744291ea625cac23af5b93dc10",
                "dad18e35782e6f2db91f13e21fab47bd",
                "dbd5c5f48d374a0adc7656600421ede7",
                "dcd053a2eb6a95f8ad94aab8a79e6486",
                "ddf342cc3a7dbee1e71393d03a05be5e",
                "e02e71dfad1055153ffbe9be4340d2e8",
                "e1b0326ada9ffc380f881bc55a1cde95",
                "e3e50ffa81ae8fea27e2b7df2027ed6c",
                "e3f6865a505ce49ba30826032b39d110",
                "e4396bca6a78d5fbb35e31bb78bf79ad",
                "e5440659c60a28e0a331366734162f58",
                "e70b393b5aa567d15daa056350970d2e",
                "e92876511641fedd28fa0620aee60e3a",
                "eaebc674e7fd859b6fa0c16595503aaa",
                "eb9402cea3b1ea6767713ce383f6041d",
                "ec6eed53af0ecff3ad15c15c6f91e480",
                "f2001b09e983eb9c2970fbb6268949d2",
                "f40235cd011decd8b1d6f545b42fe4cc",
                "f469e3344241a8220070defd531d08f7",
                "f539a8651f216b93c6d71aaca7353cb5",
                "f66453c2bb113b3605875a1301dddde5",
                "f6f84be9a162715437f89f0ea50f4cd3",
                "f74746f2988c3669d4aa31565d8e8fdc",
                "f7ecb2948ba2c273c57dc5c4b836cd3b",
                "fa7799d842c2a63b917ed3a3a7bd3b9d",
                "fdddec30462d44e73920a923ddfdab51",
                "fe2a774d482dc00e8e1dce117d82f94a");

        List<CompBrandFinancingMid> midList = compBrand.selectByPrimaryKeyList(compNos);


        System.out.println(midList.size());
    }


    public static String getStringFormatTimeOfSecondTimestamp(Long timStamp, String pattern) {
        String stringFormatTime = "";
        try {
            if (timStamp != null && StringUtils.isNotBlank(pattern)) {
                stringFormatTime = DateUtil.getStringFormatTimeOfTimestamp(timStamp * 1000, pattern);
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        return stringFormatTime;
    }
}
