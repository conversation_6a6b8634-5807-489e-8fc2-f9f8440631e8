package com.ld.clean.beans;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.util.Objects;

/**
 * <AUTHOR>
 */
@Data
public class InvestEntity {
    @JSONField(name = "Id")
    private String id;

    @JSONField(name = "VcpeType")
    private String vcpeType;

    @JSONField(name = "InType")
    private String inType;

    @JSONField(name = "Name")
    private String name;

    @JSONField(name = "KeyNo")
    private String keyNo;
    @JSONField(name = "Org")
    private Integer org;

    /**
     * 1：领投，2：跟投，0 未知
     */
    @JSONField(name = "Type")
    private Integer type;
    /**
     * 1：投资机构，2：公司，0：个人 3:母基金（待添加）
     */
    @JSONField(name = "Category")
    private Integer category;

    // 原始投资方名称
    @JSONField(name = "OriginalName")
    private String OriginalName;

    // 创投主体id
    @JSONField(name = "VcpeId")
    private String vcpeId;

    public InvestEntity() {

    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof InvestEntity)) {
            return false;
        }
        InvestEntity invest = (InvestEntity) o;
        return Objects.equals(getName(), invest.getName()) && Objects.equals(getKeyNo(), invest.getKeyNo()) && Objects.equals(getVcpeId(), invest.getVcpeId());
    }

    @Override
    public int hashCode() {
        return Objects.hash(getName());
    }
}
