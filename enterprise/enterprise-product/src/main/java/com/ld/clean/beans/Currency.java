package com.ld.clean.beans;

import com.ld.clean.util.FinacingAmountUtil;
import com.ld.clean.util.StringUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 */
@Data
@Slf4j
public class Currency {

    private String amount;
    private String amountHide;
    private Long amountRmb = 0L;
    private BigDecimal amountDecimalRmb;

    public static Map<String, Double> CURRENCY = new HashMap<>();

    static {
        // 其它币种转换人民币系数
        CURRENCY.put(null, 1.0);
        CURRENCY.put("", 1.0);
        CURRENCY.put("RMB", 1.0);
        CURRENCY.put("USD", 6.6151);
        CURRENCY.put("POUND", 8.9019);
        CURRENCY.put("EURO", 7.8263);
        CURRENCY.put("SINGAPORE", 4.9004);
        CURRENCY.put("AUSTRALIA", 5.0017);
        CURRENCY.put("JPY", 0.05885);
        CURRENCY.put("HK", 0.8472);
        CURRENCY.put("美元", 6.6151);
        CURRENCY.put("韩元", 0.005774);
        CURRENCY.put("韩国元", 0.005774);
        CURRENCY.put("美金", 6.6151);
        CURRENCY.put("港元", 0.8472);
        CURRENCY.put("港币", 0.8472);
        CURRENCY.put("英镑", 8.9019);
        CURRENCY.put("镑", 8.9019);
        CURRENCY.put("欧元", 7.8263);
        CURRENCY.put("欧", 7.8263);
        CURRENCY.put("新加坡元", 4.9004);
        CURRENCY.put("新加坡币", 4.9004);
        CURRENCY.put("日元", 0.05885);
        CURRENCY.put("日圆", 0.05885);
        CURRENCY.put("澳大利亚元", 5.0017);
        CURRENCY.put("元", 1.0);
        CURRENCY.put("人民币", 1.0);
        CURRENCY.put("卢布", 0.1089);
        CURRENCY.put("加拿大元", 5.188);
        CURRENCY.put("印度卢比", 0.087185);
        CURRENCY.put("新台币", 0.2265);
    }

    /**
     * 提取金额
     *
     * @param rate
     * @param money 字符串金额
     * @param unit  币种
     * @return 金额数，单位： 元
     */
    public static Currency getRmb(String money, String unit, Double rate) {
        if (StringUtils.isEmpty(money) || "-".equalsIgnoreCase(money)) {
            money = "未披露";
        }

        Currency currency = new Currency();
        // 对于无法识别的币种，或者是0开头，即是介绍上市,不做处理
        if (StringUtils.isEmpty(unit) || (money.startsWith("0") && !money.startsWith("0."))) {
            currency.setAmount(money);
            currency.setAmountRmb(0L);
            currency.setAmountDecimalRmb(new BigDecimal(0));
            return currency;
        }

        unit = getUnit(unit);
        String error = money;

        // 分离汉字和数字
        List<String> words = StringUtil.getChinese(money);
        String chinese = "";
        String prefix = "";
        if (words.size() == 1) {
            chinese = words.get(0);
        } else if (words.size() == 2) {
            prefix = words.get(0);
            chinese = words.get(1);
        }
        money = money.replaceAll("[a-zA-Z$￥?？,，。\\-\u4e00-\u9fa5\u200b\ufeff]", "").replaceAll("\\s*", "").trim();

        // 转换为数字
        BigDecimal decimal;
        try {
            decimal = new BigDecimal(money);
        } catch (Exception e) {
            log.info("字符串金额转换为数值异常：money={}", error);
            return errorCurrency(error, unit, rate);
        }

        // 如果是整数，显示的时候取整；如果有小数，显示的时候保留两位小数
        double amount = decimal.setScale(2, BigDecimal.ROUND_HALF_DOWN).doubleValue();
        String show;
        long a = Math.round(amount);
        if (a - amount == 0L) {
            show = a + "";
        } else {
            show = amount + "";
        }

        // 约数的人民币金额加上“元”后缀
        unit = StringUtils.isNotBlank(prefix) && unit.equalsIgnoreCase("人民币") ? "元" + unit : unit;

        // 将汉字数量级转化为数字
        if (chinese.contains("百万")) {
            currency.setAmount(prefix + show + "百万" + unit);
            decimal = decimal.multiply(new BigDecimal(1000000));
        } else if (chinese.contains("千万")) {
            currency.setAmount(prefix + show + "千万" + unit);
            decimal = decimal.multiply(new BigDecimal(10000000));
        } else if (chinese.contains("亿")) {
            currency.setAmount(prefix + show + "亿" + unit);
            decimal = decimal.multiply(new BigDecimal(100000000));
        } else if (chinese.contains("万")) {
            currency.setAmount(prefix + show + "万" + unit);
            decimal = decimal.multiply(new BigDecimal(10000));
        } else {
            currency.setAmount(prefix + show + unit);
        }

        // 币种转换为人民币，精确到个位
        Double d = rate;
        d = d == null ? 1.0 : d;
        decimal = decimal.multiply(new BigDecimal(d));

        // 根据文字描述，略微区分人民币计算金额
        BigDecimal bigDecimal = generateDecimalRmbByDesc(decimal, currency.getAmount());

        currency.setAmountDecimalRmb(bigDecimal);
        currency.setAmountRmb(bigDecimal.longValue());

        return currency;
    }

    private static long generateRmbByDesc(long amountRmb, String money) {
        if (StringUtils.isBlank(money)) {
            return amountRmb;
        }
        if (money.contains("数")) {
            return 2 * amountRmb + 1;
        } else if (money.contains("约")) {
            return amountRmb - 1;
        } else if (money.contains("不超")) {
            return amountRmb - 2;
        } else if (money.contains("超")) {
            return amountRmb + 1;
        } else {
            return amountRmb;
        }
    }

    private static BigDecimal generateDecimalRmbByDesc(BigDecimal amount, String money) {
        if (StringUtils.isBlank(money)) {
            return new BigDecimal(0);
        }
        if (money.contains("数")) {
            return amount.multiply(new BigDecimal(2)).add(new BigDecimal(1));
        } else if (money.contains("约")) {
            return amount.subtract(new BigDecimal(1));
        } else if (money.contains("不超")) {
            return amount.subtract(new BigDecimal(2));
        } else if (money.contains("超")) {
            return amount.add(new BigDecimal(1));
        } else {
            return amount;
        }
    }

    /**
     * 无法提取数字金额，再次清洗金额
     *
     * @param amount 金额描述
     * @param unit   币种描述
     */
    private static Currency errorCurrency(String amount, String unit, double rate) {
        unit = StringUtils.isEmpty(unit) ? getUnitFromAmount(amount) : unit;
        int cnNumber = FinacingAmountUtil.getCNNumber(amount);

        BigDecimal decimal = new BigDecimal(rate * cnNumber);
        Currency currency = new Currency();

        // 汇率表对应的货币名称转换
        amount = formatAmount(amount);

        unit = getUnit(unit);

        if (amount.contains("百万")) {
            decimal = decimal.multiply(new BigDecimal(1000000));
            currency.setAmount(amount.contains(unit) ? amount : amount + unit);
        } else if (amount.contains("千万")) {
            decimal = decimal.multiply(new BigDecimal(10000000));
            currency.setAmount(amount.contains(unit) ? amount : amount + unit);
        } else if (amount.contains("亿")) {
            decimal = decimal.multiply(new BigDecimal(100000000));
            currency.setAmount(amount.contains(unit) ? amount : amount + unit);
        } else if (amount.contains("万")) {
            decimal = decimal.multiply(new BigDecimal(10000));
            currency.setAmount(amount.contains(unit) ? amount : amount + unit);
        } else {
            decimal = decimal.multiply(BigDecimal.ONE);
            currency.setAmount(amount);
        }
        String newAmount = currency.getAmount();
        // 不存在 级 这种录入方式
        newAmount = newAmount.replace("级", "");
        if (!newAmount.contains("数十") && newAmount.contains("十")) {
            newAmount = newAmount.replace("十", "10");
        }

        // 替换所有中文
        for (Map.Entry<String, Integer> entry : FinacingAmountUtil.AMOUNT_CN_NUMBER_MAP.entrySet()) {
            if (entry.getKey().equalsIgnoreCase("十") || entry.getKey().equalsIgnoreCase("拾")) {
                continue;
            }
            newAmount = newAmount.replace(entry.getKey(), entry.getValue().toString());
        }

        if (!newAmount.contains("元人民币") && newAmount.contains("人民币")) {
            newAmount = newAmount.replace("人民币", "元人民币");
        }
        currency.setAmount(newAmount);

        // 根据文字描述，略微区分人民币计算金额
        BigDecimal bigDecimal = generateDecimalRmbByDesc(decimal, currency.getAmount());

        currency.setAmountDecimalRmb(bigDecimal);
        currency.setAmountRmb(bigDecimal.longValue());

        return currency;
    }

    public static void main(String[] args) {
        System.out.println(StringUtil.getChinese("50万美元"));
    }

    private static String formatAmount(String amount) {
        return amount.replace("澳大利亚元", "澳元")
                .replace("加拿大元", "加元")
                .replace("韩国元", "韩元")
                .replace("印度卢比", "卢比");
    }

    /**
     * 币种描述转换为汉字
     *
     * @param unit 英文币种
     * @return 汉字币种
     */
    private static String getUnit(String unit) {
        switch (unit) {
            case "RMB":
                return "人民币";
            case "USD":
                return "美元";
            case "POUND":
                return "英镑";
            case "EURO":
                return "欧元";
            case "SINGAPORE":
                return "新加坡元";
            case "AUSTRALIA":
                return "澳元";
            case "JPY":
                return "日元";
            case "HK":
                return "港币";
            case "澳大利亚元":
                return "澳元";
            case "韩国元":
                return "韩元";
            case "加拿大元":
                return "加元";
            case "印度卢比":
                return "卢比";
            default:
                return unit;
        }
    }

    /**
     * 从金额描述中获取币种
     *
     * @param amount 金额描述，例如：5000万美元，1.5亿人民币
     * @return 汉字币种
     */
    public static String getUnitFromAmount(String amount) {
        if (StringUtils.isEmpty(amount) || amount.contains("人民币")) {
            return "人民币";
        }
        if (amount.contains("美元")) {
            return "美元";
        }
        if (amount.contains("英镑")) {
            return "英镑";
        }
        if (amount.contains("欧元")) {
            return "欧元";
        }
        if (amount.contains("新加坡元")) {
            return "新加坡元";
        }
        if (amount.contains("澳大利亚元") || amount.contains("澳元")) {
            return "澳大利亚元";
        }
        if (amount.contains("日元")) {
            return "日元";
        }
        if (amount.contains("港币") || amount.contains("港元")) {
            return "港币";
        }
        if (amount.contains("卢布")) {
            return "卢布";
        }
        if (amount.contains("韩元") || amount.contains("韩国元")) {
            return "韩国元";
        }
        if (amount.contains("加元") || amount.contains("加拿大元")) {
            return "加拿大元";
        }
        if (amount.contains("新台币")) {
            return "新台币";
        }
        if (amount.contains("印尼卢比")) {
            return "印尼卢比";
        }
        if (amount.contains("沙特里亚尔")) {
            return "沙特里亚尔";
        }
        if (amount.contains("阿联酋迪拉姆")) {
            return "阿联酋迪拉姆";
        }
        if (amount.contains("瑞典克朗")) {
            return "瑞典克朗";
        }
        if (amount.contains("林吉特")) {
            return "林吉特";
        }
        if (amount.contains("印度卢比") || amount.contains("卢比")) {
            return "印度卢比";
        }
        if (amount.contains("菲律宾比索")) {
            return "菲律宾比索";
        }
        if (amount.contains("新西兰元")) {
            return "新西兰元";
        }
        if (amount.contains("瑞士法郎")) {
            return "瑞士法郎";
        }
        if(amount.contains("土耳其里拉")){
            return "土耳其里拉";
        }
        if(amount.contains("丹麦克朗")){
            return "丹麦克朗";
        }
        if(amount.contains("挪威克朗")){
            return "挪威克朗";
        }

        return "";
    }

    /**
     * 从金额描述中获取币种
     *
     * @param amount 金额描述，例如：5000万美元，1.5亿人民币
     * @return 汉字币种
     */
    public static String getUnitFromAmountSimple(String amount) {
        if (StringUtils.isEmpty(amount) || amount.contains("人民币")) {
            return "人民币";
        }
        if (amount.contains("美元")) {
            return "美元";
        }
        if (amount.contains("英镑")) {
            return "英镑";
        }
        if (amount.contains("欧元")) {
            return "欧元";
        }
        if (amount.contains("新加坡元")) {
            return "新元";
        }
        if (amount.contains("澳大利亚元") || amount.contains("澳元")) {
            return "澳元";
        }
        if (amount.contains("日元")) {
            return "日元";
        }
        if (amount.contains("港币") || amount.contains("港元")) {
            return "港币";
        }
        if (amount.contains("卢布")) {
            return "卢布";
        }
        if (amount.contains("韩元") || amount.contains("韩国元")) {
            return "韩元";
        }
        if (amount.contains("加元") || amount.contains("加拿大元")) {
            return "加元";
        }
        if (amount.contains("新台币")) {
            return "新台币";
        }
        if (amount.contains("印尼卢比")) {
            return "印尼卢比";
        }
        if (amount.contains("沙特里亚尔")) {
            return "沙特里亚尔";
        }
        if (amount.contains("阿联酋迪拉姆")) {
            return "阿联酋迪拉姆";
        }
        if (amount.contains("瑞典克朗")) {
            return "瑞典克朗";
        }
        if (amount.contains("林吉特")) {
            return "林吉特";
        }
        if (amount.contains("印度卢比") || amount.contains("卢比")) {
            return "卢比";
        }
        if (amount.contains("菲律宾比索")) {
            return "菲律宾比索";
        }
        if (amount.contains("新西兰元")) {
            return "新西兰元";
        }
        if (amount.contains("瑞士法郎")) {
            return "瑞士法郎";
        }
        if(amount.contains("土耳其里拉")){
            return "土耳其里拉";
        }
        if(amount.contains("丹麦克朗")){
            return "丹麦克朗";
        }
        if(amount.contains("挪威克朗")){
            return "挪威克朗";
        }
        if(amount.contains("雷亚尔")){
            return "雷亚尔";
        }
        return "";
    }
}
