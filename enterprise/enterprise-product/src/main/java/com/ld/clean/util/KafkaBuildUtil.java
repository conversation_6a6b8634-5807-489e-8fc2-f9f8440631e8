package com.ld.clean.util;

import com.ld.clean.kafka.CleanFlinkKafkaConsumer;
import com.ld.clean.kafka.CleanFlinkKafkaProducer;
import com.ld.clean.kafka.KafkaHelper;
import org.apache.flink.streaming.connectors.kafka.FlinkKafkaConsumer;
import org.apache.flink.streaming.connectors.kafka.FlinkKafkaProducer;

/**
 * <AUTHOR>
 * @date 2021/6/18 19:54
 * @description
 */
public class KafkaBuildUtil {

    public static FlinkKafkaConsumer<String> buildConsumer(String topic, String groupId) {
        return KafkaHelper.buildAuthKafkaSource(CleanFlinkKafkaConsumer
                .builder()
                .topic(topic)
                .groupId(groupId)
                .build());
    }

    public static FlinkKafkaProducer<String> buildProducer(String topic) {
        return KafkaHelper.buildAuthKafkaSink(CleanFlinkKafkaProducer
                .builder()
                .topic(topic)
                .build());
    }
}
