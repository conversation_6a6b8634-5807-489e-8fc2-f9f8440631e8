package com.ld.clean.constants;

import lombok.Getter;

/**
 * 投资方类型
 *
 * <AUTHOR>
 */
@Getter
public enum InvestTypeEnum {
    OTHER(-1, "未知"),
    INSTITUTION(1, "投资机构"),
    COMPANY(2, "企业公司"),
    PERSON(0, "个人"),
    PRODUCT(3, "产品");// 待废弃

    private Integer code;

    private String desc;


    InvestTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static InvestTypeEnum getByCode(int code) {
        for (InvestTypeEnum roundLevelEnum : InvestTypeEnum.values()) {
            if (code == roundLevelEnum.getCode()) {
                return roundLevelEnum;
            }
        }
        return OTHER;
    }

}

