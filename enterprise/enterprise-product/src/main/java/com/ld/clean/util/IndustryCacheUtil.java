package com.ld.clean.util;


import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.ld.clean.context.ApplicationCoreContextManager;
import com.ld.clean.model.qccproduct.ProductIndustry;
import com.ld.clean.repository.qccproduct.ProductIndustryRepository;
import lombok.Synchronized;
import org.apache.commons.collections.CollectionUtils;
import tk.mybatis.mapper.entity.Condition;

import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

public class IndustryCacheUtil {
    private static ProductIndustryRepository productIndustryRepository = ApplicationCoreContextManager.getInstance(ProductIndustryRepository.class);

    private static Cache<String,List<ProductIndustry>> cache = CacheBuilder.newBuilder()
            // .recordStats()
            .maximumSize(3000)
            .expireAfterWrite(24, TimeUnit.HOURS)
            .build();

    @Synchronized
     public static List<ProductIndustry> getNameByCode(List<String> codes){
         List<ProductIndustry> productIndustryList = cache.getIfPresent("industry");
         if (productIndustryList == null) {
             Condition condition = new Condition(ProductIndustry.class);
             //condition.createCriteria().andEqualTo("dataStatus",1);
             productIndustryList =productIndustryRepository.selectByCondition(condition);
             cache.put("industry", productIndustryList);
         }
         return productIndustryList.stream().filter(itt->codes.contains(itt.getCode())).collect(Collectors.toList());
     }

    @Synchronized
    public static ProductIndustry getNameByCode(String code){
        List<ProductIndustry> productIndustryList = cache.getIfPresent("industry");
        if (productIndustryList == null) {
            Condition condition = new Condition(ProductIndustry.class);
            //condition.createCriteria().andEqualTo("dataStatus",1);
            productIndustryList =productIndustryRepository.selectByCondition(condition);
            cache.put("industry", productIndustryList);
        }
        productIndustryList = productIndustryList.stream().filter(productIndustry -> productIndustry.getCode().equals(code)).collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(productIndustryList)){
            return productIndustryList.get(0);
        }
        return null;
    }
}
