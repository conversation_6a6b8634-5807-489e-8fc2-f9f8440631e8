package com.ld.clean.test;

import com.alibaba.fastjson.JSON;
import com.ld.clean.beans.ParticipantEntity;
import com.ld.clean.context.ApplicationCoreContextManager;
import com.ld.clean.kafka.KafkaHelper;
import com.ld.clean.model.tidbsearchsyncenterprise.EpProductFinancingV2Sync;
import com.ld.clean.repository.manageenterprisedb.CompInvestInstitutionBaseinfoV2Repository;
import com.ld.clean.repository.tidbsearchsyncenterprise.EpProductFinancingV2SyncRepository;
import com.ld.clean.test.entity.FinancialExcel;
import com.ld.clean.utils.DateUtil;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import tk.mybatis.mapper.entity.Condition;

import java.util.*;
import java.util.stream.Collectors;

public class CvdInvestorTypeExcelTest {
    private static final CompInvestInstitutionBaseinfoV2Repository compInvestInstitutionBaseinfoV2Repository = ApplicationCoreContextManager.getInstance(CompInvestInstitutionBaseinfoV2Repository.class);
    private static final EpProductFinancingV2SyncRepository epProductFinancingV2SyncRepository = ApplicationCoreContextManager.getInstance(EpProductFinancingV2SyncRepository.class);

    /* public static void main(String[] args) {
         System.out.println("xxx");
     }*/
    public static void main(String[] args) throws Exception {
        System.out.println("step1");
        try {
            XSSFWorkbook book = new XSSFWorkbook("C:\\Users\\<USER>\\Desktop\\融资事件待补充与线上数据对比_90天内投资方完全相同的数据(考虑关联机构或项目)_to数产.xlsx");

            XSSFSheet sheet = book.getSheet("Sheet1");

            int lastRowNum = sheet.getLastRowNum();


            List<FinancialExcel> financialExcels = new ArrayList<>();

            for (int i = 1; i <= 2531; i++) {
                XSSFRow row = sheet.getRow(i);

                if (row != null) {

                    FinancialExcel excel = new FinancialExcel();
                    for (int j = 0; j <= 17; j++) {
                        Cell cell = row.getCell(j);

                        if (cell != null) {
                            cell.setCellType(Cell.CELL_TYPE_STRING);
                            String value = cell.getStringCellValue();
                            switch (j) {
                                case 0:
                                    excel.setId(value);
                                    break;
                                case 1:
                                    excel.setPid(value);
                                    break;
                                case 4:
                                    excel.setFinanceDateCurrent(value);
                                case 12:
                                    excel.setFinanceDateExcel(value);
                                    break;
                                case 14:
                                    excel.setParticipantDetails(value);
                                    break;
                                default:
                                    break;

                            }

                        }

                    }

                    financialExcels.add(excel);
                }

            }

            List<String> pids = financialExcels.stream().filter(vv -> StringUtils.isNotBlank(vv.getPid())).map(FinancialExcel::getPid).collect(Collectors.toList());
            Condition condition = new Condition(EpProductFinancingV2Sync.class);
            condition.createCriteria().andEqualTo("dataStatus", 1).andIn("productId", pids);
            List<EpProductFinancingV2Sync> epProductFinancingV2Syncs = epProductFinancingV2SyncRepository.selectByCondition(condition);

            List<String> testData = Arrays.asList(
                    "e083fc716afb49298f0c0acbfb781ccd",
                    "94099f7df90d459ca7ae2ca716cf6167",
                    "2d01594f64bc4cefa5676f984a8b2b43",
                    "4f10ab415aa94fb0a50a2c33115b0b69",
                    "eadb4492843f498c85b4397ba2f6b33f",
                    "9dff72002c0e933c56fd75717a9ed429",
                    "7e32b82361eab78336ffe5349ff9eacd",
                    "8ec5325104e6c8053622fe4c9e1ad57a",
                    "d36117508a7151033ca0322696b574db",
                    "54cdf73c76dcb89c24d7a9234830c485",
                    "4adeed1ecff460b834f4606d3a678c42",
                    "315c0dc9deef77c52439a8b44deac4b2",
                    "aa0f0faa-9b2b-48ac-87d8-ba7ceef8a9e1",
                    "06aef482b866ff0d037f545b07e25de0",
                    "101336e52a3c4ea9b5bbbe936cfeebe7",
                    "651a0db84aa645e59066bb5765d7666a",
                    "14f3f5ad1a2346fcbd637733f187f818",
                    "31704b658729adfc86c0f68f2985f2d1",
                    "4f8de68540bda2fc61aa984c4f30a6bd",
                    "5be905e8dd85466eb2d4da96323479a8",
                    "12e74d03cc8b1d61e1e0b2f3589fb4f7",
                    "bff6184c-f79e-440e-a8d4-22ff10ef90e2",
                    "9aae4368b3004774db9834f4835281ae",
                    "148bfd587e7b4335b141a8b4316e2fcd",
                    "c400db3363b99ee026bc9ed0741b79df"

            );


            financialExcels.stream().filter(vv -> !testData.contains(vv.getId())).forEach(it -> {
                EpProductFinancingV2Sync vv = epProductFinancingV2Syncs.stream().filter(hh -> hh.getId().equals(it.getId())).findFirst().orElse(null);
                if (vv != null) {

                    if(StringUtils.isNotBlank(vv.getParticipantDetails())){
                        List<ParticipantEntity> result1 = JSON.parseArray(vv.getParticipantDetails(), ParticipantEntity.class);
                        if(result1.stream().anyMatch(vvv->vvv.getType()==1)){
                            System.out.println(it.getId() + ":企业投资方存在领头");

                        }
                    }
                    if (vv.getSourceType() == 14) {
                        //判断投资方是否一致  还有看看sourcetype 是否可修改

                        //如果一致的话


                        if (StringUtils.isNotBlank(it.getParticipantDetails())) {
                            List<ParticipantEntity> result = JSON.parseArray(it.getParticipantDetails(), ParticipantEntity.class);
                            result.stream().filter(v1v -> StringUtils.isNotBlank(v1v.getName())).map(ParticipantEntity::getName).distinct().collect(Collectors.joining(","));

                            List<HashMap<String, Object>> data = new ArrayList<>();
                            data.add(new HashMap<String, Object>() {{
                                put("source_type", 21);
                                put("finance_date", it.getFinanceDateExcel().replace("-", ""));
                                put("stockholder_investor", result.stream().filter(v1v -> StringUtils.isNotBlank(v1v.getName())).map(ParticipantEntity::getName).distinct().collect(Collectors.joining(",")));
                                put("id", vv.getId());
                            }});
                            Map<String, Object> dapAuditRecordsDto = new HashMap<String, Object>() {{
                                put("cleanUserNameMobile", "15027323661");
                                put("userNameMobile", "18014345570");
                                put("dataType", 1);
                                put("data", JSON.toJSONString(data));
                                put("type", "UPDATE");
                                put("userEnglishName", "sunchao");
                                put("cleanEnglishName", "liupf");
                                put("userName", "孙超");
                                put("database", "manage_enterprise_db");
                                put("cleanUserName", "刘鹏飞");
                                put("table", "comp_product_financing");
                                put("dapAuditId", 0);
                                put("ts", System.currentTimeMillis());
                            }};
                            //System.out.println(JSON.toJSONString(dapAuditRecordsDto));
                            KafkaHelper.javaKafkaProducer("dap_dap_manage_enterprise_db_comp_product_financing", JSON.toJSONString(dapAuditRecordsDto));
                        }

                        System.out.println(it.getId() + ":清洗补充企业投资方,修正融资日期和sourceType =21");

                        //如果不一致 要补充投资方  还有看看sourcetype 是否可修改  ==4

                    } else {
                        List<EpProductFinancingV2Sync> opList = epProductFinancingV2Syncs.stream().filter(vvv -> vvv.getProductId().equals(vv.getProductId())).collect(Collectors.toList());
                        Long date1 = DateUtil.getTimestampOfStringDate(it.getFinanceDateCurrent(), "yyyy-MM-dd");
                        Long date2 = DateUtil.getTimestampOfStringDate(it.getFinanceDateExcel(), "yyyy-MM-dd");
                        List<EpProductFinancingV2Sync> midList = new ArrayList<>();
                        if (date1 >= date2) {
                            midList.addAll(opList.stream().filter(kk -> !kk.getId().equals(it.getId()) && kk.getFinanceDate() <= date1 / 1000 && kk.getFinanceDate() >= date2 / 1000).collect(Collectors.toList()));
                        } else {
                            midList.addAll(opList.stream().filter(kk -> !kk.getId().equals(it.getId()) && kk.getFinanceDate() <= date2 / 1000 && kk.getFinanceDate() >= date1 / 1000).collect(Collectors.toList()));

                        }

                        if (midList.isEmpty()) {
                            System.out.println(it.getId() + ":清洗补充企业投资方");

                            if (StringUtils.isNotBlank(it.getParticipantDetails())) {
                                List<ParticipantEntity> result = JSON.parseArray(it.getParticipantDetails(), ParticipantEntity.class);
                                result.stream().filter(v1v -> StringUtils.isNotBlank(v1v.getName())).map(ParticipantEntity::getName).distinct().collect(Collectors.joining(","));

                                List<HashMap<String, Object>> data = new ArrayList<>();
                                data.add(new HashMap<String, Object>() {{
                                    put("stockholder_investor", result.stream().filter(v1v -> StringUtils.isNotBlank(v1v.getName())).map(ParticipantEntity::getName).distinct().collect(Collectors.joining(",")));
                                    put("id", vv.getId());
                                }});
                                Map<String, Object> dapAuditRecordsDto = new HashMap<String, Object>() {{
                                    put("cleanUserNameMobile", "15027323661");
                                    put("userNameMobile", "18014345570");
                                    put("dataType", 1);
                                    put("data", JSON.toJSONString(data));
                                    put("type", "UPDATE");
                                    put("userEnglishName", "sunchao");
                                    put("cleanEnglishName", "liupf");
                                    put("userName", "孙超");
                                    put("database", "manage_enterprise_db");
                                    put("cleanUserName", "刘鹏飞");
                                    put("table", "comp_product_financing");
                                    put("dapAuditId", 0);
                                    put("ts", System.currentTimeMillis());
                                }};
                                //System.out.println(JSON.toJSONString(dapAuditRecordsDto));
                                KafkaHelper.javaKafkaProducer("dap_dap_manage_enterprise_db_comp_product_financing", JSON.toJSONString(dapAuditRecordsDto));
                            }


                        } else {
                            System.out.println(it.getId() + ":采编核验补充，因为下一轮已存在事件");
                        }


                    }
                } else {
                    System.out.println("id 不存在");
                }
            });


        } catch (Exception e) {
            e.printStackTrace();
        }
    }


}
