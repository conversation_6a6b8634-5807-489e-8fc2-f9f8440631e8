package com.ld.clean.test.mainbrand.supplementfinances.step;

import com.ld.clean.constants.ProductConstants;
import com.ld.clean.kafka.CleanFlinkKafkaConsumer;
import com.ld.clean.kafka.KafkaHelper;
import com.ld.clean.step.MainParentStep;
import com.ld.clean.step.StepConstructorParam;
import org.apache.flink.streaming.api.datastream.DataStream;

/**
 * <AUTHOR>
 */
public class HandleRoundMainStep extends MainParentStep {

    private StepConstructorParam stepConstructorParam;

    public HandleRoundMainStep(StepConstructorParam stepConstructorParam) {
        this.stepConstructorParam = stepConstructorParam;
    }

    @Override
    protected DataStream<String> getSpiderSourceData() {
        String topic = "base_tmp_liupf";
        String group = "group_base_product_baseinfo_data_test";
        return stepConstructorParam.getEnv().addSource(
                KafkaHelper.buildAuthKafkaSource(CleanFlinkKafkaConsumer.builder()
                                .topic(topic)
                                .groupId(group).build())
                        .setCommitOffsetsOnCheckpoints(true)
        );

    }

    @Override
    protected DataStream<String> getDapSourceData() {
        return null;
    }

    @Override
    protected void spiderStep(DataStream<String> dataStream) {
        int spiderProcessParallelism = stepConstructorParam.getDmpParams().getInt(ProductConstants.SPIDER_PRODUCT_FINANCING_PROCESS_PARALLELISM,
                stepConstructorParam.getPropertiesParams().getInt(ProductConstants.SPIDER_PRODUCT_FINANCING_PROCESS_PARALLELISM));
        stepConstructorParam.getEnv().setParallelism(spiderProcessParallelism);
        int corePoolSize = stepConstructorParam.getDmpParams().getInt(ProductConstants.CORE_POOL_SIZE, ProductConstants.CORE_POOL_SIZE_DEFAULT);
        stepConstructorParam.setCorePoolSize(corePoolSize);
        new HandleRoundStep(stepConstructorParam).process(dataStream);
    }

    @Override
    protected void dapStep(DataStream<String> dataStream) {
    }
}
