
package com.ld.clean.test.financev2sync;

import com.ld.clean.context.ApplicationCoreContextManager;
import com.ld.clean.kafka.CleanFlinkKafkaConsumer;
import com.ld.clean.kafka.KafkaHelper;
import com.ld.clean.test.mainbrand.dao.mergerexit.ScSeIpoExitBaseinfo20250610Repository;
import com.ld.clean.utils.CheckPointUtil;
import com.ld.clean.utils.ExecutionEnvUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.api.java.utils.ParameterTool;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.flink.streaming.connectors.kafka.FlinkKafkaConsumer;


/**
 * 爬虫数据字段清洗
 *
 * <AUTHOR>
 */

@Slf4j
public class LocalV2FinancingApplication {
    private static ScSeIpoExitBaseinfo20250610Repository scSeIpoExitBaseinfo20250610Repository = ApplicationCoreContextManager.getInstance(ScSeIpoExitBaseinfo20250610Repository.class);


    public static void main(String[] args) throws Exception {
        ParameterTool parameterTool = ExecutionEnvUtil.PARAMETER_TOOL;
        StreamExecutionEnvironment env = CheckPointUtil.setCheckpointConfig(ExecutionEnvUtil.prepare(parameterTool), parameterTool);
        ParameterTool dmpParams = ParameterTool.fromArgs(args);
        int spiderProcessParallelism = 2;
        env.setParallelism(spiderProcessParallelism);
        env.disableOperatorChaining();

        String topic = "base_tmp_liupf";
        String group = "group_base_product_baseinfo_data_test";


        FlinkKafkaConsumer<String> consumer = KafkaHelper.buildAuthKafkaSource(CleanFlinkKafkaConsumer.builder()
                .topic(topic)
                .groupId(group).build());


        DataStream<String> originDataStream = env.addSource(consumer.setCommitOffsetsOnCheckpoints(true));
        //DataStream<EpProductFinancingV2Sync> baseinfoDataStream = originDataStream.flatMap(new TmpFinancingBrandFlatMap()).name("TmpBaseInfoFlatMap");

        /*AsyncCleanDataSteam.orderedWait(baseinfoDataStream, new TmpFinancingV2FlatMap(20)).name("TmpUpdateBaseFinanceInfoFunction");*/


        //DataStream<EpProductFinancingV2Sync> v2SyncDataStream = baseinfoDataStream.flatMap(new TmpFinancingV2FlatMap()).name("TmpBaseInfoFlatMap");


        /*DataStream<List<ScSeFinanceInvestMapping20240320>> listMapping = AsyncCleanDataSteam.orderedWait(baseinfoDataStream,new TmpCleanFinanceInvestMappingDetailFunction(2)).name("TmpBaseInfoFlatMap");

        DataStream<List<ScSeFinanceInvestMapping20240320>> insertList = AsyncCleanDataSteam.unorderedWait(
                listMapping, new TmpInsertFinanceInvestMappingFunction(2)).name("InsertFinanceInvestMappingFunction");*/
        //并购数据处理
        //DataStream<DapProductMergerEvents> baseinfoDataStream = originDataStream.flatMap(new TmpMergerFlatMap()).name("TmpBaseInfoFlatMap");
        //DataStream<SpecialCompanyNoProduct> baseinfoDataStream = originDataStream.flatMap(new TmpMergerFlatMap()).name("TmpBaseInfoFlatMap");

        /*DataStream<List<SpecialCompanyNoProduct>> list = WindowCleanDataStream.toDataStreamList(baseinfoDataStream, Time.seconds(5), 20);

        AsyncCleanDataSteam.orderedWait(list, new TmpUpdateDbFunction(20)).name("TmpUpdateBaseFinanceInfoFunction");*/


        /*SingleOutputStreamOperator<EpProductFinancingV2Sync> ep = AsyncCleanDataSteam.orderedWait(baseinfoDataStream, new TmpUpdateV2FinanceInfoFunction(10)).flatMap(new FlatMapFunction<List<EpProductFinancingV2Sync>, EpProductFinancingV2Sync>() {
            @Override
            public void flatMap(List<EpProductFinancingV2Sync> events, Collector<EpProductFinancingV2Sync> collector) throws Exception {
                events.forEach(collector::collect);
            }

        });*/


        //融资事件处理
        /*DataStream<List<String>> list = WindowCleanDataStream.toDataStreamList(originDataStream, Time.seconds(5), 100);
        DataStream<ScSeIpoExitBaseinfo20250610> baseinfoDataStream = list.flatMap(new TestCleanBaesFinanceFlatMap()).name("TmpBaseInfoFlatMap");

        DataStream<ScSeIpoExitBaseinfo20250610> listExit = AsyncCleanDataSteam.orderedWait(baseinfoDataStream,new TmpExitHoderOrgTypeFunction(10)).name("TmpBaseInfoFlatMap");
        //DataStream<List<ScSeIpoExitBaseinfo20250610>> listRe = WindowCleanDataStream.toDataStreamList(listExit, Time.seconds(5), 20);
        WindowCleanDataStream.toDataStreamList(listExit, Time.seconds(10L),100).flatMap(new FlatMapFunction<List<ScSeIpoExitBaseinfo20250610>, String>() {
            @Override
            public void flatMap(List<ScSeIpoExitBaseinfo20250610> ipoExitBaseinfos, Collector<String> collector) throws Exception {
                scSeIpoExitBaseinfo20250610Repository.insertBatch(ipoExitBaseinfos);
            }
        });*/


      /*  SingleOutputStreamOperator<EpProductFinancingV2Sync> ep = AsyncCleanDataSteam.orderedWait(baseinfoDataStream, new TestUpdateV2FinanceInfoFunction(10)).flatMap(new FlatMapFunction<List<EpProductFinancingV2Sync>, EpProductFinancingV2Sync>() {
            @Override
            public void flatMap(List<EpProductFinancingV2Sync> events, Collector<EpProductFinancingV2Sync> collector) throws Exception {
                events.forEach(collector::collect);
            }

        });*/
        //更新基础表
        //DataStream<EpProductFinancingV2Sync> financingV2SyncDataStream = AsyncCleanDataSteam.orderedWait(baseinfoDataStream, new TmpUpdateBaseFinanceInfoFunction(2)).name("TmpUpdateBaseFinanceInfoFunction");
       /* //更新mongo
        AsyncCleanDataSteam.orderedWait(productInfo,
                new TmpSyncBrandMongoFinanceInfoFunction(2)).name("TmpSyncBrandMongoFinanceInfoFunction");*/
        env.execute(ApplicationCoreContextManager.getAppId());
    }
}
