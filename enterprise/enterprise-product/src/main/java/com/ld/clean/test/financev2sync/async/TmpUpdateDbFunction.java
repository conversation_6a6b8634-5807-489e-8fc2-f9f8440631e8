package com.ld.clean.test.financev2sync.async;

import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Lists;
import com.ld.clean.context.ApplicationCoreContextManager;
import com.ld.clean.core.base.BaseModel;
import com.ld.clean.dao.model.basecleandatatmp.ScSeFinanceInvestMapping20240320;
import com.ld.clean.dao.repository.basecleandatatmp.ScSeFinanceInvestMapping20240320Repository;
import com.ld.clean.job.productv2.finance.entity.PersonInfoEntity;
import com.ld.clean.model.manageenterprisedb.FinanceInvestMapping;
import com.ld.clean.model.manageenterprisedb.SpecialCompanyNoProduct;
import com.ld.clean.parent.AsyncCleanParentFunction;
import com.ld.clean.repository.manageenterprisedb.SpecialCompanyNoProductRepository;
import com.ld.clean.utils.DateUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.configuration.Configuration;
import tk.mybatis.mapper.entity.Condition;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
public class TmpUpdateDbFunction extends AsyncCleanParentFunction<List<SpecialCompanyNoProduct>, SpecialCompanyNoProduct> {

    private static ScSeFinanceInvestMapping20240320Repository scSeFinanceInvestMapping20240320Repository;
    private static  SpecialCompanyNoProductRepository specialCompanyNoProductRepository ;

    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);
        scSeFinanceInvestMapping20240320Repository = ApplicationCoreContextManager.getInstance(ScSeFinanceInvestMapping20240320Repository.class);
        specialCompanyNoProductRepository = ApplicationCoreContextManager.getInstance(SpecialCompanyNoProductRepository.class);
    }

    public TmpUpdateDbFunction(int corePoolSize) {
        super(corePoolSize);
    }

    @Override
    protected SpecialCompanyNoProduct invoke(List<SpecialCompanyNoProduct> list) throws Exception {

        /*if (CollectionUtils.isNotEmpty(list)) {
            for(SpecialCompanyNoProduct product : list){
                String infos = product.getPersonInfos();
                //处理核心人员
                if(StringUtils.isNotBlank(infos) && !"[]".equals(infos)){
                    ObjectMapper objectMapper = new ObjectMapper();
                    List<PersonInfoEntity> entities = new ArrayList<>();
                    try {
                        JsonNode jsonNode = objectMapper.readTree(infos);
                        for(JsonNode node : jsonNode){
                            String zhiwu = node.get("zhiwu").asText();
                            if(StringUtils.isNotBlank(zhiwu)){
                                boolean containsKeyword = titles.stream()
                                        .anyMatch(zhiwu::contains);
                                if(containsKeyword){
                                    String icon = node.get("icon").asText();
                                    String name = node.get("name").asText();
                                    String jieshao = node.get("jieshao").asText();
                                    PersonInfoEntity entity = new PersonInfoEntity(icon,name, zhiwu  ,jieshao);
                                    entities.add(entity);
                                }
                            }
                        }
                        product.setPersonInfos(JSONObject.toJSONString(entities));
                    } catch (IOException e) {
                        e.printStackTrace();
                    }
                }
            }

            specialCompanyNoProductRepository.insertBatch(list);
        }*/

        return null;
    }
}
