/*
package com.ld.clean.beans;

import lombok.Data;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

*/
/**
 * <AUTHOR>
 * @Date: 2019/2/18 17:47
 * @Description: 省市区编码
 *//*

@Data
@Document(collection = "AreaV2")
public class MongoArea {
    @Field("_id")
    private Integer id;
    @Field("Province")
    private String province;
    @Field("City")
    private String city;
    @Field("Country")
    private String country;
}
*/
