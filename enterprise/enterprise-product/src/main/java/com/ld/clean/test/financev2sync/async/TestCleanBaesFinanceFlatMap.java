package com.ld.clean.test.financev2sync.async;

import com.ld.clean.context.ApplicationCoreContextManager;
import com.ld.clean.model.CompBrandFinancing;
import com.ld.clean.repository.CompBrandFinancingRepository;
import com.ld.clean.repository.manageenterprisedb.CompProductFinancingRepository;
import com.ld.clean.test.mainbrand.dao.ScSeCompBrandFinancing20240513;
import com.ld.clean.test.mainbrand.dao.ScSeCompBrandFinancing20240513Repository;
import org.apache.commons.collections.CollectionUtils;
import org.apache.flink.api.common.functions.RichFlatMapFunction;
import org.apache.flink.util.Collector;

import java.util.List;

/**
 *
 *
 * <AUTHOR>
 */
public class TestCleanBaesFinanceFlatMap extends RichFlatMapFunction<List<String>, CompBrandFinancing> {

    private static CompProductFinancingRepository compProductFinancingRepository = ApplicationCoreContextManager.getInstance(CompProductFinancingRepository.class);
    private static CompBrandFinancingRepository compBrandFinancingRepository = ApplicationCoreContextManager.getInstance(CompBrandFinancingRepository.class);

    @Override
    public void flatMap(List<String> ids, Collector<CompBrandFinancing> collector) throws Exception {
        if (CollectionUtils.isNotEmpty(ids)) {
            List<CompBrandFinancing> list = compBrandFinancingRepository.selectByPrimaryKeyList(ids);
            for (CompBrandFinancing financing : list) {
                collector.collect(financing);
            }
        }
    }
}