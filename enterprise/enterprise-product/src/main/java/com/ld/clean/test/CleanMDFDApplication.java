package com.ld.clean.test;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.base.clean.utils.BaseDateUtil;
import com.base.clean.utils.financial.FinancialApiUtil;
import com.ld.clean.context.ApplicationCoreContextManager;
import com.ld.clean.datasteam.AsyncCleanDataSteam;
import com.ld.clean.dto.company.IpoPromoterOut;
import com.ld.clean.dto.company.PromoterOut;
import com.ld.clean.dto.company.QccCompanyOut;
import com.ld.clean.enums.CleanStatusEnum;
import com.ld.clean.enums.DimensionEnum;
import com.ld.clean.job.product.monitorcompanychange.entity.NameKeyNoTagsEntity;
import com.ld.clean.job.productv2.lptype.process.CleanLpTypeKeynoFunction;
import com.ld.clean.job.productv2.lptype.process.MergeLpTypeKeynoFunction;
import com.ld.clean.job.productv2.lptype.sink.InsertLpTypeFunction;
import com.ld.clean.kafka.CleanFlinkKafkaConsumer;
import com.ld.clean.kafka.KafkaHelper;
import com.ld.clean.mapper.manageenterprisedb.CompVcpeInfoRepository;
import com.ld.clean.model.manageenterprisedb.CompBrandFinancingMid;
import com.ld.clean.model.manageenterprisedb.CompVcpeInfo;
import com.ld.clean.model.manageenterprisedb.CompZzLpTypeBaseinfo;
import com.ld.clean.model.manageenterprisedb.ProductBaseinfo;
import com.ld.clean.repository.manageenterprisedb.CompBrandFinancingMidRepository;
import com.ld.clean.repository.manageenterprisedb.ProductBaseinfoRepository;
import com.ld.clean.repository.tidbsearchsyncenterprise.EpProductFinancingV2SyncRepository;
import com.ld.clean.test.entity.ForMdFdEntity;
import com.ld.clean.util.StringUtil;
import com.ld.clean.util.ct.CtKeynoUtil;
import com.ld.clean.utils.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.api.common.functions.FlatMapFunction;
import org.apache.flink.api.java.utils.ParameterTool;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.flink.streaming.connectors.kafka.FlinkKafkaConsumer;
import org.apache.flink.util.Collector;
import tk.mybatis.mapper.entity.Condition;

import java.io.File;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 清洗外部补充md fd
 *
 * <AUTHOR>
 */
@Slf4j
public class CleanMDFDApplication {
    private static final ProductBaseinfoRepository productBaseinfoRepository =ApplicationCoreContextManager.getInstance(ProductBaseinfoRepository.class);
    private static final com.ld.clean.mapper.manageenterprisedb.CompVcpeInfoRepository compVcpeInfoRepository =ApplicationCoreContextManager.getInstance(CompVcpeInfoRepository.class);

    private static final CompBrandFinancingMidRepository compBrand = ApplicationCoreContextManager.getInstance(CompBrandFinancingMidRepository.class);
    public static void main(String[] args) throws Exception {
        List<CompBrandFinancingMid> midList = new ArrayList<>();
        //获取公司名
        List<String> compNames = Arrays.asList(
                "苏州思科赛德电子科技股份有限公司",
                "睿龙材料科技无锡股份有限公司",
                "马鞍山新康达磁业股份有限公司",
                "江苏昇昌家居新材料股份有限公司",
                "安徽曙光化工集团股份有限公司",
                "珩星电子（连云港）股份有限公司",
                "河北宇航热电股份有限公司",
                "山东海王星辰医药连锁集团股份有限公司",
                "山东永聚医药科技股份有限公司",
                "湖南恒兴新材料科技股份有限公司",
                "上海众力汽车系统集团股份有限公司",
                "广东热立方科技股份有限公司",
                "重庆富普新材料科技股份有限公司",
                "福建新峰科技股份有限公司",
                "浙江传化合成材料股份有限公司",
                "山东金昌树新材料股份有限公司",
                "宝利根（成都）精密工业股份有限公司",
                "创斯达科技（中国）集团股份公司",
                "冠佳技术股份有限公司",
                "浙江硕克科技股份有限公司",
                "株洲菲斯罗克光电科技股份有限公司",
                "珠海东方重工股份有限公司",
                "重庆海特能源股份有限公司",
                "上海碧云天生物技术股份有限公司",
                "马鞍山新康达磁业股份有限公司",
                "四川经准检验检测集团股份有限公司",
                "赛瓦特（山东）动力科技股份有限公司",
                "深圳市蓝鲸智联科技股份有限公司",
                "浙江永励精密制造股份有限公司",
                "浙江巨马文旅股份有限公司",
                "山东悦龙橡塑科技股份有限公司",
                "北京普凡防护科技股份有限公司",
                "安徽海创绿能环保集团股份有限公司",
                "江苏鸿舜工业科技股份有限公司"
                );
        List<String> keyNoList = new ArrayList<>();
        //获取公司内码
        compNames.forEach(itt->{
            String keyNoByName = FinancialApiUtil.getKeyNoByName(itt, "", "", null, true, "", DimensionEnum.PD_PRODUCT);
            keyNoList.add(keyNoByName);
        });
        //获取公司品牌
        //List<ProductBaseinfo> productBaseinfos = productBaseinfoRepository.selectByKeynos(keyNoList);


        List<String> fields = Arrays.asList("_id", "Name", "IpoPartners", "Partners","Product");

        //获取公司股东
        List<QccCompanyOut>  companyList = CompanyDetailsUtil.getCompanyList(keyNoList, false, fields)  ;

        List<String> allKeyNoList = new ArrayList<>();

        List<ForMdFdEntity> forMdFdEntityList = new ArrayList<>();
        //获取股东中的MDFD
        companyList.forEach(itt->{
            //brand_id
            itt.getProduct().getId();
            itt.getProduct().getName();
            List<PromoterOut> partners = itt.getPartners();
            for(PromoterOut part : partners){
                ForMdFdEntity forMdFdEntity = new ForMdFdEntity();
                tk.mybatis.mapper.entity.Condition condition = new Condition(CompVcpeInfo.class);
                // 加入keyNo 为空判断
                if(StringUtils.isNotBlank(part.getKeyNo())){
                    condition.createCriteria().andEqualTo("compKeyno", part.getKeyNo()).andEqualTo("dataStatus", CleanStatusEnum.VALID.getVal()).andIn("vcpeType",Arrays.asList("1,2".split(",")));
                    List<CompVcpeInfo> compVcpeInfo = compVcpeInfoRepository.selectByCondition(condition);
                    if(ObjectUtil.isNotEmpty(compVcpeInfo)){
                        CompVcpeInfo info = compVcpeInfo.get(0);
                        forMdFdEntity.setBrandName(itt.getProduct().getName());
                        forMdFdEntity.setBrandId(itt.getProduct().getId());
                        forMdFdEntity.setFinanceDate(getStringFormatTimeOfSecondTimestamp(part.getInDate(), DateUtil.YMD));
                        forMdFdEntity.setInvestorName(part.getStockName());
                        forMdFdEntity.setInvestorKeyNo(part.getKeyNo());
                        forMdFdEntityList.add(forMdFdEntity);
                    }
                }
            }
           /* if(ObjectUtil.isEmpty(itt.getIpoPartners())){


            }else {
                //优先获取ipoPartners
                List<IpoPromoterOut> partners = itt.getIpoPartners();
                for(IpoPromoterOut part : partners){
                    ForMdFdEntity forMdFdEntity = new ForMdFdEntity();
                    tk.mybatis.mapper.entity.Condition condition = new Condition(CompVcpeInfo.class);
                    condition.createCriteria().andEqualTo("compKeyno", part.getKeyNo()).andEqualTo("dataStatus", CleanStatusEnum.VALID.getVal()).andIn("vcpeType",Arrays.asList("1,2".split(",")));
                    List<CompVcpeInfo> compVcpeInfo = compVcpeInfoRepository.selectByCondition(condition);
                    if(ObjectUtil.isNotEmpty(compVcpeInfo)){
                        CompVcpeInfo info = compVcpeInfo.get(0);
                        forMdFdEntity.setBrandName(itt.getProduct().getName());
                        forMdFdEntity.setBrandId(itt.getProduct().getId());
                        forMdFdEntity.setFinanceDate(getStringFormatTimeOfSecondTimestamp(part.getEndDate(), DateUtil.YMD));
                        forMdFdEntity.setInvestorName(part.getInvestName());
                        forMdFdEntity.setInvestorKeyNo(part.getKeyNo());
                        forMdFdEntityList.add(forMdFdEntity);
                    }
                }
            }*/
        });
        System.out.println(forMdFdEntityList.size());



        Map<String, List<ForMdFdEntity>> groupedByBrandName = forMdFdEntityList.stream()
                .collect(Collectors.groupingBy(ForMdFdEntity::getBrandName));





        groupedByBrandName.forEach((brandName, entities) -> {



            Map<String, List<ForMdFdEntity>> groupedByDate = entities.stream()
                    .collect(Collectors.groupingBy(ForMdFdEntity::getFinanceDate));
            System.out.println(groupedByDate);


            groupedByDate.forEach((date, entitiesWithSameDate) -> {
                Set<String> seenKeys = new HashSet<>();
                List<NameKeyNoTagsEntity> mergedEntities = new ArrayList<>();
                CompBrandFinancingMid compBrandFinancingMid = new CompBrandFinancingMid();
                for (ForMdFdEntity entity : entitiesWithSameDate) {
                    String key = entity.getInvestorKeyNo() + entity.getInvestorName();
                    if (!seenKeys.contains(key)) {
                        NameKeyNoTagsEntity newEntity = new NameKeyNoTagsEntity( entity.getInvestorName(),entity.getInvestorKeyNo());
                        //mergedEntities.add(newEntity);
                        mergedEntities.add(newEntity);
                    }
                }

                compBrandFinancingMid.setInvestorDetails(JSON.toJSONString(mergedEntities));
                compBrandFinancingMid.setFinanceDate(date);
                compBrandFinancingMid.setAmount("未披露");
                compBrandFinancingMid.setRound("股权融资");
                compBrandFinancingMid.setBrandId(entitiesWithSameDate.get(0).getBrandId());
                compBrandFinancingMid.setBrandName(entitiesWithSameDate.get(0).getBrandName());
                compBrandFinancingMid.setId(MD5Util.encode(compBrandFinancingMid.getBrandId() + "_" + compBrandFinancingMid.getRound() + "_" +
                        compBrandFinancingMid.getFinanceDate()));
                midList.add(compBrandFinancingMid);
            });
        });


        System.out.println(midList.size());
        compBrand.insertBatch(midList);

        /*List<Entity> result = new ArrayList<>();
        groupedByBrandName.forEach((brandName, entities) -> {
            Map<String, List<ForMdFdEntity>> groupedByFinanceDate = entities.stream()
                    .collect(Collectors.groupingBy(ForMdFdEntity::getFinanceDate));

            groupedByFinanceDate.forEach((financeDate, entitiesWithSameDate) -> {
                List<Entity> entitiesList = entitiesWithSameDate.stream()
                        .map(entity -> new Entity(entity.getInvestorName(), entity.getInvestorKeyNo()))
                        .collect(Collectors.toList());
                result.addAll(entitiesList);
            });
        });*/





    }


    public static String getStringFormatTimeOfSecondTimestamp(Long timStamp, String pattern) {
        String stringFormatTime = "";
        try {
            if (timStamp != null && StringUtils.isNotBlank(pattern)) {
                stringFormatTime = DateUtil.getStringFormatTimeOfTimestamp(timStamp * 1000, pattern);
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        return stringFormatTime;
    }
}
