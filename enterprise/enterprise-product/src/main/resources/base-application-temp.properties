# ç»´åº¦ä¸­æå
clean.app.name=æ°ç»æ¬æ¡ä»¶
# ç»´åº¦è±æåï¼ä»pom.xmlä¸­èªå¨è·å
clean.app.id=@project.artifactId@

# ç¬è«topic å group_id
spider.kafka.group.id=base_temp_spider_risk_end_execute_case_prod
spider.metrics.topic=base_temp_spider_risk_db_end_execute_case

# å®¡è®¡å¹³å°topic å group_id
dap.kafka.group.id=end_execute_case_dap_prod
# å®¡è®¡å¹³å°è¿æªæ´æ¹ææ¶ä½¿ç¨ä¹ååå»ºçéå
dap.metrics.topic=dap_dap_manage_risk_db_end_execute_case_spider

dap.clean.kafka.group.id=dap_dap_clean_manage_risk_db_end_execute_case_spider_prod
dap.clean.metrics.topic=base_temp_dap_clean_risk_db_end_execute_case

# æ¸æ´ä¸å¡é»è¾topic å group_id
clean.kafka.group.id=base_temp_spider_clean_risk_end_execute_case_prod
clean.metrics.topic=base_temp_spider_clean_risk_db_end_execute_case

# stream
stream.parallelism=20
# 5åéè§¦åä¸æ¬¡
stream.checkpoint.interval=300000
stream.checkpoint.enable=true
stream.checkpoint.type=fs
# checkpoint ä¿å­æ¶é´é»è®¤ï¼20åé
stream.checkpoint.timeout=1200000