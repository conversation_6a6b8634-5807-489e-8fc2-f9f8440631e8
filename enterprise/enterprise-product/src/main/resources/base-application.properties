# ç»´åº¦ä¸­æå
clean.app.name=äº§å
# ç»´åº¦è±æåï¼ä»pom.xmlä¸­èªå¨è·å
clean.app.id=@project.artifactId@
# topic å group_id
## å¬å¸åºæ¬ä¿¡æ¯topicä¸groupid
spider.enterprise.product.financing.topic=dap_spider_enterprise_product_financing
spider.enterprise.product.financing.groupid=group_dap_product_financing

## å¬å¸åºæ¬ä¿¡æ¯topicä¸groupid
clean.product.service.topic=dap_enterprise_product_service
clean.product.service.group=group_dap_product_service2

stream.parallelism=4

# å¹¶ååº¦è®¾ç½®
spider.process.parallelism=4
clean.process.parallelism=4
# 2åéè§¦åä¸æ¬¡
stream.checkpoint.interval=120000
stream.checkpoint.enable=true
stream.checkpoint.type=fs
# checkpoint ä¿å­æ¶é´é»è®¤ï¼20åé
stream.checkpoint.timeout=1200000