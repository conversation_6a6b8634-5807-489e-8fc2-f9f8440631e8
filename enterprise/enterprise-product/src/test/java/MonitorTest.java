import com.ld.clean.context.ApplicationCoreContextManager;
import com.ld.clean.model.tidbsearchsyncenterprise.EpInvestFinanceDynamicMonitor;
import com.ld.clean.repository.manageenterprisedb.CompInvestProductMonitorMsgRepository;
import com.ld.clean.repository.tidbsearchsyncenterprise.EpInvestFinanceDynamicMonitorRepository;
import com.ld.clean.utils.qcc.QccUnirest;
import junit.framework.TestCase;
import kong.unirest.HttpResponse;
import kong.unirest.RequestBodyEntity;

public class MonitorTest extends TestCase {
    public static final String SS_MONITOR = "https://oapi.dingtalk.com/robot/send?access_token=8a18f5e4c2ca580ed7a67114cdbf03a14e5b7c441c84440b439ee95eba239dd8";

    private static final EpInvestFinanceDynamicMonitorRepository epInvestFinanceDynamicMonitorRepository = ApplicationCoreContextManager.getInstance(EpInvestFinanceDynamicMonitorRepository.class);
    private static final CompInvestProductMonitorMsgRepository compInvestProductMonitorMsgRepository = ApplicationCoreContextManager.getInstance(CompInvestProductMonitorMsgRepository.class);
    private static final String MONITOR_MSG_URL_TEST = "http://nodejs-qcc-business-extend-api.sit.office.qichacha.com/invest/monitor/pushMsg";


    public void testPost() {
        EpInvestFinanceDynamicMonitor epInvestFinanceDynamicMonitor = (EpInvestFinanceDynamicMonitor) epInvestFinanceDynamicMonitorRepository.selectByPrimaryKey("5f46d0b3132ccb6e032819a197c8599b");
        RequestBodyEntity body = QccUnirest.post(MONITOR_MSG_URL_TEST)
                .header("Content-Type", "application/json; charset=UTF-8")
                .body(epInvestFinanceDynamicMonitor);
        HttpResponse<String> httpResponse = body.asString();
        String responseBody = httpResponse.getBody();
        System.out.println(responseBody);
    }

}
