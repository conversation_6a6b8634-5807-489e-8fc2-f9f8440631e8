import com.google.common.collect.Lists;
import com.ld.clean.context.ApplicationCoreContextManager;
import com.ld.clean.model.manageenterprisedb.CompTrackInfoCompetitionMapping;
import com.ld.clean.repository.manageenterprisedb.CompTrackInfoCompetitionMappingRepository;
import com.ld.clean.utils.MD5Util;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.xssf.usermodel.XSSFCell;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.junit.Test;

import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/11/25
 */
public class ImportTrackInfoCompetitionMapping {
    private static final CompTrackInfoCompetitionMappingRepository compTrackInfoCompetitionMappingRepository = ApplicationCoreContextManager.getInstance(CompTrackInfoCompetitionMappingRepository.class);

    @Test
    public void removeAllData() throws IOException {
        String path = "C:\\Users\\<USER>\\Desktop\\ids.txt";
        List<String> list = FileUtils.readLines(new File(path));
        List<List<String>> lists= Lists.partition(list,100);
        for (List<String> strings : lists) {
            compTrackInfoCompetitionMappingRepository.deleteByPrimaryKeyList(strings);
        }
    }
    @Test
    public void importFromExcel() throws IOException {
        String path = "C:\\Users\\<USER>\\Desktop\\qk和qmp源码表映射关系全量-1125-王琳.xlsx";
        XSSFWorkbook book = new XSSFWorkbook(path);
        XSSFSheet xssfSheet=book.getSheet("全量关系码表");
        int length =xssfSheet.getLastRowNum();
        List<CompTrackInfoCompetitionMapping> compTrackInfoCompetitionMappings=new ArrayList<>();
        for (int i = 1; i <=length; i++) {
            XSSFRow xssfRow = xssfSheet.getRow(i);
            XSSFCell xssfCell0 = xssfRow.getCell(0);
            XSSFCell xssfCell1 = xssfRow.getCell(1);
            XSSFCell xssfCell2 = xssfRow.getCell(2);
            XSSFCell xssfCell3 = xssfRow.getCell(3);
            XSSFCell xssfCell4 = xssfRow.getCell(4);
            XSSFCell xssfCell5 = xssfRow.getCell(5);
            XSSFCell xssfCell6 = xssfRow.getCell(6);
            XSSFCell xssfCell7 = xssfRow.getCell(7);
            XSSFCell xssfCell8 = xssfRow.getCell(8);
            XSSFCell xssfCell9 = xssfRow.getCell(9);

            String tackName = xssfCell0.getStringCellValue();
            String trackCode = xssfCell1.getStringCellValue();
//            xssfCell2.setCellType(XSSFCell.CELL_TYPE_STRING);
            String levelName1 = xssfCell2.getStringCellValue();
            String levelCode1 = xssfCell3.getStringCellValue();
            String levelName2 = null!=xssfCell4?xssfCell4.getStringCellValue():"";
            String levelCode2 = null!=xssfCell5?xssfCell5.getStringCellValue():"";
            String levelName3 = null!=xssfCell6?xssfCell6.getStringCellValue():"";
            String levelCode3 = null!=xssfCell7?xssfCell7.getStringCellValue():"";
            String searchWord = null!=xssfCell8?xssfCell8.getStringCellValue():"";
            String standWord = null!=xssfCell9?xssfCell9.getStringCellValue():"";

            searchWord=searchWord.replace("，",",");
            int sourceType=0;
            if(levelCode1.startsWith("qmp")){
                sourceType=1;
            }
            if(levelCode1.startsWith("qk")){
                sourceType=2;
            }
            if(StringUtils.isNotBlank(trackCode+levelCode1+levelCode2+levelCode3)&&sourceType>0){
                CompTrackInfoCompetitionMapping mapping=new CompTrackInfoCompetitionMapping();
                mapping.setDataStatus(1);
                mapping.setTrackName(StringUtils.trimToEmpty(tackName));
                mapping.setTrackCode(StringUtils.trimToEmpty(trackCode));
                mapping.setCompetitionTrackName(StringUtils.trimToEmpty(levelName1));
                mapping.setCompetitionTrackCode(StringUtils.trimToEmpty(levelCode1));
                mapping.setCompetitionSubTrackName(levelName2);
                mapping.setCompetitionSubTrackCode(levelCode2);
                mapping.setCompetitionThirdTrackName(levelName3);
                mapping.setCompetitionThirdTrackCode(levelCode3);
                mapping.setKeywords(searchWord);
                mapping.setStandardWords(standWord);
                mapping.setSourceType(sourceType);
                String idStr=mapping.getTrackCode()+mapping.getCompetitionTrackCode()+mapping.getCompetitionSubTrackCode()+mapping.getCompetitionThirdTrackCode()+mapping.getSourceType();
                mapping.setId(MD5Util.encode(idStr));
                compTrackInfoCompetitionMappings.add(mapping);
            }

        }

        System.out.println("导入条数:"+compTrackInfoCompetitionMappings.size());

        List<List<CompTrackInfoCompetitionMapping>> partitions=Lists.partition(compTrackInfoCompetitionMappings,100);
        for (List<CompTrackInfoCompetitionMapping> partition : partitions) {
            compTrackInfoCompetitionMappingRepository.insertBatch(partition);
        }
    }
}
