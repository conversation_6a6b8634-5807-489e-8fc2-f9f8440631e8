package ProductFinancingv2sync;

import com.google.common.collect.Lists;
import com.ld.clean.constants.ExchangeForWebEnums;
import com.ld.clean.context.ApplicationCoreContextManager;
import com.ld.clean.model.searchsyncenterprise.EpProductBaseinfoSync;
import com.ld.clean.model.tidbsearchsyncenterprise.EpProductFinancingV2Sync;
import com.ld.clean.repository.searchsyncenterprise.EpProductBaseinfoSyncRepository;
import com.ld.clean.repository.tidbsearchsyncenterprise.EpProductFinancingV2SyncRepository;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.junit.Test;

import java.io.File;
import java.io.IOException;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/3/28
 */
public class QccIndustryTest {
    private static final EpProductFinancingV2SyncRepository epProductFinancingV2SyncRepository = ApplicationCoreContextManager.getInstance(EpProductFinancingV2SyncRepository.class);
    private static final EpProductBaseinfoSyncRepository epProductBaseinfoSyncRepository = ApplicationCoreContextManager.getInstance(EpProductBaseinfoSyncRepository.class);

    @Test
    public void updateQccIndustry() throws IOException {
        String path = "C:\\Users\\<USER>\\Desktop\\ids.txt";
        List<String> list = FileUtils.readLines(new File(path));
        for (String productId : list) {
            System.out.println(productId);
            EpProductBaseinfoSync productBaseinfoSync = (EpProductBaseinfoSync) epProductBaseinfoSyncRepository.selectByPrimaryKey(productId);
            if(null!=productBaseinfoSync){
                List<EpProductFinancingV2Sync>  epProductFinancingV2Syncs= epProductFinancingV2SyncRepository.getListByProductId(productId);
                if(CollectionUtils.isNotEmpty(epProductFinancingV2Syncs)){
                    for (EpProductFinancingV2Sync ep : epProductFinancingV2Syncs) {
                        //项目所属企业金融标签
                        ep.setCompFinancialTags(productBaseinfoSync.getCompFinancialTags());
                        //企查查一级行业
                        ep.setQccIndustryFirst(productBaseinfoSync.getQccIndustryFirst());
                        //企查查二级至四级行业
                        ep.setQccIndustrySecond(productBaseinfoSync.getQccIndustrySecond());

                        if(StringUtils.isNotBlank(ep.getStockExchange())){
                            if(StringUtils.isBlank(ep.getShortStockExchange())){
                                ep.setListSectionCode(ExchangeForWebEnums.OTHER.getNumberCode());
                            }else{
                                ExchangeForWebEnums exchangeForWebEnums=ExchangeForWebEnums.getByName(ep.getShortStockExchange());
                                if(null!=exchangeForWebEnums){
                                    ep.setListSectionCode(exchangeForWebEnums.getNumberCode());
                                }
                            }
                        }else{
                            ep.setListSectionCode("");
                        }
                    }
                    epProductFinancingV2SyncRepository.insertBatch(epProductFinancingV2Syncs);
                }
            }
        }
    }

    @Test
    public void updateQccListSectionCode() throws IOException {
        String path = "C:\\Users\\<USER>\\Desktop\\ids.txt";
        List<String> list = FileUtils.readLines(new File(path));
        List<List<String>> lists= Lists.partition(list,100);
        for (List<String> ids : lists) {
            List<EpProductFinancingV2Sync>  epProductFinancingV2Syncs= epProductFinancingV2SyncRepository.selectByPrimaryKeyList(ids);
            if(CollectionUtils.isNotEmpty(epProductFinancingV2Syncs)){
                for (EpProductFinancingV2Sync ep : epProductFinancingV2Syncs) {
                    if(StringUtils.isNotBlank(ep.getStockExchange())){
                        if(StringUtils.isBlank(ep.getShortStockExchange())){
                            ep.setListSectionCode(ExchangeForWebEnums.OTHER.getNumberCode());
                        }else{
                            ExchangeForWebEnums exchangeForWebEnums=ExchangeForWebEnums.getByName(ep.getShortStockExchange());
                            if(null!=exchangeForWebEnums){
                                ep.setListSectionCode(exchangeForWebEnums.getNumberCode());
                            }
                        }
                    }else{
                        ep.setListSectionCode("");
                    }
                }
                epProductFinancingV2SyncRepository.insertBatch(epProductFinancingV2Syncs);
            }
        }
    }
}
