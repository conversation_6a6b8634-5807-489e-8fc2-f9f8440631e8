package ProductFinancingv2sync;

import cn.hutool.core.bean.BeanUtil;
import com.ld.clean.context.ApplicationCoreContextManager;
import com.ld.clean.model.CompBrandFinancing;
import com.ld.clean.model.tidbsearchsyncenterprise.EpProductFinancingV2Sync;
import com.ld.clean.repository.CompBrandFinancingRepository;
import com.ld.clean.repository.tidbsearchsyncenterprise.EpProductFinancingV2SyncRepository;
import com.ld.clean.test.mainbrand.dao.finance.LpfSeEpProductFinancingV2Sync241011;
import com.ld.clean.test.mainbrand.dao.finance.LpfSeEpProductFinancingV2Sync241011Repository;
import com.ld.clean.util.FinanceRoundUtil;
import org.apache.commons.io.FileUtils;
import org.junit.Test;

import java.io.File;
import java.io.IOException;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/3/6
 */
public class StockCodeTest {
    private static final EpProductFinancingV2SyncRepository epProductFinancingV2SyncRepository = ApplicationCoreContextManager.getInstance(EpProductFinancingV2SyncRepository.class);
    private static final CompBrandFinancingRepository compBrandFinancingRepository = ApplicationCoreContextManager.getInstance(CompBrandFinancingRepository.class);
//    private static final LpfSeEpProductFinancingV2Sync241011Repository testProductFinancingV2SyncRepository = ApplicationCoreContextManager.getInstance(LpfSeEpProductFinancingV2Sync241011Repository.class);

    @Test
    public void updateStockCode() throws IOException {
        String path = "C:\\Users\\<USER>\\Desktop\\ids.txt";
        List<String> list = FileUtils.readLines(new File(path));
        for (String id : list) {
            CompBrandFinancing compBrandFinancing=(CompBrandFinancing)compBrandFinancingRepository.selectByPrimaryKey(id);
            EpProductFinancingV2Sync epProductFinancingV2Sync=epProductFinancingV2SyncRepository.selectByPrimaryKey(id);
            epProductFinancingV2Sync.setStockCode(FinanceRoundUtil.getStockCode(compBrandFinancing.getRound()));
            epProductFinancingV2SyncRepository.insertBatch(Arrays.asList(epProductFinancingV2Sync));
//            if(null!=epProductFinancingV2Sync&&null!=compBrandFinancing){
//                System.out.println(id);
//                LpfSeEpProductFinancingV2Sync241011 testProductFinancingV2Sync= BeanUtil.toBean(epProductFinancingV2Sync,LpfSeEpProductFinancingV2Sync241011.class);
//                testProductFinancingV2Sync.setStockCode(FinanceRoundUtil.getStockCode(compBrandFinancing.getRound()));
//                testProductFinancingV2SyncRepository.insertBatch(Arrays.asList(testProductFinancingV2Sync));
//            }

        }
    }
}
