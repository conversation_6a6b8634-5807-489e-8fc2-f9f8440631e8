package com.ld.clean.dim.greenfinancial.notice;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.ld.clean.context.ApplicationCoreContextManager;
import com.ld.clean.kafka.KafkaHelper;
import com.ld.clean.model.manageenterprisedb.CompGreenFinancialExtractRecord;
import com.ld.clean.model.manageenterprisedb.CompGreenFinancialNoticeInfo;
import com.ld.clean.mongo.BaseMongoEnum;
import com.ld.clean.repository.manageenterprisedb.CompGreenFinancialNoticeInfoRepository;
import com.ld.clean.utils.MD5Util;
import com.ld.clean.utils.MongodbUtil;
import org.apache.commons.io.FileUtils;
import org.junit.Test;
import org.springframework.beans.BeanUtils;

import java.io.File;
import java.io.IOException;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date ：2025/4/28  11:53
 * @description ：
 */
public class SendKafkaTest {

    private static final CompGreenFinancialNoticeInfoRepository REPOSITORY = ApplicationCoreContextManager.getInstance(CompGreenFinancialNoticeInfoRepository.class);

    @Test
    public void send() throws IOException {
        List<String> ids = FileUtils.readLines(new File("c:\\work\\id.txt"), "utf-8");
        List<CompGreenFinancialNoticeInfo> compGreenFinancialNoticeInfos = REPOSITORY.selectByPrimaryKeyList(ids);
        compGreenFinancialNoticeInfos.forEach(iter -> {
            JSONObject jsonObject = new JSONObject();
            com.ld.clean.dim.greenfinancial.pojo.so.batch3.CompGreenFinancialNoticeInfo noticeInfo = new com.ld.clean.dim.greenfinancial.pojo.so.batch3.CompGreenFinancialNoticeInfo();
            BeanUtils.copyProperties(iter, noticeInfo);
            noticeInfo.setStepType(1);
            jsonObject.put("comp_green_financial_notice_info", Collections.singletonList(noticeInfo));

            KafkaHelper.javaKafkaProducer("base_green_comp_notice_send_nlp", jsonObject.toJSONString());
        });

    }
}
