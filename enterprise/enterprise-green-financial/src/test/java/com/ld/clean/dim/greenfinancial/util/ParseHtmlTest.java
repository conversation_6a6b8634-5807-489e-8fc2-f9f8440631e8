package com.ld.clean.dim.greenfinancial.util;

import com.alibaba.fastjson.JSON;
import com.base.clean.utils.BaseCephUtil;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.util.TimeUtils;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;
import org.junit.Test;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.net.URL;
import java.nio.file.Files;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date ：2024/10/22  17:09
 * @description ：
 */
public class ParseHtmlTest {

    @Test
    public void parseHtml() throws IOException {
        InputStream inputStream = BaseCephUtil.getCleanObject("qcc-data/greenFinancial", "1a12b1dcff48677a17268f1a6881dfab.xls");
        String filePath = System.getProperty("user.dir") + File.separator + "tmp" + File.separator + "1a12b1dcff48677a17268f1a6881dfab.xls";
        Document document = Jsoup.parse(inputStream, "utf-8", filePath);
        Elements elements = document.select("Row");
        Integer wide = elements.get(0).select("Cell").stream().map(iter -> {
            int attr1 = iter.attr("ss:MergeAcross").isEmpty() ? 1 : Integer.valueOf(iter.attr("ss:MergeAcross"));
            return attr1;
        }).reduce((x, y) -> x + y).orElse(0) + 1;
        Integer high = elements.size();
        String[][] result = new String[high][wide];

        for (int x = 0; x < elements.size(); x++) {
            Elements cell = elements.get(x).select("Cell");
            for (int y = 0; y < cell.size(); y++) {
                if(StringUtils.isNotEmpty(result[x][y])){
                    continue;
                }
                Element line = cell.get(y);
                int attr1 = line.attr("ss:MergeAcross").isEmpty()?1:Integer.valueOf(line.attr("ss:MergeAcross"))+1;
                int attr2 = line.attr("ss:MergeDown").isEmpty()?1:Integer.valueOf(line.attr("ss:MergeDown"))+1;
                for (int i = 0; i < attr1; i++) {
                    result[x][y+i] = line.text();
                }

                for (int i = 0; i < attr2; i++) {
                    result[x+i][y] = line.text();
                }
            }
        }

        System.out.println(JSON.toJSONString(result));
    }
}
