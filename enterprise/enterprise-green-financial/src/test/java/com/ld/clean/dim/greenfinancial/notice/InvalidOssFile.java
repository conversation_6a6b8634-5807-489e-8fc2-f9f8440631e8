package com.ld.clean.dim.greenfinancial.notice;

import com.qcc.clean.util.QccOssUtil;
import org.apache.commons.io.FileUtils;
import org.junit.Test;

import java.io.File;
import java.io.IOException;
import java.util.List;

/**
 * <AUTHOR>
 * @date ：2025/5/14  14:36
 * @description ：
 */
public class InvalidOssFile {

    @Test
    public void deleteFile() throws IOException {
        List<String> strings = FileUtils.readLines(new File("c:\\work\\id.txt"), "utf-8");

        strings.forEach(iter -> {
//            boolean b = BaseCephUtil.doesObjectExist(iter, "base-data-file", 3);
//            System.out.println(b);
//
//            QccCephUtil.getInstance().deleteObject("base-data-file", iter);
            iter = iter.replace("/qcc-data/", "");
            boolean b1 = QccOssUtil.getSingleton().doesObjectExist(iter, "qcc-data");
            System.out.println(b1);
            if(b1) {
                QccOssUtil.getSingleton().deleteObject("qcc-data", iter);
            }
        });

    }
}
