package com.ld.clean.dim.greenfinancial.util;


import com.ld.clean.util.ReadMergeCellExcelUtil;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import java.io.*;
import java.util.Iterator;
import org.junit.Test;

import java.io.FileInputStream;
import java.io.IOException;
import java.util.Iterator;

/**
 * <AUTHOR>
 * @date ：2024/9/18  16:21
 * @description ：
 */

public class StructuralTest {

    @Test
    public void testAtta() throws IOException {
        ReadMergeCellExcelUtil.readExcelToObj(new File("C:\\Users\\<USER>\\Downloads\\e4a382fb9b488f7c2f4261bb68f5e138.xlsx"));
    }
}
