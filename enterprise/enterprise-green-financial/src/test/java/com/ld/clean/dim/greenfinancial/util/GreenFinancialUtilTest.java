package com.ld.clean.dim.greenfinancial.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.ld.clean.context.ApplicationCoreContextManager;
import com.ld.clean.core.dto.company.Company;
import com.ld.clean.dim.greenfinancial.pojo.so.batch3.IprightCertificationListInfoCnca;
import com.ld.clean.dim.greenfinancial.pojo.so.batch4.lsxm.TFinanceQinglvEnvironmentEnvironmentalAccepted;
import com.ld.clean.jdbc.DynamicDatasource;
import com.ld.clean.jdbc.QccJdbcTemplate;
import com.ld.clean.kafka.KafkaHelper;
import com.ld.clean.model.manageenterprisedb.CompGreenFinancialListInfo;
import com.ld.clean.model.searchsyncenterprise.CompGreenFinancialListInfoSync;
import com.ld.clean.repository.manageenterprisedb.CompGreenFinancialListInfoRepository;
import com.ld.clean.repository.tidbsearchsyncenterprise.CompGreenFinancialListInfoSyncRepository;
import com.ld.clean.util.GreenFinancialUtil;
import junit.framework.TestCase;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.jdbc.core.BeanPropertyRowMapper;

import java.io.File;
import java.io.IOException;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

public class GreenFinancialUtilTest extends TestCase {

    public void testGetBatchFromStr() {
        String value = "9千9拾";
        System.out.println(GreenFinancialUtil.getBatchFromStr(value));

    }

    public void testCodeUnion() {
        String va = "001005001004";
        System.out.println("GreenFinancialUtil.getCodeUnion(va) = " + GreenFinancialUtil.getCodeUnion(va));

    }

    public void testCompName(){
        TFinanceQinglvEnvironmentEnvironmentalAccepted t = new TFinanceQinglvEnvironmentEnvironmentalAccepted();
        String compName = "洛吉环审〔2017〕14号";
        System.out.println(GreenFinancialUtil.cleanLsxmIssuingAuthority(compName));
//        System.out.println(compName.matches(".*[\\d]{4}年[\\d]{2}月[\\d]{2}.*$"));

    }


    public void testToKafka(){
        String sql = "select a.* from manage_ipright_db.ipright_certification_list_info_cnca a\n" +
                "join manage_enterprise_db.comp_green_financial_list_info b\n" +
                "on a.id = b.source_id\n" +
                "join\n" +
                "(\n" +
                "select main_id,count(0) as cnt from manage_enterprise_db.comp_green_financial_detail_info \n" +
                "where data_status = 1 and source_table in ('manage_ipright_db.ipright_certification_detail_info_cnca')\n" +
                "group by main_id having count(0)<>51 \n" +
                ")c\n" +
                "on b.id = c.main_id";
        QccJdbcTemplate manage_industry_db = DynamicDatasource.getSingleton().getJdbcTemplate("manage_ipright_db");

        BeanPropertyRowMapper<IprightCertificationListInfoCnca> mapper = new BeanPropertyRowMapper<>(IprightCertificationListInfoCnca.class);
        List<IprightCertificationListInfoCnca> query = manage_industry_db.query(sql, mapper);
        AtomicInteger num = new AtomicInteger();
        query.forEach(x->{
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("ipright_certification_list_info_cnca", Collections.singletonList(x));
            num.getAndIncrement();
            KafkaHelper.javaKafkaProducer("dap_spider_financial_qinglv", JSON.toJSONString(jsonObject));
        });
        System.out.println(num.get());
    }

    public void testToKafka1(){
        String sql = "select a.* from manage_ipright_db.ipright_certification_list_info a\n" +
                " join (\n" +
                " select distinct source_id\n" +
                " from manage_enterprise_db.comp_green_financial_list_info\n" +
                " where comp_category_code = '001009002' and source_table = 'manage_ipright_db.ipright_certification_list_info' " +
                " and qualification_type like '%,%' and data_status = 1) b" +
                " on a.id = b.source_id";
        QccJdbcTemplate manage_industry_db = DynamicDatasource.getSingleton().getJdbcTemplate("manage_ipright_db");

        BeanPropertyRowMapper<IprightCertificationListInfoCnca> mapper = new BeanPropertyRowMapper<>(IprightCertificationListInfoCnca.class);
        List<IprightCertificationListInfoCnca> query = manage_industry_db.query(sql, mapper);
        AtomicInteger num = new AtomicInteger();
        query.forEach(x->{
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("ipright_certification_list_info", Collections.singletonList(x));
            num.getAndIncrement();
            KafkaHelper.javaKafkaProducer("dap_spider_financial_qinglv", JSON.toJSONString(jsonObject));
        });
        System.out.println(num.get());
    }

    public void testCompName1(){
        List<String> ids = Arrays.asList("61272b04ee6446ce51af527e30c91515");


        CompGreenFinancialListInfoSyncRepository repository = ApplicationCoreContextManager.getInstance(CompGreenFinancialListInfoSyncRepository.class);
        List<CompGreenFinancialListInfoSync> select = repository.selectByPrimaryKeyList(ids);
        select = select.stream().peek(x -> x.setDataStatus(3)).collect(Collectors.toList());

        repository.insertBatch(select);

    }

    public void testName() throws IOException {
        List<String> lines = FileUtils.readLines(new File("c:\\work\\id.txt"), "utf-8");
        lines.forEach(iter -> {
            String regex = "^(扶贫第|第)[一二三四五六七八九十两仨零壹贰叁肆伍陆柒捌玖拾]+批";
            Pattern pattern = Pattern.compile(regex);
            Matcher matcher = pattern.matcher(iter);
            System.out.println(iter + "---" + matcher.find());
        });
    }
}