package com.ld.clean.dim.greenfinancial.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.base.clean.utils.BaseCephUtil;
import com.base.clean.utils.BaseStringUtil;
import com.ld.clean.context.ApplicationCoreContextManager;
import com.ld.clean.kafka.KafkaHelper;
import com.ld.clean.model.manageenterprisedb.CompGreenFinancialNoticeInfo;
import com.ld.clean.model.manageenterprisedb.CompGreenFinancialNoticeMid;
import com.ld.clean.repository.manageenterprisedb.CompGreenFinancialNoticeInfoRepository;
import com.ld.clean.repository.manageenterprisedb.CompGreenFinancialNoticeMidRepository;
import com.ld.clean.util.ObjectUtil;
import com.ld.clean.util.PdfContentNlpUtil;
import com.ld.clean.utils.DateUtil;
import org.apache.commons.io.FileUtils;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.select.Elements;
import org.junit.Test;

import java.io.*;
import java.nio.charset.StandardCharsets;
import java.sql.Timestamp;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date ：2024/10/30  16:41
 * @description ：
 */
public class NoticeTest {

    @Test
    public void testContent() {
        String label = "5.5压缩机:一般用喷油螺杆空气压缩机".replaceFirst("^[\\d+.|\\d+\\.\\d+]*", "");
        System.out.println(label);

//        String url = "https://qccdata.qichacha.com/greenFinancial/b6c6af110ca7add772348b4eacbeadc3.pdf";
//        List<Map<String, String>> maps = PdfContentNlpUtil.parsePdf(url);
//        System.out.println(maps.size());
    }

    @Test
    public void testTime() throws IOException {
        System.out.println(getPublishTime("信息来源：本网 发布日期：2022-04-15 浏览次数：2742 分享："));
    }

    public Timestamp getPublishTime(String publishTimeStr){
        if(BaseStringUtil.checkChineseChar(publishTimeStr)){
            publishTimeStr = BaseStringUtil.getYMDdateStr(publishTimeStr);
        }

        if(publishTimeStr.contains(" ")){
            String[] split = publishTimeStr.split(" ");
            if(split != null && split.length == 2){
                String dateStr = split[0];
                String timeStr = split[1];

                dateStr = BaseStringUtil.getYMDdateStr(dateStr);
                dateStr = DateUtil.getStringFormatTimeOfDate(DateUtil.getDateOfStringFormatTime(dateStr, DateUtil.YMD), DateUtil.YMD_DASH);
                if(timeStr.length() - timeStr.replaceAll(":", "").length() == 1){
                    timeStr += ":00";
                }
                Timestamp timestamp = Timestamp.valueOf(String.format("%s %s", dateStr, timeStr));
                return timestamp;
            }
            String dateStr = BaseStringUtil.getYMDdateStr(publishTimeStr);
            dateStr = DateUtil.getStringFormatTimeOfDate(DateUtil.getDateOfStringFormatTime(dateStr, DateUtil.YMD), DateUtil.YMD_DASH);
            Timestamp timestamp = Timestamp.valueOf(String.format("%s %s", dateStr, "00:00:00"));
            return timestamp;
        } else {
            String ymDdateStr = BaseStringUtil.getYMDdateStr(publishTimeStr);
            Date dateOfStringFormatTime = DateUtil.getDateOfStringFormatTime(ymDdateStr, DateUtil.YMD);
            Timestamp timestamp = new Timestamp(dateOfStringFormatTime.getTime());
            return timestamp;
        }
    }


    @Test
    public void testFactory() throws InstantiationException, IllegalAccessException, IOException {
        List<String> ids = FileUtils.readLines(new File("c:\\work\\id.txt"), "utf-8");
        CompGreenFinancialNoticeInfoRepository noticeInfoRepository = ApplicationCoreContextManager.getInstance(CompGreenFinancialNoticeInfoRepository.class);
        List<CompGreenFinancialNoticeInfo> infos = noticeInfoRepository.selectByPrimaryKeyList(ids);
        infos.forEach(iter -> iter.setDealType(1));
        noticeInfoRepository.insertBatch(infos);
    }

    @Test
    public void testMatch() throws IOException {
        List<String> lines = FileUtils.readLines(new File("c:\\work\\id.txt"), "utf-8");

        CompGreenFinancialNoticeMidRepository midRepository = ApplicationCoreContextManager.getInstance(CompGreenFinancialNoticeMidRepository.class);
        List<CompGreenFinancialNoticeMid> list = midRepository.selectByPrimaryKeyList(lines);
        list.forEach(x->{
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("comp_green_financial_notice_mid", Collections.singletonList(x));
            KafkaHelper.javaKafkaProducer("base_green_financial_data_test", JSON.toJSONString(jsonObject));
        });
    }
}
