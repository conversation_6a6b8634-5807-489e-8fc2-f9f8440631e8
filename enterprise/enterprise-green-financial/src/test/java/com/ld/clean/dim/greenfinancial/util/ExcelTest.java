package com.ld.clean.dim.greenfinancial.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.base.clean.utils.BaseCephUtil;
import com.ld.clean.util.ReadMergeCellExcelUtil;
import org.apache.commons.io.FileUtils;
import org.junit.Test;

import java.io.*;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * <AUTHOR>
 * @date ：2024/10/12  14:35
 * @description ：
 */
public class ExcelTest {

    @Test
    public void parseExcels() throws IOException {
        List<String> lines = FileUtils.readLines(new File("c:\\work\\id.txt"), "utf-8");
        AtomicInteger index = new AtomicInteger(1);
        lines.forEach(line -> {
            String key = line.substring(line.lastIndexOf("/") + 1);
            if(BaseCephUtil.doesObjectExist(key, "qcc-data/greenFinancial", 2)) {
                InputStream inputStream = BaseCephUtil.getCleanObject("qcc-data/greenFinancial", key);
                List<Map<String, String>> maps = ReadMergeCellExcelUtil.readExcelToObj(inputStream);
                System.out.println(index.getAndIncrement() + ":" + key + "----" + maps.size());
            }
        });
    }

    @Test
    public void parseSingle() throws IOException {
        List<String> lines = FileUtils.readLines(new File("c:\\work\\id.txt"), "utf-8");
        lines.forEach(iter -> {
            JSONArray array = JSON.parseArray(iter);
            for (int i = 0; i < array.size(); i++) {
                JSONObject jsonObject = array.getJSONObject(i);
                String ossFileKey = jsonObject.getString("OssFileKey");
                if(ossFileKey.contains("xlsx") || ossFileKey.contains("xls")){

                    InputStream inputStream = BaseCephUtil.getCleanObject("qcc-data/greenFinancial", ossFileKey);
                    Map<String, List<Map<String, String>>> stringListMap = ReadMergeCellExcelUtil.readAllExcelToObj(inputStream);
                    System.out.println(JSON.toJSONString(stringListMap));

                }
            }
        });

    }
}
