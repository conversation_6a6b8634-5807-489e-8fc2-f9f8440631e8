package com.ld.clean.dim.greenfinancial.util;

import cn.hutool.core.collection.CollectionUtil;
import com.ld.clean.context.ApplicationCoreContextManager;
import com.ld.clean.model.manageenterprisedb.CompGreenFinancialDetailInfo;
import com.ld.clean.model.manageenterprisedb.CompGreenFinancialListInfo;
import com.ld.clean.model.searchsyncenterprise.CompGreenFinancialListInfoSync;
import com.ld.clean.repository.manageenterprisedb.CompGreenFinancialDetailInfoRepository;
import com.ld.clean.repository.manageenterprisedb.CompGreenFinancialListInfoRepository;
import com.ld.clean.repository.tidbsearchsyncenterprise.CompGreenFinancialListInfoSyncRepository;
import com.ld.clean.utils.BlockThreadPool;
import org.apache.commons.compress.utils.Lists;
import org.junit.Test;
import tk.mybatis.mapper.entity.Condition;

import java.util.List;
import java.util.Objects;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;
import java.util.stream.Collectors;

public class MyTestSync {
    private static final BlockThreadPool SEARCH_POOL = new BlockThreadPool("CleanProcess_POOL", 10);

    private static final CompGreenFinancialListInfoRepository LIST_REPO = ApplicationCoreContextManager.getInstance(CompGreenFinancialListInfoRepository.class);
    private static final CompGreenFinancialDetailInfoRepository DETAIL_REPO = ApplicationCoreContextManager.getInstance(CompGreenFinancialDetailInfoRepository.class);
    private static CompGreenFinancialListInfoSyncRepository SYNC_REPO = ApplicationCoreContextManager.getInstance(CompGreenFinancialListInfoSyncRepository.class);

    @Test
    public void testAddSync() throws Exception {
        String id = "";
        int allCount = 0;
        boolean running = true;
        while (running) {
            List<CompGreenFinancialListInfo> manageList = Lists.newArrayList();
            Lock lock = new ReentrantLock();
            if (lock.tryLock()) {
                Condition condition = new Condition(CompGreenFinancialListInfo.class);
                condition.setOrderByClause("id ASC LIMIT 1000");
                condition.createCriteria().andGreaterThan("id", id);
                manageList = LIST_REPO.selectByCondition(condition);

                if (CollectionUtil.isNotEmpty(manageList)) {
                    allCount += manageList.size();
                    id = manageList.get(manageList.size() - 1).getId();
                    System.out.println("StartId:" + id + ",TotalCount:" + allCount);
                }
                lock.unlock();
            }

            manageList = manageList.stream()
                    .filter(e -> Objects.equals(e.getSourceWeb(), "新能源云") || Objects.equals(e.getSourceWeb(), "全国认证认可信息平台"))
                    .collect(Collectors.toList());

            if (CollectionUtil.isNotEmpty(manageList)) {
                manageList.forEach(e -> {
                    if (Objects.equals(e.getSourceWeb(), "新能源云")) {
                        e.setSourceWeb("国网新能源云");
                    } else if (Objects.equals(e.getSourceWeb(), "全国认证认可信息平台")) {
                        e.setSourceWeb("全国认证认可信息公共服务平台");
                    }
                });
                LIST_REPO.insertBatch(manageList);
            } else {
                System.out.println("END!");
            }
        }
    }

    @Test
    public void testDetailSync() throws Exception {
        String id = "";
        int allCount = 0;
        boolean running = true;
        while (running) {
            List<CompGreenFinancialDetailInfo> detailList = Lists.newArrayList();
            Lock lock = new ReentrantLock();
            if (lock.tryLock()) {
                Condition condition = new Condition(CompGreenFinancialDetailInfo.class);
                condition.setOrderByClause("id ASC LIMIT 1000");
                condition.createCriteria().andGreaterThan("id", id);
                detailList = DETAIL_REPO.selectByCondition(condition);

                if (CollectionUtil.isNotEmpty(detailList)) {
                    allCount += detailList.size();
                    id = detailList.get(detailList.size() - 1).getId();
                    System.out.println("StartId:" + id + ",TotalCount:" + allCount);
                }
                lock.unlock();
            }

            detailList = detailList.stream()
                    .filter(e -> Objects.equals(e.getSourceWeb(), "新能源云") || Objects.equals(e.getSourceWeb(), "全国认证认可信息平台"))
                    .collect(Collectors.toList());

            if (CollectionUtil.isNotEmpty(detailList)) {
                List<CompGreenFinancialDetailInfo> finalDetailList = detailList;
                SEARCH_POOL.execute(() -> {
                    finalDetailList.forEach(e -> {
                        if (Objects.equals(e.getSourceWeb(), "新能源云")) {
                            e.setSourceWeb("国网新能源云");
                        } else if (Objects.equals(e.getSourceWeb(), "全国认证认可信息平台")) {
                            e.setSourceWeb("全国认证认可信息公共服务平台");
                        }
                    });
                    DETAIL_REPO.insertBatch(finalDetailList);
                });
            } else {
                System.out.println("END!");
            }
        }
    }

    @Test
    public void testSearchSync() throws Exception {
        String id = "";
        int allCount = 0;
        boolean running = true;
        while (running) {
            List<CompGreenFinancialListInfoSync> searchList = Lists.newArrayList();
            Lock lock = new ReentrantLock();
            if (lock.tryLock()) {
                Condition condition = new Condition(CompGreenFinancialListInfoSync.class);
                condition.setOrderByClause("id ASC LIMIT 100");
                condition.createCriteria().andGreaterThan("id", id);
                searchList = SYNC_REPO.selectByCondition(condition);

                if (CollectionUtil.isNotEmpty(searchList)) {
                    allCount += searchList.size();
                    id = searchList.get(searchList.size() - 1).getId();
                    System.out.println("StartId:" + id + ",TotalCount:" + allCount);
                }
                lock.unlock();
            }

            searchList = searchList.stream()
                    .filter(e -> e.getSourceInfo().contains("全国认证认可信息平台"))
                    .collect(Collectors.toList());

            if (CollectionUtil.isNotEmpty(searchList)) {
                List<CompGreenFinancialListInfoSync> finalDetailList = searchList;
                SEARCH_POOL.execute(() -> {
                    finalDetailList.forEach(e -> {
                        if (e.getSourceInfo().contains("全国认证认可信息平台")) {
                            e.setSourceInfo(e.getSourceInfo().replace("全国认证认可信息平台", "全国认证认可信息公共服务平台"));
                        }
                    });
                    SYNC_REPO.insertBatch(finalDetailList);
                });
            } else {
                System.out.println("END!");
            }
        }
    }
}