package com.ld.clean.dim.greenfinancial.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.ld.clean.context.ApplicationCoreContextManager;
import com.ld.clean.core.dto.company.Company;
import com.ld.clean.dto.company.CompanyCommonOut;
import com.ld.clean.dto.company.QccCompanyOut;
import com.ld.clean.kafka.KafkaHelper;
import com.ld.clean.model.manageenterprisedb.CompGreenFinancialListInfo;
import com.ld.clean.model.manageenterprisedb.CompGreenFinancialNoticeInfo;
import com.ld.clean.model.manageenterprisedb.CompGreenFinancialNoticeMid;
import com.ld.clean.model.searchsyncenterprise.CompGreenFinancialListInfoSync;
import com.ld.clean.repository.basecleandatatmp.MeCompGreenFinancialListInfo240327Repository;
import com.ld.clean.repository.basecleandatatmp.SeCompGreenFinancialListInfoSync240319Repository;
import com.ld.clean.repository.manageenterprisedb.CompGreenFinancialDetailInfoRepository;
import com.ld.clean.repository.manageenterprisedb.CompGreenFinancialListInfoRepository;
import com.ld.clean.repository.manageenterprisedb.CompGreenFinancialNoticeInfoRepository;
import com.ld.clean.repository.manageenterprisedb.CompGreenFinancialNoticeMidRepository;
import com.ld.clean.repository.tidbsearchsyncenterprise.CompGreenFinancialListInfoSyncRepository;
import com.ld.clean.util.GreenFinancialUtil;
import com.ld.clean.utils.CommonUtil;
import com.ld.clean.utils.CompanyDetailsUtil;
import com.ld.clean.utils.MD5Util;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.junit.Test;
import tk.mybatis.mapper.entity.Condition;

import java.io.File;
import java.io.IOException;
import java.util.*;

public class SyncTest {
    private static CompGreenFinancialListInfoSyncRepository SYNC_REPO = ApplicationCoreContextManager.getInstance(CompGreenFinancialListInfoSyncRepository.class);
    private static final CompGreenFinancialListInfoRepository REPO = ApplicationCoreContextManager.getInstance(CompGreenFinancialListInfoRepository.class);
    private static final CompGreenFinancialDetailInfoRepository detail_repo = ApplicationCoreContextManager.getInstance(CompGreenFinancialDetailInfoRepository.class);
    private static final MeCompGreenFinancialListInfo240327Repository tmp_repo = ApplicationCoreContextManager.getInstance(MeCompGreenFinancialListInfo240327Repository.class);
    private static final SeCompGreenFinancialListInfoSync240319Repository sync_tmp_repo = ApplicationCoreContextManager.getInstance(SeCompGreenFinancialListInfoSync240319Repository.class);

    @Test
    public void testPdf() throws IOException {
        List<String> lines = FileUtils.readLines(new File("C:\\work\\id.txt"), "utf-8");

        List<CompGreenFinancialListInfo> listInfoList = REPO.selectByPrimaryKeyList(lines);
        List<CompGreenFinancialListInfoSync> syncList = SYNC_REPO.selectByPrimaryKeyList(lines);
        syncList.forEach(x -> {
            CompGreenFinancialListInfo first = listInfoList.stream().filter(y -> StringUtils.equals(x.getId(), y.getId())).findFirst().orElse(null);
            x.setDataStatus(first.getDataStatus());
        });

        SYNC_REPO.insertBatch(syncList);

    }

    @Test
    public void testDelete() throws IOException {
        List<String> ids = FileUtils.readLines(new File("c:\\work\\id.txt"), "utf-8");
        List<CompGreenFinancialListInfo> lists = REPO.selectByPrimaryKeyList(ids);

        List<CompGreenFinancialListInfoSync> syncs = SYNC_REPO.selectByPrimaryKeyList(ids);

        syncs.forEach(x -> {
            CompGreenFinancialListInfo listInfo = lists.stream().filter(y -> x.getId().equals(y.getId())).findFirst().orElse(null);
            if(listInfo != null){
                x.setDataStatus(listInfo.getDataStatus());
            }
        });

        SYNC_REPO.insertBatch(syncs);

//        CompGreenFinancialListInfo listInfo = REPO.selectByPrimaryKey("8567960ba2fb53b39839a1fa679cbee8");
//        listInfo.setDataStatus(1);
//        REPO.insertBatch(Collections.singletonList(listInfo));


    }

    @Test
    public void test(){
        List<String> fields = Arrays.asList("_id", "Province", "Tags");
        List<QccCompanyOut> qccCompanyList = CompanyDetailsUtil.getCompanyList(Arrays.asList("g2f307ad8cac1e9390fa517f08bb3abb"), false, fields);
        if (CollectionUtils.isNotEmpty(qccCompanyList)) {
            CompanyCommonOut comm = qccCompanyList.get(0).getCommonList().stream().filter(e -> 117 == e.getKey()).findFirst().orElse(null);
            if(comm != null){

            }
        }
    }

    @Test
    public void testContent(){
        String str = "{\"id\":\"071b5c16f71a11eead91bc97e108a120\",\"data_status\":\"1\",\"create_date\":\"2024-10-31 20:03:22\",\"update_date\":\"2024-10-31 20:03:22\",\"spider_id\":\"071b5c16f71a11eead91bc97e108a120\",\"title\":\"晋中市生态环境局关于第一批入河排污口设置论证审查专家人选名单的公示\",\"publish_time\":\"2023-01-06 00:00:00\",\"channel\":\"通知公告\",\"content\":\"根据晋中市生态环境局《关于开展晋中市环境影响评价技术评审专家库更新工作的通知》（市环函〔2022〕354号），结合个人申请和单位推荐，经遴选，现确定第一批69名入河排污口设置论证审查专家人选名单（具体名单见附件）。 现将专家名单予以公示，公示期为5个工作日（2023年1月6日至1月12日）。公示期内，若无异议即为生效，若对专家有异议或发现专家信息有误，可如实反映并提供相关证明材料。以单位名义反映问题的材料，应加盖单位公章；以个人名义反映问题的材料，应署实名并提供联系方式，便于调查核实。 电  话：0354-3029550 邮  箱：jzhbhpk＠163.com 通讯地址：山西省晋中市榆次区广安街市民之家生态环境窗口 附件：1215晋中市生态环境局入河人排污口专家库专家名单.xlsx                      晋中市生态环境局 2023年1月6日\",\"attachmentinfos\":\"[{\\\"Type\\\":\\\".xlsx\\\",\\\"OssFileKey\\\":\\\"b8ac183d824cb14f4ccb5c6d906cd644.xlsx\\\",\\\"ImgExtract\\\":0,\\\"HtmlRenderAnalysis\\\":-1,\\\"Url\\\":\\\"https://qccdata.qichacha.com/greenFinancial/b8ac183d824cb14f4ccb5c6d906cd644.xlsx\\\",\\\"Name\\\":\\\"1215晋中市生态环境局入河人排污口专家库专家名单.xlsx\\\"},{\\\"Type\\\":\\\".gif\\\",\\\"OssFileKey\\\":\\\"5480518832fc2d48c5b2a77b2c150e5b.gif\\\",\\\"ImgExtract\\\":0,\\\"HtmlRenderAnalysis\\\":-1,\\\"Url\\\":\\\"https://qccdata.qichacha.com/greenFinancial/5480518832fc2d48c5b2a77b2c150e5b.gif\\\",\\\"Name\\\":\\\"附件\\\"}]\",\"content_s3\":\"http://data-s3.ld-hadoop.com/data-spider/green_finance_render_detail_content/d7a4c77e5cc8c4f1c00e89ff1367b022\",\"source_web\":\"晋中市生态环境局\",\"source_url\":\"https://sthjj.sxjz.gov.cn/tzgg/content_447201\",\"spider_table\":\"spider_company_base_info.t_green_finance_spot_sampling_result\",\"data_source\":\"d_spider_16bc51ab298045c9801742ed248044ca\"}";
        System.out.println(CommonUtil.getString(str));
    }

    @Test
    public void testCompName() throws IOException {
        List<String> lines = FileUtils.readLines(new File("c:\\work\\id.txt"), "utf-8");
        lines.forEach(line -> {

            List<Company> listCompanyByCompanyName = GreenFinancialUtil.getListCompanyByCompanyName(line, "", "");
            System.out.println(line + "   " + JSON.toJSONString(listCompanyByCompanyName));
        });

    }



    @Test
    public void testDeleteTmp() throws IOException {
//        MeCompGreenFinancialListInfo240327 query = new MeCompGreenFinancialListInfo240327();
//        query.setDataStatus(3);
//        query.setSourceTable("manage_enterprise_db.comp_green_financial_notice_info");
//
//        List<MeCompGreenFinancialListInfo240327> select = tmp_repo.select(query);
//        List<String> ids = select.stream().map(BaseModel::getId).collect(Collectors.toList());
//        Lists.partition( ids, 500).forEach(iter -> tmp_repo.deleteByPrimaryKeyList(iter));

        List<String> lines = FileUtils.readLines(new File("c:\\work\\id.txt"), "utf-8");
        Lists.partition(lines, 200).forEach(iter -> tmp_repo.deleteByPrimaryKeyList(iter));
    }

    @Test
    public void deleteNotice() throws IOException {
        List<String> lines = FileUtils.readLines(new File("c:\\work\\id.txt"), "utf-8");

        CompGreenFinancialNoticeInfoRepository repository = ApplicationCoreContextManager.getInstance(CompGreenFinancialNoticeInfoRepository.class);

        List<CompGreenFinancialNoticeInfo> select = repository.selectByPrimaryKeyList(lines);
        select.forEach(iter -> {
            iter.setDealType(1);
        });

        repository.insertBatch(select);
    }

    @Test
    public void testKafka() throws IOException {
        List<String> input = FileUtils.readLines(new File("c:\\work\\id.txt"), "utf-8");

        CompGreenFinancialNoticeMidRepository R = ApplicationCoreContextManager.getInstance(CompGreenFinancialNoticeMidRepository.class);


        com.google.common.collect.Lists.partition(input, 500).forEach(ids -> {
            Condition condition = new Condition(CompGreenFinancialNoticeMid.class);
            condition.createCriteria()
                    .andEqualTo("dataStatus", 1)
                    .andIn("id", ids);
            List<CompGreenFinancialNoticeMid> foods = R.selectByCondition(condition);
            foods.forEach(iter -> {
                JSONObject json = new JSONObject();
                json.put("comp_green_financial_notice_mid", Collections.singletonList(iter));
                System.out.println(json);
                KafkaHelper.javaKafkaProducer("base_industry_vertical_product_merge_source", JSON.toJSONString(json));
            });
        });
    }

    @Test
    public void testTitle() throws IOException {
        List<String> input = FileUtils.readLines(new File("c:\\work\\id.txt"), "utf-8");
        SYNC_REPO.deleteByPrimaryKeyList(input);
        List<CompGreenFinancialListInfoSync> syncs = SYNC_REPO.selectByPrimaryKeyList(input);
        List<CompGreenFinancialListInfo> bases = REPO.selectByPrimaryKeyList(input);
        bases.forEach(iter -> {
            CompGreenFinancialListInfoSync sync = syncs.stream().filter(x -> StringUtils.equals(x.getId(), iter.getId())).findFirst().orElse(null);
            if(sync != null){
                iter.setDataStatus(sync.getDataStatus());
            }
        });
        REPO.insertBatch(bases);
    }

    @Test
    public void testPkId(){
        CompGreenFinancialListInfoRepository repository = ApplicationCoreContextManager.getInstance(CompGreenFinancialListInfoRepository.class);
        CompGreenFinancialListInfoSyncRepository syncRepository = ApplicationCoreContextManager.getInstance(CompGreenFinancialListInfoSyncRepository.class);

        CompGreenFinancialListInfo listInfo = repository.selectByPrimaryKey("44b0fc691f60ab8cc2eecbfbad323474");

        CompGreenFinancialListInfoSync sync = syncRepository.selectByPrimaryKey("44b0fc691f60ab8cc2eecbfbad323474");


        System.out.println(MD5Util.encode(listInfo.getCompName(), listInfo.getCompCategoryCode(), listInfo.getNoticeDate(),
                listInfo.getPtTechName(), listInfo.getCertificateNumber(), listInfo.getIssuingAuthority(), listInfo.getStartDate()));

        System.out.println(MD5Util.encode("日商爱帝欧股份有限公司", listInfo.getCompCategoryCode(), listInfo.getNoticeDate(),
                listInfo.getPtTechName(), listInfo.getCertificateNumber(), listInfo.getIssuingAuthority(), listInfo.getStartDate()));

    }
}
