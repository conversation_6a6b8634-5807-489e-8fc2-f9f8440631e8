package com.ld.clean.dim.greenfinancial.util;

import com.base.clean.utils.BaseStringUtil;
import com.google.common.collect.Lists;
import com.ld.clean.context.ApplicationCoreContextManager;
import com.ld.clean.dto.company.CompanyCommonOut;
import com.ld.clean.dto.company.QccCompanyOut;
import com.ld.clean.model.basecleandatatmp.SeCompGreenFinancialAttachmentSync240911;
import com.ld.clean.model.manageenterprisedb.CompGreenFinancialAttachment;
import com.ld.clean.model.manageenterprisedb.CompGreenFinancialList;
import com.ld.clean.model.searchsyncenterprise.CompGreenFinancialAttachmentSync;
import com.ld.clean.model.searchsyncenterprise.CompGreenFinancialListSync;
import com.ld.clean.repository.basecleandatatmp.SeCompGreenFinancialAttachmentSync240911Repository;
import com.ld.clean.repository.manageenterprisedb.CompGreenFinancialAttachmentRepository;
import com.ld.clean.repository.manageenterprisedb.CompGreenFinancialListRepository;
import com.ld.clean.repository.tidbsearchsyncenterprise.CompGreenFinancialAttachmentSyncRepository;
import com.ld.clean.repository.tidbsearchsyncenterprise.CompGreenFinancialListSyncRepository;
import com.ld.clean.util.GreenFinancialEntityUtil;
import com.ld.clean.util.GreenFinancialUtil;
import com.ld.clean.utils.CommonUtil;
import com.ld.clean.utils.CompanyDetailsUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.junit.Test;
import tk.mybatis.mapper.entity.Condition;

import java.io.File;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date ：2024/7/18  10:05
 * @description ：
 */
public class GreenAttachmentTest {

    private static final CompGreenFinancialAttachmentRepository REPOSITORY = ApplicationCoreContextManager.getInstance(CompGreenFinancialAttachmentRepository.class);
    private static final CompGreenFinancialAttachmentSyncRepository SYNC_REPOSITORY = ApplicationCoreContextManager.getInstance(CompGreenFinancialAttachmentSyncRepository.class);

    private static final CompGreenFinancialListRepository R2 = ApplicationCoreContextManager.getInstance(CompGreenFinancialListRepository.class);
    private static final CompGreenFinancialListSyncRepository SYNC = ApplicationCoreContextManager.getInstance(CompGreenFinancialListSyncRepository.class);

    private static final SeCompGreenFinancialAttachmentSync240911Repository TMP_SYNC_REPOSITORY = ApplicationCoreContextManager.getInstance(SeCompGreenFinancialAttachmentSync240911Repository.class);

    @Test
    public void deltetError() throws InterruptedException {
        CompGreenFinancialListSync li = (CompGreenFinancialListSync) SYNC.selectByPrimaryKey("c268baf1f369f1f26dc5cae123b4900a");
        li.setDataStatus(2);
        SYNC.insertBatch(Collections.singletonList(li));



//        List<CompGreenFinancialList> base = R2.selectByPrimaryKeyList(ids);
//        base.forEach(x ->x.setDataStatus(2));
//        R2.insertBatch(base);
//
//        List<CompGreenFinancialListSync> sync = SYNC.selectByPrimaryKeyList(ids);
//        sync.forEach(x ->x.setDataStatus(2));
//        SYNC.insertBatch(sync);

//         List<CompGreenFinancialAttachment> att = REPOSITORY.selectByPrimaryKeyList(ids);
//         att.forEach(x -> x.setDataStatus(2));
//         REPOSITORY.insertBatch(att);


//         List<CompGreenFinancialAttachmentSync> attSync = SYNC_REPOSITORY.selectByPrimaryKeyList(ids);
//         attSync.forEach(x -> x.setDataStatus(2));
//         SYNC_REPOSITORY.insertBatch(attSync);
//        Thread.sleep(1000);
//        SYNC_REPOSITORY.deleteByPrimaryKeyList(ids);
    }



    @Test
    public void change() {

        CompGreenFinancialAttachment query = new CompGreenFinancialAttachment();
        query.setDataStatus(1);

        List<CompGreenFinancialAttachment> select = REPOSITORY.select(query);
        select.forEach(att ->{

            CompGreenFinancialList listQuery = new CompGreenFinancialList();
            listQuery.setDataStatus(1);
            listQuery.setAttachmentId(att.getAttachmentId());

            List<CompGreenFinancialList> list = R2.select(listQuery);
            Set<String> set = list.stream().map(CompGreenFinancialList::getCompanyKeyno).collect(Collectors.toSet());

            if(CollectionUtils.isNotEmpty(set)) {
                List<String> fields = Arrays.asList("_id", "Province", "CommonList");
                List<QccCompanyOut> qccCompanyList = CompanyDetailsUtil.getCompanyList(new ArrayList<>(set), false, fields);
                if (CollectionUtils.isNotEmpty(qccCompanyList)) {
                    Set<String> provs = qccCompanyList.stream().map(QccCompanyOut::getProvince).collect(Collectors.toSet());
                    Set<String> areas = qccCompanyList.stream().flatMap(iter -> {
                        List<String> array = new ArrayList<>();
                        CompanyCommonOut comm = iter.getCommonList().stream().filter(e -> 28 == e.getKey()).findFirst().orElse(null);
                        if (comm != null && StringUtils.isNotEmpty(comm.getValue()) && comm.getValue().length() == 6) {
                            String cityCode = comm.getValue().substring(0, 4);
                            String countryCode = comm.getValue().substring(0, 6);
                            array.add(cityCode);
                            array.add(countryCode);
                        }
                        return array.stream();
                    }).filter(Objects::nonNull).collect(Collectors.toSet());

                    att.setProvinces(provs.stream().sorted().collect(Collectors.joining(",")));
                    att.setAreaCodes(areas.stream().sorted().collect(Collectors.joining(",")));
                }
            }
        });

        Lists.partition(select, 200).forEach(iter -> {
            REPOSITORY.insertBatch(iter);
        });

    }

    @Test
    public void testAtt(){
        String str = "2022.7.22";
        String ymDdateStr = BaseStringUtil.getYMDdateStr(str);
        System.out.println(ymDdateStr);
    }

}
