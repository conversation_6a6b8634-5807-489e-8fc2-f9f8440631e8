package com.ld.clean.dim.greenfinancial.notice;

import com.alibaba.fastjson.JSON;
import com.ld.clean.context.ApplicationCoreContextManager;
import com.ld.clean.model.manageenterprisedb.CompGreenFinancialExtractRecord;
import com.ld.clean.model.manageenterprisedb.CompGreenFinancialNoticeInfo;
import com.ld.clean.mongo.BaseMongoEnum;
import com.ld.clean.repository.manageenterprisedb.CompGreenFinancialExtractRecordRepository;
import com.ld.clean.repository.manageenterprisedb.CompGreenFinancialNoticeInfoRepository;
import com.ld.clean.utils.MD5Util;
import com.ld.clean.utils.MongodbUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.junit.Test;
import org.springframework.beans.BeanUtils;

import java.io.File;
import java.io.IOException;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date ：2025/4/23  15:39
 * @description ：
 */
public class PromptTest {

    private static final CompGreenFinancialExtractRecordRepository EXTRACT_RECORD_REPOSITORY = ApplicationCoreContextManager.getInstance(CompGreenFinancialExtractRecordRepository.class);

    private static final CompGreenFinancialNoticeInfoRepository NOTICE_INFO_REPOSITORY = ApplicationCoreContextManager.getInstance(CompGreenFinancialNoticeInfoRepository.class);
    @Test
    public void deleteError() throws IOException {
        List<String> strings = FileUtils.readLines(new File("c:\\work\\id.txt"), "utf-8");
        EXTRACT_RECORD_REPOSITORY.deleteByPrimaryKeyList(strings);
    }
}
