package com.ld.clean.dim.greenfinancial.comp;

import com.ld.clean.context.ApplicationCoreContextManager;
import com.ld.clean.repository.manageenterprisedb.CompGreenFinancialListInfoRepository;
import org.apache.commons.io.FileUtils;
import org.junit.Test;

import java.io.File;
import java.io.IOException;
import java.util.List;

/**
 * <AUTHOR>
 * @date ：2025/4/27  11:15
 * @description ：
 */
public class SendBinlogTest {

    private static final CompGreenFinancialListInfoRepository REPOSITORY = ApplicationCoreContextManager.getInstance(CompGreenFinancialListInfoRepository.class);

    @Test
    public void addTime() throws IOException {
        List<String> strings = FileUtils.readLines(new File("c:\\work\\id.txt"), "utf-8");
        REPOSITORY.addUpdateTime(strings);
    }
}
