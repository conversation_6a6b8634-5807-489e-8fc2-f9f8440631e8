/*
*  Copyright 2021-2031 基础数据
*/
package com.ld.clean.repository.searchoverseadb;

import com.ld.clean.context.ApplicationDaoContextManager;
import com.ld.clean.core.base.BaseRepository;
import com.ld.clean.mapper.searchoverseadb.CompIpClassificationSyncMapper;
import com.ld.clean.model.searchoverseadb.CompIpClassificationSync;

/**
* @website https://www.qcc.com
* <AUTHOR>
* @date 2022-12-30
**/
public class CompIpClassificationSyncRepository extends  BaseRepository {
    private static final CompIpClassificationSyncMapper compIpClassificationSyncMapper = ApplicationDaoContextManager.getInstance(CompIpClassificationSyncMapper.class);

    public CompIpClassificationSyncRepository() {
      super(compIpClassificationSyncMapper, CompIpClassificationSync.class);
    }
}