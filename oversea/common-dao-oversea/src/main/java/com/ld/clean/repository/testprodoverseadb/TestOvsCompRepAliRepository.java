/*
 *  Copyright 2021-2031 基础数据
 */
package com.ld.clean.repository.testprodoverseadb;

import com.ld.clean.context.ApplicationDaoContextManager;
import com.ld.clean.core.base.BaseRepository;
import com.ld.clean.mapper.testprodoverseadb.TestOvsCompRepAliMapper;
import com.ld.clean.model.prodoverseadb.OvsCompRep;

/**
 * <AUTHOR>
 * @website https://www.qcc.com
 * @date 2022-07-04
 **/
public class TestOvsCompRepAliRepository extends BaseRepository {

    private static final TestOvsCompRepAliMapper TEMP_MAPPER = ApplicationDaoContextManager.getInstance(TestOvsCompRepAliMapper.class);

    public TestOvsCompRepAliRepository() {
        super(TEMP_MAPPER, OvsCompRep.class);
    }
}