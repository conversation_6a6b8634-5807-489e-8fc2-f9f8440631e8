/*
 *  Copyright 2021-2031 基础数据
 */
package com.ld.clean.repository.manageoverseadb;

import com.google.inject.Inject;
import com.ld.clean.context.ApplicationDaoContextManager;
import com.ld.clean.core.base.BaseRepository;
import com.ld.clean.mapper.manageoverseadb.CompOvsBasicinfoChgMapper;
import com.ld.clean.mapper.manageoverseadb.CompOvsBasicinfoMapper;
import com.ld.clean.model.manageoverseadb.CompOvsBasicinfo;
import com.ld.clean.model.manageoverseadb.CompOvsBasicinfoChg;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import tk.mybatis.mapper.entity.Example;

import java.util.ArrayList;
import java.util.List;

/**
 * 海外企业基本信息历史变更表
 *
 * <AUTHOR>
 * @website https://www.qcc.com
 * @date 2022-06-13
 **/
public class CompOvsBasicinfoChgRepository extends BaseRepository {

    private static final CompOvsBasicinfoChgMapper TEMP_MAPPER = ApplicationDaoContextManager.getInstance(CompOvsBasicinfoChgMapper.class);

    public CompOvsBasicinfoChgRepository() {
        super(TEMP_MAPPER, CompOvsBasicinfoChg.class);
    }

    public List<CompOvsBasicinfoChg> selectByCompKeyNo(String compKeyno,Integer dataStatus) {
        if(StringUtils.isNotBlank(compKeyno)){
            return TEMP_MAPPER.selectWithoutDateByKeyNo(compKeyno,dataStatus);
        }else{
            return new ArrayList<>();
        }

    }

    public List<CompOvsBasicinfoChg> selectByExample(Example example) {
        if (example == null) {
            return new ArrayList<>();
        }
        return TEMP_MAPPER.selectByExample(example);
    }
}