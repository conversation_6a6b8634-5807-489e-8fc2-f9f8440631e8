/*
 *  Copyright 2021-2031 基础数据
 */
package com.ld.clean.repository.elasticsearchsync3;

import com.ld.clean.context.ApplicationDaoContextManager;
import com.ld.clean.core.base.BaseRepository;
import com.ld.clean.mapper.elasticsearchsync3.TOverseasCompanyOsV2Mapper;
import com.ld.clean.model.elasticsearchsync3.TOverseasCompanyOsV2;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @website https://www.qcc.com
 * @date 2022-07-06
 **/
public class TOverseasCompanyOsV2Repository extends BaseRepository {

    private static final TOverseasCompanyOsV2Mapper TEMP_MAPPER = ApplicationDaoContextManager.getInstance(TOverseasCompanyOsV2Mapper.class);

    public TOverseasCompanyOsV2Repository() {
        super(TEMP_MAPPER, TOverseasCompanyOsV2.class);
    }

    /**
     * 批量插入(主键冲突更新)
     *
     * @param list
     * @return 受影响行数
     */
    public void insertBatchUtf8mb4(List<TOverseasCompanyOsV2> list) {
        if (CollectionUtils.isNotEmpty(list)) {
            TEMP_MAPPER.insertBatchUtf8mb4(list);
        }
    }
}