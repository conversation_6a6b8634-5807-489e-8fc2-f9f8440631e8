/*
*  Copyright 2021-2031 基础数据
*/
package com.ld.clean.repository.basecleandatatmp;

import com.ld.clean.model.basecleandatatmp.YuwlPoQccProdomphongkongreportinforealtimeorder250731;
import com.ld.clean.mapper.basecleandatatmp.YuwlPoQccProdomphongkongreportinforealtimeorder250731Mapper;
import com.ld.clean.context.ApplicationDaoContextManager;
import com.ld.clean.core.base.BaseRepository;

/**
* @website https://www.qcc.com
* <AUTHOR>
* @date 2025-07-31
**/
public class YuwlPoQccProdomphongkongreportinforealtimeorder250731Repository extends  BaseRepository {
    private static final YuwlPoQccProdomphongkongreportinforealtimeorder250731Mapper  yuwlPoQccProdomphongkongreportinforealtimeorder250731Mapper = ApplicationDaoContextManager.getInstance(YuwlPoQccProdomphongkongreportinforealtimeorder250731Mapper.class);

    public YuwlPoQccProdomphongkongreportinforealtimeorder250731Repository() {
      super(yuwlPoQccProdomphongkongreportinforealtimeorder250731Mapper, YuwlPoQccProdomphongkongreportinforealtimeorder250731.class);
    }
}