/*
*  Copyright 2021-2031 基础数据
*/
package com.ld.clean.repository.manageoverseadb;

import com.ld.clean.model.manageoverseadb.CompOvsOpensanctions;
import com.ld.clean.mapper.manageoverseadb.CompOvsOpensanctionsMapper;
import com.ld.clean.context.ApplicationDaoContextManager;
import com.ld.clean.core.base.BaseRepository;

/**
* @website https://www.qcc.com
* <AUTHOR>
* @date 2025-07-23
**/
public class CompOvsOpensanctionsRepository extends  BaseRepository {
    private static final CompOvsOpensanctionsMapper  compOvsOpensanctionsMapper = ApplicationDaoContextManager.getInstance(CompOvsOpensanctionsMapper.class);

    public CompOvsOpensanctionsRepository() {
      super(compOvsOpensanctionsMapper, CompOvsOpensanctions.class);
    }
}