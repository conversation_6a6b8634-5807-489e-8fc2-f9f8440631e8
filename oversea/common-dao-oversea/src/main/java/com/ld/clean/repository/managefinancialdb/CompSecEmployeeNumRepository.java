/*
*  Copyright 2021-2031 基础数据
*/
package com.ld.clean.repository.managefinancialdb;

import com.ld.clean.context.ApplicationDaoContextManager;
import com.ld.clean.core.base.BaseRepository;
import com.ld.clean.mapper.managefinancialdb.CompSecEmployeeNumMapper;
import com.ld.clean.model.managefinancialdb.CompSecEmployeeNum;

/**
* @website https://www.qcc.com
* <AUTHOR>
* @date 2023-11-22
**/
public class CompSecEmployeeNumRepository extends  BaseRepository {
    private static final CompSecEmployeeNumMapper compSecEmployeeNumMapper = ApplicationDaoContextManager.getInstance(CompSecEmployeeNumMapper.class);

    public CompSecEmployeeNumRepository() {
      super(compSecEmployeeNumMapper, CompSecEmployeeNum.class);
    }
}