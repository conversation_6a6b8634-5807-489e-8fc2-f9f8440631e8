/*
*  Copyright 2021-2031 基础数据
*/
package com.ld.clean.repository.basecleandatatmp;

import com.ld.clean.mapper.basecleandatatmp.YuwlMoOvsHkShareholder250528Mapper;
import com.ld.clean.context.ApplicationDaoContextManager;
import com.ld.clean.core.base.BaseRepository;
import com.ld.clean.model.basecleandatatmp.YuwlMoOvsHkShareholder250528;

/**
* @website https://www.qcc.com
* <AUTHOR>
* @date 2025-05-28
**/
public class YuwlMoOvsHkShareholder250528Repository extends  BaseRepository {
    private static final YuwlMoOvsHkShareholder250528Mapper  yuwlMoOvsHkShareholder250528Mapper = ApplicationDaoContextManager.getInstance(YuwlMoOvsHkShareholder250528Mapper.class);

    public YuwlMoOvsHkShareholder250528Repository() {
      super(yuwlMoOvsHkShareholder250528Mapper, YuwlMoOvsHkShareholder250528.class);
    }
}