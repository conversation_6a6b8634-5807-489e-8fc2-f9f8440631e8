/*
*  Copyright 2021-2031 基础数据
*/
package com.ld.clean.repository.searchsyncenterprise;

import com.ld.clean.model.searchsyncenterprise.CompanySearchKeyword;
import com.ld.clean.mapper.searchsyncenterprise.CompanySearchKeywordMapper;
import com.ld.clean.context.ApplicationDaoContextManager;
import com.ld.clean.core.base.BaseRepository;

/**
* @website https://www.qcc.com
* <AUTHOR>
* @date 2023-05-17
**/
public class CompanySearchKeywordRepository extends  BaseRepository {
    private static final CompanySearchKeywordMapper  companySearchKeywordMapper = ApplicationDaoContextManager.getInstance(CompanySearchKeywordMapper.class);

    public CompanySearchKeywordRepository() {
      super(companySearchKeywordMapper, CompanySearchKeyword.class);
    }
}