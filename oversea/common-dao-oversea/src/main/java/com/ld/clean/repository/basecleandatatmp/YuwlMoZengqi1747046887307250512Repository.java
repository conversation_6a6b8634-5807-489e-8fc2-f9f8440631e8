/*
*  Copyright 2021-2031 基础数据
*/
package com.ld.clean.repository.basecleandatatmp;

import com.ld.clean.model.basecleandatatmp.YuwlMoZengqi1747046887307250512;
import com.ld.clean.mapper.basecleandatatmp.YuwlMoZengqi1747046887307250512Mapper;
import com.ld.clean.context.ApplicationDaoContextManager;
import com.ld.clean.core.base.BaseRepository;

/**
* @website https://www.qcc.com
* <AUTHOR>
* @date 2025-05-12
**/
public class YuwlMoZengqi1747046887307250512Repository extends  BaseRepository {
    private static final YuwlMoZengqi1747046887307250512Mapper  yuwlMoZengqi1747046887307250512Mapper = ApplicationDaoContextManager.getInstance(YuwlMoZengqi1747046887307250512Mapper.class);

    public YuwlMoZengqi1747046887307250512Repository() {
      super(yuwlMoZengqi1747046887307250512<PERSON>apper, YuwlMoZengqi1747046887307250512.class);
    }
}