/*
*  Copyright 2021-2031 基础数据
*/
package com.ld.clean.repository.aliyunprodoverseadbtest;

import com.ld.clean.context.ApplicationDaoContextManager;
import com.ld.clean.core.base.BaseRepository;
import com.ld.clean.mapper.aliyunprodoverseadbtest.TestOvsHkReportresultProdMapper;
import com.ld.clean.mapper.prodoverseadb.OvsHkReportresultProdMapper;
import com.ld.clean.model.aliyunprodoverseadbtest.TestOvsHkReportresultProd;
import com.ld.clean.model.prodoverseadb.OvsHkReportresultProd;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import tk.mybatis.mapper.entity.Condition;

import java.util.List;

/**
* @website https://www.qcc.com
* <AUTHOR>
* @date 2024-02-02
**/
public class TestOvsHkReportresultProdRepository extends  BaseRepository {
    private static final TestOvsHkReportresultProdMapper ovsHkReportresultProdMapper = ApplicationDaoContextManager.getInstance(TestOvsHkReportresultProdMapper.class);

    public TestOvsHkReportresultProdRepository() {
      super(ovsHkReportresultProdMapper, TestOvsHkReportresultProd.class);
    }

    public TestOvsHkReportresultProd getHkReportResultByKeyNoAndDocNo(String compKeyNo,String docNo){
        if(StringUtils.isNotBlank(compKeyNo)&&StringUtils.isNotBlank(docNo)){
            Condition condition=new Condition(TestOvsHkReportresultProd.class);
            condition.createCriteria().andEqualTo("compKeyno",compKeyNo)
                    .andEqualTo("documentNumber",docNo)
                    .andEqualTo("dataStatus",1);
            List<TestOvsHkReportresultProd> list= ovsHkReportresultProdMapper.selectByCondition(condition);
            if(CollectionUtils.isNotEmpty(list)){
                return list.get(0);
            }
        }
        return null;
    }
}