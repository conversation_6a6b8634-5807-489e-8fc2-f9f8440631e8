/*
*  Copyright 2021-2031 基础数据
*/
package com.ld.clean.repository.basecleandatatmp;

import com.ld.clean.model.basecleandatatmp.YuwlMoOvsFileData250729;
import com.ld.clean.mapper.basecleandatatmp.YuwlMoOvsFileData250729Mapper;
import com.ld.clean.context.ApplicationDaoContextManager;
import com.ld.clean.core.base.BaseRepository;

/**
* @website https://www.qcc.com
* <AUTHOR>
* @date 2025-07-29
**/
public class YuwlMoOvsFileData250729Repository extends  BaseRepository {
    private static final YuwlMoOvsFileData250729Mapper  yuwlMoOvsFileData250729Mapper = ApplicationDaoContextManager.getInstance(YuwlMoOvsFileData250729Mapper.class);

    public YuwlMoOvsFileData250729Repository() {
      super(yuwlMoOvsFileData250729Mapper, YuwlMoOvsFileData250729.class);
    }
}