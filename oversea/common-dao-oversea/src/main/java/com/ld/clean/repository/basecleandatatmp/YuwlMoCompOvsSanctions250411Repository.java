/*
*  Copyright 2021-2031 基础数据
*/
package com.ld.clean.repository.basecleandatatmp;

import com.ld.clean.context.ApplicationDaoContextManager;
import com.ld.clean.core.base.BaseRepository;
import com.ld.clean.mapper.basecleandatatmp.YuwlMoCompOvsSanctions250411Mapper;
import com.ld.clean.model.basecleandatatmp.YuwlMoCompOvsSanctions250411;

/**
* @website https://www.qcc.com
* <AUTHOR>
* @date 2025-04-11
**/
public class YuwlMoCompOvsSanctions250411Repository extends  BaseRepository {
    private static final YuwlMoCompOvsSanctions250411Mapper yuwlMoCompOvsSanctions250411Mapper = ApplicationDaoContextManager.getInstance(YuwlMoCompOvsSanctions250411Mapper.class);

    public YuwlMoCompOvsSanctions250411Repository() {
      super(yuwlMoCompOvsSanctions250411Mapper, YuwlMoCompOvsSanctions250411.class);
    }
}