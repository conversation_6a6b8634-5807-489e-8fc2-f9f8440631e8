/*
*  Copyright 2021-2031 基础数据
*/
package com.ld.clean.repository.manageglobalsysdb;

import com.ld.clean.context.ApplicationDaoContextManager;
import com.ld.clean.core.base.BaseRepository;
import com.ld.clean.mapper.manageglobalsysdb.SysDataDictVMapper;
import com.ld.clean.model.manageglobalsysdb.SysDataDictV;
import tk.mybatis.mapper.entity.Condition;

import java.util.List;

/**
* @website https://www.qcc.com
* <AUTHOR>
* @date 2023-12-24
**/
public class SysDataDictVRepository extends BaseRepository {
    private static final SysDataDictVMapper sysDataDictVMapper = ApplicationDaoContextManager.getInstance(SysDataDictVMapper.class);

    public SysDataDictVRepository() {
      super(sysDataDictVMapper, SysDataDictV.class);
    }
    public List<SysDataDictV> selectByCondition(Condition condition){
        condition.selectProperties("dbName","tbNameEn","tbNameCn","natrualKey","fieldnameEn","fieldnameCn","fieldType","fieldExtent","fieldUnit","enumValue","enumValuePara","enumCode","business","explainType");
        return super.selectByCondition(condition);
    }
}