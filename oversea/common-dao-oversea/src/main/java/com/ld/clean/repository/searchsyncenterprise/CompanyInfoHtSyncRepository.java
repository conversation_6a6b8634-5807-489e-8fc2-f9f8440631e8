/*
*  Copyright 2021-2031 基础数据
*/
package com.ld.clean.repository.searchsyncenterprise;

import com.ld.clean.model.searchsyncenterprise.CompanyInfoHtSync;
import com.ld.clean.mapper.searchsyncenterprise.CompanyInfoHtSyncMapper;
import com.ld.clean.context.ApplicationDaoContextManager;
import com.ld.clean.core.base.BaseRepository;

/**
* @website https://www.qcc.com
* <AUTHOR>
* @date 2023-05-23
**/
public class CompanyInfoHtSyncRepository extends  BaseRepository {
    private static final CompanyInfoHtSyncMapper  companyInfoHtSyncMapper = ApplicationDaoContextManager.getInstance(CompanyInfoHtSyncMapper.class);

    public CompanyInfoHtSyncRepository() {
      super(companyInfoHtSyncMapper, CompanyInfoHtSync.class);
    }
}