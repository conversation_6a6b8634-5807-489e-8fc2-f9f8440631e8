/*
 *  Copyright 2021-2031 基础数据
 */
package com.ld.clean.repository.testprodoverseadb;

import com.ld.clean.context.ApplicationDaoContextManager;
import com.ld.clean.core.base.BaseRepository;
import com.ld.clean.mapper.testprodoverseadb.TestCompOvsPurchasedinfoAliMapper;
import com.ld.clean.model.prodoverseadb.CompOvsPurchasedinfo;

/**
 * <AUTHOR>
 * @website https://www.qcc.com
 * @date 2022-06-28
 **/
public class TestCompOvsPurchasedinfoAliRepository extends BaseRepository {

    private static final TestCompOvsPurchasedinfoAliMapper TEMP_MAPPER = ApplicationDaoContextManager.getInstance(TestCompOvsPurchasedinfoAliMapper.class);

    public TestCompOvsPurchasedinfoAliRepository() {
        super(TEMP_MAPPER, CompOvsPurchasedinfo.class);
    }
}