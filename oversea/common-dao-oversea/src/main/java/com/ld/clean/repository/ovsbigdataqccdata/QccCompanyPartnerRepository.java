/*
*  Copyright 2021-2031 基础数据
*/
package com.ld.clean.repository.ovsbigdataqccdata;

import com.ld.clean.model.ovsbigdataqccdata.QccCompanyPartner;
import com.ld.clean.mapper.ovsbigdataqccdata.QccCompanyPartnerMapper;
import com.ld.clean.context.ApplicationDaoContextManager;
import com.ld.clean.core.base.BaseRepository;

/**
* @website https://www.qcc.com
* <AUTHOR>
* @date 2025-01-07
**/
public class QccCompanyPartnerRepository extends  BaseRepository {
    private static final QccCompanyPartnerMapper  qccCompanyPartnerMapper = ApplicationDaoContextManager.getInstance(QccCompanyPartnerMapper.class);

    public QccCompanyPartnerRepository() {
      super(qccCompanyPartnerMapper, QccCompanyPartner.class);
    }
}