/*
*  Copyright 2021-2031 基础数据
*/
package com.ld.clean.repository.basecleandatatmp;

import com.ld.clean.model.basecleandatatmp.YuwlMoCompOvsBasicinfo;
import com.ld.clean.mapper.basecleandatatmp.YuwlMoCompOvsBasicinfoMapper;
import com.ld.clean.context.ApplicationDaoContextManager;
import com.ld.clean.core.base.BaseRepository;
import com.ld.clean.model.manageoverseadb.CompOvsBasicinfo;
import tk.mybatis.mapper.entity.Example;

import java.util.List;

/**
* @website https://www.qcc.com
* <AUTHOR>
* @date 2024-12-09
**/
public class YuwlMoCompOvsBasicinfoRepository extends  BaseRepository {
    private static final YuwlMoCompOvsBasicinfoMapper  yuwlMoCompOvsBasicinfoMapper = ApplicationDaoContextManager.getInstance(YuwlMoCompOvsBasicinfoMapper.class);

    public YuwlMoCompOvsBasicinfoRepository() {
      super(yuwlMoCompOvsBasicinfoMapper, YuwlMoCompOvsBasicinfo.class);
    }
    public List<YuwlMoCompOvsBasicinfo> selectListByCompNoAndNationCode(String compNo, String nationCode) {
        Example example = new Example(YuwlMoCompOvsBasicinfo.class);
        example.and().andEqualTo("compNo", compNo).andEqualTo("nationCode", nationCode).andEqualTo("dataStatus", "1");
        return yuwlMoCompOvsBasicinfoMapper.selectByExample(example);
    }
    public List<YuwlMoCompOvsBasicinfo> selectListByCompNoAndNationAndRegPlaceCode(String compNo, String nationCode,String regPlaceCode) {
        Example example = new Example(YuwlMoCompOvsBasicinfo.class);
        example.and().andEqualTo("compNo", compNo).andEqualTo("nationCode", nationCode)
                .andEqualTo("regPlace", regPlaceCode)
                .andEqualTo("dataStatus", "1");
        return yuwlMoCompOvsBasicinfoMapper.selectByExample(example);
    }
}