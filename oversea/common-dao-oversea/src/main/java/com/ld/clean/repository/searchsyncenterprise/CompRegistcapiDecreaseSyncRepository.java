/*
 *  Copyright 2021-2031 基础数据
 */
package com.ld.clean.repository.searchsyncenterprise;

import com.ld.clean.context.ApplicationDaoContextManager;
import com.ld.clean.core.base.BaseRepository;
import com.ld.clean.mapper.searchsyncenterprise.CompRegistcapiDecreaseSyncMapper;
import com.ld.clean.model.searchsyncenterprise.CompRegistcapiDecreaseSync;

/**
 * <AUTHOR>
 * @website https://www.qcc.com
 * @date 2024-01-08
 **/
public class CompRegistcapiDecreaseSyncRepository extends BaseRepository<CompRegistcapiDecreaseSync> {
    private static final CompRegistcapiDecreaseSyncMapper compRegistcapiDecreaseSyncMapper = ApplicationDaoContextManager.getInstance(CompRegistcapiDecreaseSyncMapper.class);

    public CompRegistcapiDecreaseSyncRepository() {
        super(compRegistcapiDecreaseSyncMapper, CompRegistcapiDecreaseSync.class);
    }
}