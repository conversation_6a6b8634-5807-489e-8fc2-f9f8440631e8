/*
*  Copyright 2021-2031 基础数据
*/
package com.ld.clean.repository.searchoverseadb;

import com.ld.clean.context.ApplicationDaoContextManager;
import com.ld.clean.core.base.BaseRepository;
import com.ld.clean.mapper.searchoverseadb.CompanyInfoHtSyncMapper;
import com.ld.clean.model.searchoverseadb.CompanyInfoHtSync;

/**
* @website https://www.qcc.com
* <AUTHOR>
* @date 2023-04-17
**/
public class CompanyInfoHtSyncRepository extends  BaseRepository {
    private static final CompanyInfoHtSyncMapper companyInfoHtSyncMapper = ApplicationDaoContextManager.getInstance(CompanyInfoHtSyncMapper.class);

    public CompanyInfoHtSyncRepository() {
      super(companyInfoHtSyncMapper, CompanyInfoHtSync.class);
    }
}