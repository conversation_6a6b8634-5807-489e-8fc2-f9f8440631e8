/*
*  Copyright 2021-2031 基础数据
*/
package com.ld.clean.repository.manageoverseadb;

import com.ld.clean.model.manageoverseadb.CompOvsHktaxReport;
import com.ld.clean.mapper.manageoverseadb.CompOvsHktaxReportMapper;
import com.ld.clean.context.ApplicationDaoContextManager;
import com.ld.clean.core.base.BaseRepository;

/**
* @website https://www.qcc.com
* <AUTHOR>
* @date 2025-07-10
**/
public class CompOvsHktaxReportRepository extends  BaseRepository {
    private static final CompOvsHktaxReportMapper  compOvsHktaxReportMapper = ApplicationDaoContextManager.getInstance(CompOvsHktaxReportMapper.class);

    public CompOvsHktaxReportRepository() {
      super(compOvsHktaxReportMapper, CompOvsHktaxReport.class);
    }
}