/*
*  Copyright 2021-2031 基础数据
*/
package com.ld.clean.repository.basecleandatatmp;

import com.ld.clean.model.basecleandatatmp.YuwlPoOvsHkReportresultProd250731;
import com.ld.clean.mapper.basecleandatatmp.YuwlPoOvsHkReportresultProd250731Mapper;
import com.ld.clean.context.ApplicationDaoContextManager;
import com.ld.clean.core.base.BaseRepository;

/**
* @website https://www.qcc.com
* <AUTHOR>
* @date 2025-07-31
**/
public class YuwlPoOvsHkReportresultProd250731Repository extends  BaseRepository {
    private static final YuwlPoOvsHkReportresultProd250731Mapper  yuwlPoOvsHkReportresultProd250731Mapper = ApplicationDaoContextManager.getInstance(YuwlPoOvsHkReportresultProd250731Mapper.class);

    public YuwlPoOvsHkReportresultProd250731Repository() {
      super(yuwlPoOvsHkReportresultProd250731Mapper, YuwlPoOvsHkReportresultProd250731.class);
    }
}