/*
 *  Copyright 2021-2031 基础数据
 */
package com.ld.clean.repository.testprodoverseadb;

import com.ld.clean.context.ApplicationDaoContextManager;
import com.ld.clean.core.base.BaseRepository;
import com.ld.clean.mapper.testprodoverseadb.TestCompOvsOfficeholdersAliMapper;
import com.ld.clean.model.prodoverseadb.CompOvsOfficeholders;

/**
 * <AUTHOR>
 * @website https://www.qcc.com
 * @date 2022-09-19
 **/
public class TestCompOvsOfficeholdersAliRepository extends BaseRepository {

    private static final TestCompOvsOfficeholdersAliMapper TEMP_MAPPER = ApplicationDaoContextManager.getInstance(TestCompOvsOfficeholdersAliMapper.class);

    public TestCompOvsOfficeholdersAliRepository() {
        super(TEMP_MAPPER, CompOvsOfficeholders.class);
    }
}