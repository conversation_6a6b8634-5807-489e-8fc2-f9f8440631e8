<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>oversea</artifactId>
        <groupId>com.ld.clean</groupId>
        <version>1.0.0</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>common-dao-oversea</artifactId>
    <version>${oversea.dao.version}</version>

    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
    </properties>

    <dependencies>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <version>${org.projectlombok.version}</version>
        </dependency>
        <dependency>
            <artifactId>common-dao</artifactId>
            <groupId>com.ld.clean</groupId>
            <version>${common.dao.version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>qcc-base-ceph</artifactId>
                    <groupId>com.ld.clean</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.ld.clean</groupId>
            <artifactId>qcc-base-ceph</artifactId>
            <version>${ld.dbcore.version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>aliyun-sdk-oss</artifactId>
                    <groupId>com.aliyun.oss</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.github.houbb</groupId>
            <artifactId>opencc4j</artifactId>
            <version>1.8.1</version>
        </dependency>
        <dependency>
            <groupId>com.ld.clean</groupId>
            <artifactId>qcc-base-mongo</artifactId>
            <version>${ld.dbcore.version}</version>
        </dependency>
        <dependency>
            <groupId>com.ld.clean</groupId>
            <artifactId>common-dao-globalsys</artifactId>
            <version>${globalsys.dao.version}</version>
        </dependency>
    </dependencies>

</project>